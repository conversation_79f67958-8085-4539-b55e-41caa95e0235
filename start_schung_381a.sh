#!/bin/bash

# Lineage 381a 專用啟動腳本
# 路徑: /home/<USER>/381a
# 硬體: i7-6700K + 64GB RAM

echo "=========================================="
echo "Lineage 381a 伺服器啟動"
echo "路徑: /home/<USER>/381a"
echo "硬體: i7-6700K + 64GB RAM"
echo "=========================================="

# 切換到正確的工作目錄
cd /home/<USER>/381a

# 設置 Java 環境
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 檢查當前目錄
echo "當前工作目錄: $(pwd)"

# 檢查 Java 環境
if ! command -v java &> /dev/null; then
    echo "❌ 錯誤: Java 未安裝"
    echo "請執行: sudo apt install openjdk-8-jdk"
    exit 1
fi

echo "✅ Java 版本:"
java -version
echo

# 檢查項目文件結構
echo "檢查項目文件..."
if [ ! -d "src/main/java" ]; then
    echo "❌ 錯誤: 找不到源碼目錄 src/main/java"
    echo "當前目錄內容:"
    ls -la
    exit 1
fi

if [ ! -f "src/main/java/com/lineage/Server.java" ]; then
    echo "❌ 錯誤: 找不到主類源文件"
    echo "請檢查源碼是否完整"
    exit 1
fi

echo "✅ 源碼目錄存在"

# 檢查編譯狀態
if [ ! -f "out/production/Lineage381a/com/lineage/Server.class" ]; then
    echo "⚠️  主類未編譯，正在自動編譯..."
    
    # 檢查依賴文件
    echo "檢查依賴文件..."
    required_jars=(
        "jar/log4j-1.2.16.jar"
        "jar/commons-logging-1.2.jar"
        "jar/c3p0-0.9.5.5.jar"
        "jar/mchange-commons-java-0.2.19.jar"
        "jar/mariadb-java-client-3.1.4.jar"
        "jar/javolution-5.5.1.jar"
    )
    
    missing_jars=0
    for jar in "${required_jars[@]}"; do
        if [ -f "$jar" ]; then
            echo "✅ $(basename $jar)"
        else
            echo "❌ $(basename $jar) 缺失"
            missing_jars=$((missing_jars + 1))
        fi
    done
    
    if [ $missing_jars -gt 0 ]; then
        echo "❌ 錯誤: 缺少 $missing_jars 個依賴文件"
        echo "請確保所有 JAR 文件都在 /home/<USER>/381a/jar/ 目錄中"
        exit 1
    fi
    
    # 執行編譯
    echo "開始編譯..."
    mkdir -p out/production/Lineage381a
    
    # 構建 classpath
    CLASSPATH=""
    for jar in "${required_jars[@]}"; do
        if [ -z "$CLASSPATH" ]; then
            CLASSPATH="$jar"
        else
            CLASSPATH="$CLASSPATH:$jar"
        fi
    done
    
    # 編譯所有 Java 文件
    java_files=$(find src/main/java -name "*.java")
    javac -cp "$CLASSPATH" -d out/production/Lineage381a -encoding UTF-8 $java_files
    
    if [ $? -eq 0 ]; then
        echo "✅ 編譯成功"
        
        # 複製配置文件
        if [ -d "config" ]; then
            cp -r config out/production/Lineage381a/
            echo "✅ 配置文件已複製"
        fi
    else
        echo "❌ 編譯失敗"
        exit 1
    fi
else
    echo "✅ 主類已編譯"
fi

# 檢查配置文件
if [ ! -f "config/sql.properties" ]; then
    echo "❌ 錯誤: 找不到資料庫配置文件"
    echo "請確保 /home/<USER>/381a/config/sql.properties 存在"
    exit 1
fi

echo "✅ 配置文件存在"

# 設置 Classpath (Ubuntu 使用冒號分隔)
CLASSPATH="out/production/Lineage381a:jar/log4j-1.2.16.jar:jar/commons-logging-1.2.jar:jar/c3p0-0.9.5.5.jar:jar/mchange-commons-java-0.2.19.jar:jar/mariadb-java-client-3.1.4.jar:jar/javolution-5.5.1.jar"

# 創建日誌目錄
mkdir -p /home/<USER>/381a/logs

# 設置 JVM 參數 (針對 64GB RAM 優化)
JVM_OPTS="-Xms16g -Xmx32g"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=100"
JVM_OPTS="$JVM_OPTS -XX:G1HeapRegionSize=32m"
JVM_OPTS="$JVM_OPTS -XX:G1NewSizePercent=20"
JVM_OPTS="$JVM_OPTS -XX:G1MaxNewSizePercent=30"
JVM_OPTS="$JVM_OPTS -XX:+UnlockExperimentalVMOptions"
JVM_OPTS="$JVM_OPTS -XX:+UseStringDeduplication"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Taipei"
JVM_OPTS="$JVM_OPTS -Djava.net.preferIPv4Stack=true"
JVM_OPTS="$JVM_OPTS -Xloggc:/home/<USER>/381a/logs/gc.log"

echo "JVM 記憶體配置: 16GB-32GB (針對 64GB RAM 優化)"
echo "日誌目錄: /home/<USER>/381a/logs"
echo

echo "=========================================="
echo "啟動 Lineage 381a 伺服器..."
echo "=========================================="

# 啟動伺服器
java $JVM_OPTS -cp "$CLASSPATH" com.lineage.Server

# 檢查退出狀態
exit_code=$?
echo
echo "=========================================="
if [ $exit_code -eq 0 ]; then
    echo "✅ 伺服器正常關閉"
else
    echo "❌ 伺服器異常退出，退出碼: $exit_code"
    echo "請檢查日誌: /home/<USER>/381a/logs/"
fi
echo "=========================================="
