#!/bin/bash

# 最小化測試啟動腳本
# 用於診斷具體的啟動問題

echo "=========================================="
echo "最小化測試啟動"
echo "用於診斷啟動問題"
echo "=========================================="

cd /home/<USER>/381a

# 設置 Java 環境
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 檢查 Java
if ! command -v java &> /dev/null; then
    echo "❌ Java 未安裝"
    exit 1
fi

echo "✅ Java 版本:"
java -version
echo

# 尋找 JAR 文件
JAR_FILES=(
    "Lineage381a.jar"
    "Lineage381a_Ubuntu.jar"
    "lineage381a.jar"
    "server.jar"
)

JAR_FILE=""
for jar in "${JAR_FILES[@]}"; do
    if [ -f "$jar" ]; then
        JAR_FILE="$jar"
        break
    fi
done

if [ -z "$JAR_FILE" ]; then
    echo "❌ 找不到 JAR 文件"
    exit 1
fi

echo "✅ 使用 JAR 文件: $JAR_FILE"

# 創建日誌目錄
mkdir -p logs

# 最小化 JVM 參數
JVM_OPTS="-Xms512m -Xmx1g"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Taipei"
JVM_OPTS="$JVM_OPTS -Djava.awt.headless=true"
JVM_OPTS="$JVM_OPTS -Dlog4j.configuration=file:config/log4j.properties"

echo "最小化 JVM 參數: $JVM_OPTS"
echo

# 檢查配置文件
echo "檢查配置文件..."
if [ -f "config/sql.properties" ]; then
    echo "✅ sql.properties 存在"
else
    echo "❌ sql.properties 不存在"
    echo "請執行: ./fix_config_files.sh"
    exit 1
fi

# 檢查資料庫服務
echo "檢查資料庫服務..."
if systemctl is-active --quiet mariadb; then
    echo "✅ MariaDB 運行中"
else
    echo "❌ MariaDB 未運行"
    echo "請執行: sudo systemctl start mariadb"
    exit 1
fi

# 檢查端口
echo "檢查端口..."
if netstat -tlnp 2>/dev/null | grep -q ":2000"; then
    echo "❌ 端口 2000 被占用"
    echo "占用進程:"
    netstat -tlnp 2>/dev/null | grep ":2000"
    echo "請終止占用進程或更改端口"
    exit 1
else
    echo "✅ 端口 2000 可用"
fi

echo
echo "=========================================="
echo "開始最小化測試啟動..."
echo "=========================================="
echo "如果出現錯誤，請仔細查看錯誤信息"
echo "按 Ctrl+C 可以停止"
echo "----------------------------------------"

# 啟動並捕獲所有輸出
java $JVM_OPTS -jar "$JAR_FILE" 2>&1 | tee logs/minimal_test.log

exit_code=$?

echo
echo "----------------------------------------"
echo "測試結束，退出碼: $exit_code"

# 分析退出碼和日誌
if [ $exit_code -eq 0 ]; then
    echo "✅ 正常退出"
elif [ $exit_code -eq 1 ]; then
    echo "❌ 異常退出 (退出碼 1)"
    echo
    echo "分析錯誤原因..."
    
    if [ -f "logs/minimal_test.log" ]; then
        echo "檢查日誌中的關鍵錯誤:"
        
        # 檢查常見錯誤
        if grep -q "ClassNotFoundException" logs/minimal_test.log; then
            echo "❌ ClassNotFoundException - 缺少類文件"
            grep "ClassNotFoundException" logs/minimal_test.log | tail -3
        fi
        
        if grep -q "SQLException" logs/minimal_test.log; then
            echo "❌ SQLException - 資料庫連接問題"
            grep "SQLException" logs/minimal_test.log | tail -3
        fi
        
        if grep -q "BindException" logs/minimal_test.log; then
            echo "❌ BindException - 端口被占用"
            grep "BindException" logs/minimal_test.log | tail -3
        fi
        
        if grep -q "FileNotFoundException" logs/minimal_test.log; then
            echo "❌ FileNotFoundException - 配置文件問題"
            grep "FileNotFoundException" logs/minimal_test.log | tail -3
        fi
        
        if grep -q "Exception" logs/minimal_test.log; then
            echo "❌ 其他異常:"
            grep "Exception" logs/minimal_test.log | tail -5
        fi
        
        echo
        echo "完整錯誤日誌的最後 20 行:"
        tail -20 logs/minimal_test.log
    fi
else
    echo "❌ 其他錯誤 (退出碼: $exit_code)"
fi

echo
echo "=========================================="
echo "測試完成"
echo "=========================================="
echo "日誌文件: logs/minimal_test.log"
echo
echo "根據錯誤信息的建議解決方案:"
echo "1. ClassNotFoundException: 重新構建 JAR 文件"
echo "2. SQLException: 檢查資料庫配置和連接"
echo "3. BindException: 檢查端口占用"
echo "4. FileNotFoundException: 檢查配置文件路徑"
echo "5. 其他問題: 查看完整日誌文件"
