#!/bin/bash

# 測試 JAR 文件的腳本
# 檢查 JAR 文件是否可以正常啟動

echo "=========================================="
echo "JAR 文件測試工具"
echo "路徑: /home/<USER>/381a"
echo "=========================================="

cd /home/<USER>/381a

# 設置 Java 環境
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 檢查 Java
if ! command -v java &> /dev/null; then
    echo "❌ Java 未安裝"
    exit 1
fi

echo "✅ Java 版本:"
java -version
echo

# 尋找 JAR 文件
JAR_FILES=(
    "Lineage381a.jar"
    "Lineage381a_Ubuntu.jar"
    "lineage381a.jar"
    "server.jar"
)

JAR_FILE=""
for jar in "${JAR_FILES[@]}"; do
    if [ -f "$jar" ]; then
        JAR_FILE="$jar"
        break
    fi
done

if [ -z "$JAR_FILE" ]; then
    echo "❌ 找不到 JAR 文件"
    exit 1
fi

echo "✅ 測試 JAR 文件: $JAR_FILE"

# 檢查 JAR 文件內容
echo
echo "=== JAR 文件內容檢查 ==="
echo "檢查 MANIFEST.MF:"
jar xf "$JAR_FILE" META-INF/MANIFEST.MF 2>/dev/null
if [ -f "META-INF/MANIFEST.MF" ]; then
    cat META-INF/MANIFEST.MF
    rm -rf META-INF
else
    echo "❌ MANIFEST.MF 不存在"
fi

echo
echo "檢查主類:"
if jar tf "$JAR_FILE" | grep -q "com/lineage/Server.class"; then
    echo "✅ 主類 com.lineage.Server 存在"
else
    echo "❌ 主類 com.lineage.Server 不存在"
    echo "JAR 中的類文件:"
    jar tf "$JAR_FILE" | grep "\.class$" | head -10
fi

echo
echo "檢查依賴庫:"
dependencies=(
    "org/apache/log4j"
    "org/apache/commons/logging"
    "com/mchange/v2/c3p0"
    "org/mariadb/jdbc"
    "javolution"
)

for dep in "${dependencies[@]}"; do
    if jar tf "$JAR_FILE" | grep -q "$dep"; then
        echo "✅ $dep"
    else
        echo "❌ $dep (缺失)"
    fi
done

# 測試 JAR 文件啟動
echo
echo "=== JAR 啟動測試 ==="
echo "測試 JAR 文件是否可以啟動..."
echo "注意: 這只是測試啟動，可能會因為配置問題而失敗"
echo

# 使用較小的記憶體進行測試
timeout 10s java -Xms1g -Xmx2g -jar "$JAR_FILE" --test 2>&1 | head -20

echo
echo "=== 測試完成 ==="
echo "如果看到 'Welcome to Lineage' 或類似訊息，表示 JAR 文件正常"
echo "如果看到 'Main-Class' 錯誤，請執行: ./fix_jar_manifest.sh"
echo
echo "完整啟動請使用: ./run_jar_ubuntu.sh"
