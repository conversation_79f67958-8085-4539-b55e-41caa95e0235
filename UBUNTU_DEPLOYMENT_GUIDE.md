# 🐧 Lineage 381a Ubuntu 24.02 部署指南

## 硬體配置
- **CPU**: i7-6700K (4核8線程)
- **記憶體**: 64GB DDR4 🔥
- **儲存**: 1TB PCIe SSD
- **網路**: 雙2.5G網卡 + 1G外線
- **系統**: Ubuntu 24.02

## 🚀 完整部署流程

### 步驟 1: 準備 Ubuntu 環境

#### 1.1 更新系統
```bash
sudo apt update && sudo apt upgrade -y
```

#### 1.2 安裝必要軟體
```bash
# 安裝 Java 開發環境
sudo apt install -y openjdk-8-jdk

# 安裝 MariaDB
sudo apt install -y mariadb-server mariadb-client

# 安裝開發工具
sudo apt install -y build-essential git wget curl
```

#### 1.3 配置 Java 環境
```bash
# 設置 JAVA_HOME
echo 'export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64' >> ~/.bashrc
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 驗證 Java 安裝
java -version
javac -version
```

### 步驟 2: 傳輸項目文件到 Ubuntu

#### 2.1 從 Windows 傳輸到 Ubuntu
```bash
# 方法 1: 使用 scp (從 Windows)
scp -r D:\IntelliJ-workspace\Lineage381a username@ubuntu-server:/home/<USER>/

# 方法 2: 使用 rsync (從 Windows WSL 或 Git Bash)
rsync -avz /mnt/d/IntelliJ-workspace/Lineage381a/ username@ubuntu-server:/home/<USER>/Lineage381a/

# 方法 3: 使用 Git (推薦)
# 在 Windows 上推送到 Git 倉庫，然後在 Ubuntu 上拉取
git clone your-repository-url
```

#### 2.2 設置文件權限
```bash
cd ~/Lineage381a
chmod +x *.sh
chmod 644 *.md
chmod 644 config/*
chmod 644 jar/*
```

### 步驟 3: 編譯項目

#### 3.1 執行編譯腳本
```bash
# 執行 Ubuntu 編譯腳本
./compile_ubuntu.sh
```

#### 3.2 驗證編譯結果
```bash
# 檢查主類是否編譯成功
ls -la out/production/Lineage381a/com/lineage/Server.class

# 檢查編譯統計
find out/production/Lineage381a -name "*.class" | wc -l
```

### 步驟 4: 配置 MariaDB

#### 4.1 安全配置
```bash
sudo mysql_secure_installation
```

#### 4.2 創建資料庫和用戶
```bash
sudo mysql -u root -p
```

```sql
-- 創建資料庫
CREATE DATABASE 381 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 創建用戶
CREATE USER 'lineage'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON 381.* TO 'lineage'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### 4.3 應用 MariaDB 優化配置
```bash
# 複製優化配置
sudo cp mariadb_optimization.cnf /etc/mysql/mariadb.conf.d/99-lineage.cnf

# 重啟 MariaDB
sudo systemctl restart mariadb

# 檢查狀態
sudo systemctl status mariadb
```

#### 4.4 導入資料庫結構和數據
```bash
# 導入資料庫結構 (假設您有 SQL 文件)
mysql -u lineage -p 381 < database_structure.sql

# 執行優化腳本
mysql -u lineage -p 381 < sql/database_optimization.sql
mysql -u lineage -p 381 < sql/add_connpool_command.sql
```

### 步驟 5: 系統優化

#### 5.1 執行 64GB RAM 系統優化
```bash
sudo ./ubuntu_optimization_6700k_64gb.sh
```

#### 5.2 重啟系統
```bash
sudo reboot
```

### 步驟 6: 啟動伺服器

#### 6.1 啟動伺服器
```bash
cd ~/Lineage381a
./start_server.sh
```

#### 6.2 檢查啟動狀態
```bash
# 檢查進程
ps aux | grep java

# 檢查端口
netstat -tlnp | grep java

# 檢查日誌
tail -f logs/server.log
```

### 步驟 7: 監控和管理

#### 7.1 系統監控
```bash
# 實時監控
./monitor_server.sh watch

# 單次檢查
./monitor_server.sh status
```

#### 7.2 服務管理 (可選)
```bash
# 安裝為系統服務
sudo cp lineage.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable lineage
sudo systemctl start lineage

# 查看服務狀態
sudo systemctl status lineage
sudo journalctl -u lineage -f
```

## 🔧 故障排除

### 編譯問題

#### 問題: 找不到 Java 編譯器
```bash
# 解決方案
sudo apt install openjdk-8-jdk
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
```

#### 問題: 編譯錯誤
```bash
# 檢查編碼問題
file -i src/main/java/com/lineage/Server.java

# 如果編碼不是 UTF-8，轉換編碼
iconv -f GBK -t UTF-8 src/main/java/com/lineage/Server.java > temp.java
mv temp.java src/main/java/com/lineage/Server.java
```

### 運行問題

#### 問題: 找不到主類
```bash
# 檢查編譯狀態
ls -la out/production/Lineage381a/com/lineage/Server.class

# 重新編譯
./compile_ubuntu.sh
```

#### 問題: 資料庫連接失敗
```bash
# 檢查 MariaDB 狀態
sudo systemctl status mariadb

# 檢查配置文件
cat config/sql.properties

# 測試連接
mysql -u lineage -p 381 -e "SELECT 1;"
```

#### 問題: 記憶體不足
```bash
# 檢查記憶體使用
free -h

# 調整 JVM 參數 (如果需要)
# 編輯 start_server.sh 中的 JVM_OPTS
```

## 📊 效能驗證

### 系統資源檢查
```bash
# CPU 使用率
top -p $(pgrep java)

# 記憶體使用
free -h
ps aux --sort=-%mem | head

# 磁碟 I/O
iotop -p $(pgrep java)

# 網路連接
netstat -an | grep :2000 | wc -l
```

### 資料庫效能檢查
```bash
# 連接數
mysql -u lineage -p -e "SHOW STATUS LIKE 'Threads_connected';"

# 查詢效能
mysql -u lineage -p -e "SHOW STATUS LIKE 'Slow_queries';"

# 緩衝池使用率
mysql -u lineage -p -e "SHOW STATUS LIKE 'Innodb_buffer_pool%';"
```

## 🎯 預期效能 (64GB RAM)

### 系統指標
- **JVM 記憶體**: 16-32GB
- **資料庫緩衝**: 24GB
- **同時在線**: 1000-1500 玩家
- **查詢響應**: < 30ms
- **記憶體使用率**: < 70%

### 監控閾值
- **CPU 使用率**: < 80%
- **記憶體使用率**: < 80%
- **連接池使用率**: < 70%
- **磁碟使用率**: < 80%

## 📝 維護建議

### 日常維護
```bash
# 檢查日誌
tail -f logs/server.log

# 檢查系統資源
./monitor_server.sh

# 檢查資料庫
mysql -u lineage -p -e "SHOW PROCESSLIST;"
```

### 定期維護
```bash
# 清理日誌 (每週)
find logs/ -name "*.log" -mtime +7 -delete

# 優化資料庫 (每月)
mysql -u lineage -p 381 -e "OPTIMIZE TABLE characters, character_items;"

# 系統更新 (每月)
sudo apt update && sudo apt upgrade
```

---

## 🎉 部署完成！

恭喜！您的 Lineage 381a 伺服器現在已經在 Ubuntu 24.02 上成功部署並優化。

**預期效能**: 支援 1000-1500 同時在線玩家的企業級私服！🚀
