package com.add.Crack;

import java.util.Timer;
import java.util.TimerTask;
import com.lineage.server.model.Instance.L1MonsterInstance;
import com.lineage.server.utils.L1SpawnUtil;
import com.lineage.server.serverpackets.S_ServerMessage;
import java.util.Iterator;
import com.lineage.server.model.L1Object;
import com.add.L1Config_other;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.model.L1Teleport;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.world.World;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.ArrayList;
import java.util.logging.Logger;

public class L1Thebes {
	private static Logger _log;
	public static final int STATUS_NONE = 0;
	public static final int STATUS_READY = 1;
	public static final int STATUS_PLAYING = 2;
	public static final int STATUS_AGAIN = 3;
	public static final int STATUS_END = 4;
	private static ArrayList<L1PcInstance> playerList;
	public static int _status;
	private static final int END_STATUS_WINNER = 1;
	private static final int END_STATUS_NOWINNER = 2;
	public static int _type;

	static {
		_log = Logger.getLogger(L1Thebes.class.getName());
		playerList = new ArrayList<L1PcInstance>();
		_status = 0;
		_type = 0;
	}

	public static void enterGame(final L1PcInstance pc, final int npcid) {
		final L1Object obj = World.get().findObject(npcid);
		final L1NpcInstance npc = (L1NpcInstance) obj;
		if (!L1CrackTime.getStart().getGateOpen()) {
			pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "thebegate2"));
			return;
		}
		if (!pc.getInventory().checkItem(49242, 1L)) {
			pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "thebegate3"));
			return;
		}
		if (getGameStatus() == 2) {
			if (L1Thebes.playerList.contains(pc)) {
				L1Teleport.teleport(pc, 32734, 32832, (short) 782, 2, true);
			} else {
				pc.sendPackets(new S_SystemMessage("副本已開始，無法進入。"));
			}
			return;
		}
		if (getGameStatus() == 3) {
			pc.sendPackets(new S_SystemMessage("副本正在重置中。"));
			return;
		}
		if (getGameStatus() == 4) {
			pc.sendPackets(new S_SystemMessage("BOSS已死亡，無法進入。"));
			return;
		}
		if (L1Thebes.playerList.size() >= L1Config_other._2213) {
			pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "thebegate4"));
			return;
		}
		if (getGameStatus() == 0) {
			L1Teleport.teleport(pc, 32734, 32832, (short) 782, 2, true);
			pc.getInventory().consumeItem(49242, 1L);
			addPlayerList(pc);
		}
		if (getGameStatus() == 1 && L1Thebes.playerList.size() < L1Config_other._2213) {
			L1Teleport.teleport(pc, 32734, 32832, (short) 782, 2, true);
			pc.getInventory().consumeItem(49242, 1L);
			addPlayerList(pc);
		}
	}

	private static void addPlayerList(final L1PcInstance pc) {
		if (!L1Thebes.playerList.contains(pc)) {
			L1Thebes.playerList.add(pc);
		}
		if (L1Thebes.playerList.size() == L1Config_other._2211) {
			if (getGameStatus() == 0) {
				setGameStatus(1);
				final Iterator<L1Object> iterator = World.get().getVisibleObjects(782).values().iterator();
				while (iterator.hasNext()) {
					final Object obj = iterator.next();
					if (obj instanceof L1PcInstance) {
						final L1PcInstance tgpc = (L1PcInstance) obj;
						tgpc.sendPackets(new S_SystemMessage("時空裂痕副本將在5秒後開始。"));
					}
				}
				OverTime(5);
			}
		} else if (L1Thebes.playerList.size() < L1Config_other._2211) {
			pc.sendPackets(new S_SystemMessage("需要有至少 " + L1Config_other._2211 + " 人以上參加者才可能開始副本。"));
		}
	}

	public static void removePlayerList(final L1PcInstance pc) {
		if (L1Thebes.playerList.contains(pc)) {
			L1Thebes.playerList.remove(pc);
			L1Teleport.teleport(pc, 33442, 32797, (short) 4, 5, true);
		}
	}

	public static void setGameStatus(final int i) {
		L1Thebes._status = i;
	}

	private static int getGameStatus() {
		return L1Thebes._status;
	}

	private static void setGameEnd(final int type) {
		switch (type) {
		case 1: {
			sendEndMessage(type);
			break;
		}
		case 2: {
			sendEndMessage(type);
			break;
		}
		}
		L1Thebes._type = type;
	}

	public static int getGameEnd() {
		return L1Thebes._type;
	}

	private static void startAgain() {
		new Again().begin();
	}

	private static void startAgain2() {
		new Again2().begin();
	}

	private static void sendEndMessage(final int type) {
		if (type == 1) {
			final Iterator<L1PcInstance> iterator = L1Thebes.playerList.iterator();
			while (iterator.hasNext()) {
				final L1PcInstance pc = iterator.next();
				pc.sendPackets(new S_ServerMessage(1474));
			}
		} else if (type == 2) {
			final Iterator<L1PcInstance> iterator2 = L1Thebes.playerList.iterator();
			while (iterator2.hasNext()) {
				final L1PcInstance pc = iterator2.next();
				pc.sendPackets(new S_ServerMessage(1479));
			}
		}
		Overtime2(type);
	}

	private static void Overtime2(final int type) {
		int time = 40;
		try {
			while (time >= 0) {
				Thread.sleep(1000L);
				final Iterator<L1PcInstance> iterator = L1Thebes.playerList.iterator();
				while (iterator.hasNext()) {
					final L1PcInstance pc = iterator.next();
					if (time == 35 && type == 1) {
						pc.sendPackets(new S_ServerMessage(1475));
					} else if (time == 30) {
						pc.sendPackets(new S_ServerMessage(1476));
					} else if (time == 20) {
						pc.sendPackets(new S_ServerMessage(1477));
					} else if (time == 10) {
						pc.sendPackets(new S_ServerMessage(1478));
					} else if (time == 5) {
						pc.sendPackets(new S_ServerMessage(1480));
					} else if (time == 4) {
						pc.sendPackets(new S_ServerMessage(1481));
					} else if (time == 3) {
						pc.sendPackets(new S_ServerMessage(1482));
					} else if (time == 2) {
						pc.sendPackets(new S_ServerMessage(1483));
					} else if (time == 1) {
						pc.sendPackets(new S_ServerMessage(1484));
					} else {
						if (time != 0 || type != 2) {
							continue;
						}
						setGameStatus(3);
						L1Thebes.playerList.clear();
						startAgain();
					}
				}
				--time;
			}
		} catch (Exception e) {
			L1Thebes._log.warning(e.getMessage());
		}
	}

	public static void OverTime(int time) {
		try {
			while (time >= 0) {
				Thread.sleep(1000L);
				--time;
				final Iterator<L1PcInstance> iterator = L1Thebes.playerList.iterator();
				while (iterator.hasNext()) {
					final L1PcInstance pc = iterator.next();
					if (time == 4) {
						pc.sendPackets(new S_ServerMessage(1471));
					} else if (time == 3) {
						pc.sendPackets(new S_ServerMessage(1472));
					} else {
						if (time != 2) {
							continue;
						}
						pc.sendPackets(new S_ServerMessage(1473));
					}
				}
				if (time == 1) {
					L1SpawnUtil.spawn(46124, 32781, 32826, (short) 782, 0);
					L1SpawnUtil.spawn(46123, 32781, 32837, (short) 782, 0);
					startSeekBossSecond();
				}
			}
		} catch (Exception e) {
			L1Thebes._log.warning(e.getMessage());
		}
	}

	private static void startSeekBossSecond() {
		new SeekBossSecond().begin();
	}

	private static int MobCount(final short mapId) {
		int MobCount = 0;
		final Iterator<L1Object> iterator = World.get().getVisibleObjects(mapId).values().iterator();
		while (iterator.hasNext()) {
			final Object obj = iterator.next();
			if (obj instanceof L1MonsterInstance) {
				final L1MonsterInstance mob = (L1MonsterInstance) obj;
				if (mob == null || mob.isDead()) {
					continue;
				}
				++MobCount;
			}
		}
		return MobCount;
	}

	private static int PcCount(final short mapId) {
		int PcCount = 0;
		final Iterator<L1Object> iterator = World.get().getVisibleObjects(mapId).values().iterator();
		while (iterator.hasNext()) {
			final Object obj = iterator.next();
			if (obj instanceof L1PcInstance) {
				final L1PcInstance pc = (L1PcInstance) obj;
				if (pc == null || pc.isDead()) {
					continue;
				}
				++PcCount;
			}
		}
		return PcCount;
	}

	private static class Again extends TimerTask {
		@Override
		public void run() {
			L1Thebes.setGameStatus(0);
			final Iterator<L1Object> iterator = World.get().getVisibleObjects(782).values().iterator();
			while (iterator.hasNext()) {
				final Object obj = iterator.next();
				if (obj instanceof L1NpcInstance) {
					final L1NpcInstance npc = (L1NpcInstance) obj;
					npc.deleteMe();
				}
			}
			this.cancel();
		}

		public void begin() {
			final Timer timer = new Timer();
			timer.schedule(this, L1Config_other._2212 * 60 * 1000);
		}
	}

	private static class Again2 extends TimerTask {
		@Override
		public void run() {
			L1Thebes.setGameStatus(0);
			final Iterator<L1Object> iterator = World.get().getVisibleObjects(782).values().iterator();
			while (iterator.hasNext()) {
				final Object obj = iterator.next();
				if (obj instanceof L1NpcInstance) {
					final L1NpcInstance npc = (L1NpcInstance) obj;
					npc.deleteMe();
				}
			}
			this.cancel();
		}

		public void begin() {
			final Timer timer = new Timer();
			timer.schedule(this, L1Config_other._2218 * 60 * 1000);
		}
	}

	private static class SeekBossSecond extends TimerTask {
		@Override
		public void run() {
			if (MobCount((short) 782) == 0) {
				setGameEnd(1);
			} else if (PcCount((short) 782) == 0) {
				setGameEnd(2);
			} else {
				startSeekBossSecond();
			}
			this.cancel();
		}

		public void begin() {
			final Timer timer = new Timer();
			timer.schedule(this, 5000L);
		}
	}
}
