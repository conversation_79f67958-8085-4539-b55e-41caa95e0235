<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.7" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <NonVisualComponents>
    <Container class="javax.swing.JFrame" name="F_Player">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" max="-2" attributes="0">
                  <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="18" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" attributes="0">
                          <Component id="L_Account" min="-2" max="-2" attributes="0"/>
                          <EmptySpace max="-2" attributes="0"/>
                          <Component id="TF_Account" min="-2" pref="108" max="-2" attributes="0"/>
                      </Group>
                      <Group type="102" attributes="0">
                          <Group type="103" groupAlignment="0" max="-2" attributes="0">
                              <Component id="L_Name" min="-2" max="-2" attributes="0"/>
                              <Component id="L_Title" min="-2" max="-2" attributes="0"/>
                              <Component id="L_Leavl" min="-2" max="-2" attributes="0"/>
                              <Component id="L_AccessLevel" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="L_Clan" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="L_Exp" alignment="1" pref="27" max="32767" attributes="1"/>
                              <Component id="L_Hp" alignment="1" min="-2" max="-2" attributes="0"/>
                              <Component id="L_Mp" alignment="1" min="-2" max="-2" attributes="0"/>
                              <Component id="L_Mp1" alignment="1" min="-2" max="-2" attributes="1"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="0" max="-2" attributes="0">
                              <Component id="TF_Mp" alignment="0" pref="108" max="32767" attributes="1"/>
                              <Component id="TF_Sex" alignment="0" pref="108" max="32767" attributes="1"/>
                              <Component id="TF_Hp" alignment="0" pref="108" max="32767" attributes="1"/>
                              <Component id="TF_Exp" alignment="0" pref="108" max="32767" attributes="1"/>
                              <Component id="TF_Clan" alignment="0" pref="108" max="32767" attributes="1"/>
                              <Component id="TF_AccessLevel" alignment="0" pref="108" max="32767" attributes="1"/>
                              <Component id="TF_Level" alignment="0" pref="108" max="32767" attributes="1"/>
                              <Component id="TF_Title" alignment="0" pref="108" max="32767" attributes="1"/>
                              <Component id="TF_Name" alignment="0" pref="108" max="32767" attributes="1"/>
                              <Component id="CB_Item" alignment="0" max="32767" attributes="1"/>
                          </Group>
                      </Group>
                  </Group>
                  <EmptySpace max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="103" groupAlignment="1" attributes="0">
                          <Group type="102" attributes="0">
                              <Group type="103" groupAlignment="1" attributes="0">
                                  <Component id="L_Int" alignment="1" min="-2" max="-2" attributes="0"/>
                                  <Component id="L_Wis" alignment="1" min="-2" max="-2" attributes="0"/>
                                  <Component id="L_Dex" alignment="1" min="-2" max="-2" attributes="0"/>
                                  <Component id="L_Cha" min="-2" max="-2" attributes="0"/>
                                  <Component id="L_AccessLevel7" min="-2" max="-2" attributes="0"/>
                                  <Component id="L_Con" min="-2" max="-2" attributes="0"/>
                                  <Component id="L_Str" min="-2" max="-2" attributes="0"/>
                                  <Component id="L_Map" min="-2" max="-2" attributes="0"/>
                                  <Component id="L_X" min="-2" max="-2" attributes="0"/>
                              </Group>
                              <EmptySpace max="-2" attributes="0"/>
                              <Group type="103" groupAlignment="0" attributes="0">
                                  <Component id="TF_Str" min="-2" pref="108" max="-2" attributes="0"/>
                                  <Component id="TF_Con" min="-2" pref="108" max="-2" attributes="0"/>
                                  <Component id="TF_Dex" min="-2" pref="108" max="-2" attributes="0"/>
                                  <Component id="TF_Wis" min="-2" pref="108" max="-2" attributes="0"/>
                                  <Component id="TF_Int" min="-2" pref="108" max="-2" attributes="0"/>
                                  <Component id="TF_Cha" min="-2" pref="108" max="-2" attributes="0"/>
                                  <Component id="TF_Ac" min="-2" pref="108" max="-2" attributes="0"/>
                                  <Component id="TF_Map" min="-2" pref="108" max="-2" attributes="0"/>
                                  <Component id="TF_X" min="-2" pref="108" max="-2" attributes="0"/>
                              </Group>
                          </Group>
                          <Group type="102" alignment="1" attributes="0">
                              <Component id="L_Y" min="-2" max="-2" attributes="0"/>
                              <EmptySpace max="-2" attributes="0"/>
                              <Component id="TF_Y" min="-2" pref="108" max="-2" attributes="0"/>
                          </Group>
                      </Group>
                      <Component id="B_Item" alignment="0" min="-2" max="-2" attributes="1"/>
                  </Group>
                  <EmptySpace pref="52" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" attributes="0">
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" alignment="0" attributes="0">
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Account" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Account" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Name" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Name" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace min="5" pref="5" max="5" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Title" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Title" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Leavl" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Level" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace min="5" pref="5" max="5" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_AccessLevel" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_AccessLevel" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Clan" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Clan" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Exp" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Exp" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Hp" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Hp" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Mp" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Mp" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Mp1" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Sex" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                              <Component id="L_Y" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Y" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                      </Group>
                      <Group type="102" alignment="0" attributes="0">
                          <EmptySpace min="-2" pref="26" max="-2" attributes="0"/>
                          <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <Group type="102" alignment="0" attributes="0">
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Str" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Str" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Con" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Con" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace min="-2" pref="5" max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Dex" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Dex" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace min="-2" pref="5" max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Wis" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Wis" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace min="-2" pref="5" max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Int" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Int" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Cha" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Cha" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_AccessLevel7" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Ac" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_Map" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_Map" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="3" attributes="0">
                              <Component id="L_X" alignment="3" min="-2" max="-2" attributes="0"/>
                              <Component id="TF_X" alignment="3" min="-2" pref="18" max="-2" attributes="0"/>
                          </Group>
                      </Group>
                  </Group>
                  <EmptySpace max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="CB_Item" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="B_Item" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace pref="27" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="L_Name">
          <Properties>
            <Property name="text" type="java.lang.String" value="�W�r:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Title">
          <Properties>
            <Property name="text" type="java.lang.String" value="�ٸ�:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Account">
          <Properties>
            <Property name="text" type="java.lang.String" value="�b��:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Leavl">
          <Properties>
            <Property name="text" type="java.lang.String" value="����:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_AccessLevel">
          <Properties>
            <Property name="text" type="java.lang.String" value="�v��:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Exp">
          <Properties>
            <Property name="text" type="java.lang.String" value=" Exp:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Hp">
          <Properties>
            <Property name="text" type="java.lang.String" value="Hp:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Mp">
          <Properties>
            <Property name="text" type="java.lang.String" value="Mp:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Int">
          <Properties>
            <Property name="text" type="java.lang.String" value="���O:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Str">
          <Properties>
            <Property name="text" type="java.lang.String" value="�O�q:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Con">
          <Properties>
            <Property name="text" type="java.lang.String" value="���:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Dex">
          <Properties>
            <Property name="text" type="java.lang.String" value="�ӱ�:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Wis">
          <Properties>
            <Property name="text" type="java.lang.String" value="�믫:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Cha">
          <Properties>
            <Property name="text" type="java.lang.String" value="�y�O:"/>
          </Properties>
        </Component>
        <Container class="javax.swing.JPanel" name="jPanel1">

          <Layout>
            <DimensionLayout dim="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="L_Image" pref="108" max="32767" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                  </Group>
              </Group>
            </DimensionLayout>
            <DimensionLayout dim="1">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="L_Image" pref="180" max="32767" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                  </Group>
              </Group>
            </DimensionLayout>
          </Layout>
          <SubComponents>
            <Component class="javax.swing.JLabel" name="L_Image">
            </Component>
          </SubComponents>
        </Container>
        <Component class="javax.swing.JLabel" name="L_Clan">
          <Properties>
            <Property name="text" type="java.lang.String" value="���:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_AccessLevel7">
          <Properties>
            <Property name="text" type="java.lang.String" value="���m�O:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Mp1">
          <Properties>
            <Property name="text" type="java.lang.String" value="�ʧO:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Map">
          <Properties>
            <Property name="text" type="java.lang.String" value="Map:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_X">
          <Properties>
            <Property name="text" type="java.lang.String" value="X:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JLabel" name="L_Y">
          <Properties>
            <Property name="text" type="java.lang.String" value="Y:"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Account">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Name">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Title">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Level">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_AccessLevel">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Clan">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Exp">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Hp">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Mp">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Sex">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Str">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Con">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Dex">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Wis">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Int">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Cha">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Ac">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Map">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_X">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Y">
          <Properties>
            <Property name="editable" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="B_Item">
          <Properties>
            <Property name="text" type="java.lang.String" value="���~�����"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="B_ItemActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JComboBox" name="CB_Item">
          <Properties>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="4">
                <StringItem index="0" value="0,���W���~"/>
                <StringItem index="1" value="1,�ܮw"/>
                <StringItem index="2" value="2,����ܮw"/>
                <StringItem index="3" value="3,���˭ܮw"/>
              </StringArray>
            </Property>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPopupMenu" name="PM_Player">

      <Layout class="org.netbeans.modules.form.compat2.layouts.DesignAbsoluteLayout">
        <Property name="useNullLayout" type="boolean" value="true"/>
      </Layout>
      <SubComponents>
        <MenuItem class="javax.swing.JMenuItem" name="MI_Kill">
          <Properties>
            <Property name="mnemonic" type="int" value="75"/>
            <Property name="text" type="java.lang.String" value="�j���(K)"/>
          </Properties>
        </MenuItem>
        <MenuItem class="javax.swing.JMenuItem" name="MI_BanIP">
          <Properties>
            <Property name="mnemonic" type="int" value="66"/>
            <Property name="text" type="java.lang.String" value="����IP(B)"/>
          </Properties>
        </MenuItem>
        <Component class="javax.swing.JSeparator" name="jSeparator1">
        </Component>
        <MenuItem class="javax.swing.JMenuItem" name="MI_ShowPlayer">
          <Properties>
            <Property name="mnemonic" type="int" value="80"/>
            <Property name="text" type="java.lang.String" value="���a���(P)"/>
          </Properties>
        </MenuItem>
        <Component class="javax.swing.JSeparator" name="jSeparator2">
        </Component>
        <MenuItem class="javax.swing.JMenuItem" name="MI_Whisper">
          <Properties>
            <Property name="mnemonic" type="int" value="87"/>
            <Property name="text" type="java.lang.String" value="�K�y(W)"/>
          </Properties>
        </MenuItem>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="text" type="java.lang.String" value="jLabel1"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JDialog" name="D_Item">

      <Layout class="org.netbeans.modules.form.compat2.layouts.DesignGridLayout">
        <Property name="columns" type="int" value="0"/>
        <Property name="rows" type="int" value="1"/>
      </Layout>
      <SubComponents>
        <Container class="javax.swing.JScrollPane" name="jScrollPane1">
          <AuxValues>
            <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
          </AuxValues>

          <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
          <SubComponents>
            <Component class="javax.swing.JTable" name="T_Item">
              <Properties>
                <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.RADConnectionPropertyEditor">
                  <SerializedValue value="-84,-19,0,5,115,114,0,64,111,114,103,46,110,101,116,98,101,97,110,115,46,109,111,100,117,108,101,115,46,102,111,114,109,46,101,100,105,116,111,114,115,50,46,84,97,98,108,101,77,111,100,101,108,69,100,105,116,111,114,36,78,98,84,97,98,108,101,77,111,100,101,108,-95,8,-65,-35,21,111,108,-106,12,0,0,120,114,0,36,106,97,118,97,120,46,115,119,105,110,103,46,116,97,98,108,101,46,65,98,115,116,114,97,99,116,84,97,98,108,101,77,111,100,101,108,114,-53,-21,56,-82,1,-1,-66,2,0,1,76,0,12,108,105,115,116,101,110,101,114,76,105,115,116,116,0,37,76,106,97,118,97,120,47,115,119,105,110,103,47,101,118,101,110,116,47,69,118,101,110,116,76,105,115,116,101,110,101,114,76,105,115,116,59,120,112,119,8,0,0,0,0,0,0,0,0,117,114,0,19,91,76,106,97,118,97,46,108,97,110,103,46,83,116,114,105,110,103,59,-83,-46,86,-25,-23,29,123,71,2,0,0,120,112,0,0,0,0,117,114,0,2,91,90,87,-113,32,57,20,-72,93,-30,2,0,0,120,112,0,0,0,0,120"/>
                </Property>
              </Properties>
              <AuxValues>
                <AuxValue name="JavaCodeGenerator_CreateCodeCustom" type="java.lang.String" value="new JTable(DTM_Item);"/>
              </AuxValues>
            </Component>
          </SubComponents>
        </Container>
      </SubComponents>
    </Container>
    <Menu class="javax.swing.JMenuBar" name="MB">
      <SubComponents>
        <Menu class="javax.swing.JMenu" name="M_File">
          <Properties>
            <Property name="mnemonic" type="int" value="70"/>
            <Property name="text" type="java.lang.String" value="�ɮ�(F)"/>
          </Properties>
          <SubComponents>
            <MenuItem class="javax.swing.JMenuItem" name="MI_Save">
              <Properties>
                <Property name="accelerator" type="javax.swing.KeyStroke" editor="org.netbeans.modules.form.editors.KeyStrokeEditor">
                  <KeyStroke key="Ctrl+S"/>
                </Property>
                <Property name="mnemonic" type="int" value="83"/>
                <Property name="text" type="java.lang.String" value="�x�s�T��(S)"/>
              </Properties>
            </MenuItem>
            <MenuItem class="javax.swing.JSeparator" name="jSeparator3">
            </MenuItem>
            <MenuItem class="javax.swing.JMenuItem" name="MI_SetClose">
              <Properties>
                <Property name="accelerator" type="javax.swing.KeyStroke" editor="org.netbeans.modules.form.editors.KeyStrokeEditor">
                  <KeyStroke key="Ctrl+E"/>
                </Property>
                <Property name="mnemonic" type="int" value="69"/>
                <Property name="text" type="java.lang.String" value="�]�w�������A��(E)..."/>
              </Properties>
            </MenuItem>
            <MenuItem class="javax.swing.JMenuItem" name="MI_Close">
              <Properties>
                <Property name="mnemonic" type="int" value="67"/>
                <Property name="text" type="java.lang.String" value="�������A��(C)"/>
              </Properties>
            </MenuItem>
          </SubComponents>
        </Menu>
        <Menu class="javax.swing.JMenu" name="M_Edit">
          <Properties>
            <Property name="mnemonic" type="int" value="69"/>
            <Property name="text" type="java.lang.String" value="�s��(E)"/>
          </Properties>
        </Menu>
        <Menu class="javax.swing.JMenu" name="M_Special">
          <Properties>
            <Property name="mnemonic" type="int" value="83"/>
            <Property name="text" type="java.lang.String" value="�S��\��(S)"/>
          </Properties>
          <SubComponents>
            <MenuItem class="javax.swing.JMenuItem" name="MI_Angel">
              <Properties>
                <Property name="mnemonic" type="int" value="65"/>
                <Property name="text" type="java.lang.String" value="�j�Ѩϯ���(A)"/>
              </Properties>
            </MenuItem>
            <MenuItem class="javax.swing.JMenuItem" name="MI_AllBuff">
              <Properties>
                <Property name="mnemonic" type="int" value="66"/>
                <Property name="text" type="java.lang.String" value="�׷�����(B)"/>
              </Properties>
            </MenuItem>
            <MenuItem class="javax.swing.JMenuItem" name="MI_AllRess">
              <Properties>
                <Property name="mnemonic" type="int" value="82"/>
                <Property name="text" type="java.lang.String" value="����_���ɦ��](R)"/>
              </Properties>
            </MenuItem>
          </SubComponents>
        </Menu>
      </SubComponents>
    </Menu>
  </NonVisualComponents>
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
    <Property name="title" type="java.lang.String" value="�Ѱ�޲z����"/>
    <Property name="locationByPlatform" type="boolean" value="true"/>
    <Property name="minimumSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
      <Dimension value="[800, 600]"/>
    </Property>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="menuBar" type="java.lang.String" value="MB"/>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
  </SyntheticProperties>
  <Events>
    <EventHandler event="windowClosed" listener="java.awt.event.WindowListener" parameters="java.awt.event.WindowEvent" handler="formWindowClosed"/>
  </Events>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
    <AuxValue name="designerSize" type="java.awt.Dimension" value="-84,-19,0,5,115,114,0,18,106,97,118,97,46,97,119,116,46,68,105,109,101,110,115,105,111,110,65,-114,-39,-41,-84,95,68,20,2,0,2,73,0,6,104,101,105,103,104,116,73,0,5,119,105,100,116,104,120,112,0,0,1,116,0,0,2,-73"/>
  </AuxValues>

  <Layout class="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout"/>
  <SubComponents>
    <Container class="javax.swing.JSplitPane" name="SP_Split">
      <Properties>
        <Property name="dividerLocation" type="int" value="550"/>
      </Properties>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
          <BorderConstraints direction="Center"/>
        </Constraint>
      </Constraints>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JSplitPaneSupportLayout"/>
      <SubComponents>
        <Container class="javax.swing.JTabbedPane" name="TP">
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JSplitPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JSplitPaneSupportLayout$JSplitPaneConstraintsDescription">
              <JSplitPaneConstraints position="left"/>
            </Constraint>
          </Constraints>

          <Layout class="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout"/>
          <SubComponents>
            <Container class="javax.swing.JScrollPane" name="SP_Consol">
              <Properties>
                <Property name="autoscrolls" type="boolean" value="true"/>
              </Properties>
              <AuxValues>
                <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
              </AuxValues>
              <Constraints>
                <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout$JTabbedPaneConstraintsDescription">
                  <JTabbedPaneConstraints tabName="Consol">
                    <Property name="tabTitle" type="java.lang.String" value="Consol"/>
                  </JTabbedPaneConstraints>
                </Constraint>
              </Constraints>

              <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
              <SubComponents>
                <Component class="javax.swing.JTextArea" name="TA_Consol">
                  <Properties>
                    <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="0" green="0" red="0" type="rgb"/>
                    </Property>
                    <Property name="columns" type="int" value="20"/>
                    <Property name="editable" type="boolean" value="false"/>
                    <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="ff" green="ff" red="ff" type="rgb"/>
                    </Property>
                    <Property name="rows" type="int" value="5"/>
                    <Property name="enabled" type="boolean" value="false"/>
                  </Properties>
                </Component>
              </SubComponents>
            </Container>
            <Container class="javax.swing.JScrollPane" name="SP_AllChat">
              <Properties>
                <Property name="autoscrolls" type="boolean" value="true"/>
              </Properties>
              <AuxValues>
                <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
              </AuxValues>
              <Constraints>
                <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout$JTabbedPaneConstraintsDescription">
                  <JTabbedPaneConstraints tabName="�����W">
                    <Property name="tabTitle" type="java.lang.String" value="�����W"/>
                  </JTabbedPaneConstraints>
                </Constraint>
              </Constraints>

              <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
              <SubComponents>
                <Component class="javax.swing.JTextArea" name="TA_AllChat">
                  <Properties>
                    <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="0" green="0" red="0" type="rgb"/>
                    </Property>
                    <Property name="columns" type="int" value="20"/>
                    <Property name="editable" type="boolean" value="false"/>
                    <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="ff" green="ff" red="ff" type="rgb"/>
                    </Property>
                    <Property name="rows" type="int" value="5"/>
                  </Properties>
                </Component>
              </SubComponents>
            </Container>
            <Container class="javax.swing.JScrollPane" name="SP_World">
              <Properties>
                <Property name="autoscrolls" type="boolean" value="true"/>
              </Properties>
              <AuxValues>
                <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
              </AuxValues>
              <Constraints>
                <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout$JTabbedPaneConstraintsDescription">
                  <JTabbedPaneConstraints tabName="�@�� ">
                    <Property name="tabTitle" type="java.lang.String" value="�@�� "/>
                  </JTabbedPaneConstraints>
                </Constraint>
              </Constraints>

              <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
              <SubComponents>
                <Component class="javax.swing.JTextArea" name="TA_World">
                  <Properties>
                    <Property name="columns" type="int" value="20"/>
                    <Property name="editable" type="boolean" value="false"/>
                    <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="cc" green="0" red="0" type="rgb"/>
                    </Property>
                    <Property name="rows" type="int" value="5"/>
                    <Property name="enabled" type="boolean" value="false"/>
                  </Properties>
                </Component>
              </SubComponents>
            </Container>
            <Container class="javax.swing.JScrollPane" name="SP_Normal">
              <Properties>
                <Property name="autoscrolls" type="boolean" value="true"/>
              </Properties>
              <AuxValues>
                <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
              </AuxValues>
              <Constraints>
                <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout$JTabbedPaneConstraintsDescription">
                  <JTabbedPaneConstraints tabName="�@��">
                    <Property name="tabTitle" type="java.lang.String" value="�@��"/>
                  </JTabbedPaneConstraints>
                </Constraint>
              </Constraints>

              <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
              <SubComponents>
                <Component class="javax.swing.JTextArea" name="TA_Normal">
                  <Properties>
                    <Property name="columns" type="int" value="20"/>
                    <Property name="editable" type="boolean" value="false"/>
                    <Property name="rows" type="int" value="5"/>
                    <Property name="enabled" type="boolean" value="false"/>
                  </Properties>
                </Component>
              </SubComponents>
            </Container>
            <Container class="javax.swing.JScrollPane" name="SP_">
              <Properties>
                <Property name="autoscrolls" type="boolean" value="true"/>
              </Properties>
              <AuxValues>
                <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
              </AuxValues>
              <Constraints>
                <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout$JTabbedPaneConstraintsDescription">
                  <JTabbedPaneConstraints tabName="�K�y">
                    <Property name="tabTitle" type="java.lang.String" value="�K�y"/>
                  </JTabbedPaneConstraints>
                </Constraint>
              </Constraints>

              <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
              <SubComponents>
                <Component class="javax.swing.JTextArea" name="TA_Private">
                  <Properties>
                    <Property name="columns" type="int" value="20"/>
                    <Property name="editable" type="boolean" value="false"/>
                    <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="33" green="0" red="cc" type="rgb"/>
                    </Property>
                    <Property name="rows" type="int" value="5"/>
                    <Property name="enabled" type="boolean" value="false"/>
                  </Properties>
                </Component>
              </SubComponents>
            </Container>
            <Container class="javax.swing.JScrollPane" name="SP_Clan">
              <Properties>
                <Property name="autoscrolls" type="boolean" value="true"/>
              </Properties>
              <AuxValues>
                <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
              </AuxValues>
              <Constraints>
                <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout$JTabbedPaneConstraintsDescription">
                  <JTabbedPaneConstraints tabName="���">
                    <Property name="tabTitle" type="java.lang.String" value="���"/>
                  </JTabbedPaneConstraints>
                </Constraint>
              </Constraints>

              <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
              <SubComponents>
                <Component class="javax.swing.JTextArea" name="TA_Clan">
                  <Properties>
                    <Property name="columns" type="int" value="20"/>
                    <Property name="editable" type="boolean" value="false"/>
                    <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="0" green="33" red="99" type="rgb"/>
                    </Property>
                    <Property name="rows" type="int" value="5"/>
                    <Property name="enabled" type="boolean" value="false"/>
                  </Properties>
                </Component>
              </SubComponents>
            </Container>
            <Container class="javax.swing.JScrollPane" name="SP_Team">
              <Properties>
                <Property name="autoscrolls" type="boolean" value="true"/>
              </Properties>
              <AuxValues>
                <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
              </AuxValues>
              <Constraints>
                <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout$JTabbedPaneConstraintsDescription">
                  <JTabbedPaneConstraints tabName="�ն�">
                    <Property name="tabTitle" type="java.lang.String" value="�ն�"/>
                  </JTabbedPaneConstraints>
                </Constraint>
              </Constraints>

              <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
              <SubComponents>
                <Component class="javax.swing.JTextArea" name="TA_Team">
                  <Properties>
                    <Property name="columns" type="int" value="20"/>
                    <Property name="editable" type="boolean" value="false"/>
                    <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                      <Color blue="66" green="0" red="66" type="rgb"/>
                    </Property>
                    <Property name="rows" type="int" value="5"/>
                    <Property name="enabled" type="boolean" value="false"/>
                  </Properties>
                </Component>
              </SubComponents>
            </Container>
          </SubComponents>
        </Container>
        <Container class="javax.swing.JScrollPane" name="SP_player">
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JSplitPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JSplitPaneSupportLayout$JSplitPaneConstraintsDescription">
              <JSplitPaneConstraints position="right"/>
            </Constraint>
          </Constraints>

          <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
          <SubComponents>
            <Component class="javax.swing.JTable" name="T_Player">
              <Properties>
                <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.RADConnectionPropertyEditor">
                  <SerializedValue value="-84,-19,0,5,115,114,0,64,111,114,103,46,110,101,116,98,101,97,110,115,46,109,111,100,117,108,101,115,46,102,111,114,109,46,101,100,105,116,111,114,115,50,46,84,97,98,108,101,77,111,100,101,108,69,100,105,116,111,114,36,78,98,84,97,98,108,101,77,111,100,101,108,-95,8,-65,-35,21,111,108,-106,12,0,0,120,114,0,36,106,97,118,97,120,46,115,119,105,110,103,46,116,97,98,108,101,46,65,98,115,116,114,97,99,116,84,97,98,108,101,77,111,100,101,108,114,-53,-21,56,-82,1,-1,-66,2,0,1,76,0,12,108,105,115,116,101,110,101,114,76,105,115,116,116,0,37,76,106,97,118,97,120,47,115,119,105,110,103,47,101,118,101,110,116,47,69,118,101,110,116,76,105,115,116,101,110,101,114,76,105,115,116,59,120,112,119,8,0,0,0,0,0,0,0,0,117,114,0,19,91,76,106,97,118,97,46,108,97,110,103,46,83,116,114,105,110,103,59,-83,-46,86,-25,-23,29,123,71,2,0,0,120,112,0,0,0,0,117,114,0,2,91,90,87,-113,32,57,20,-72,93,-30,2,0,0,120,112,0,0,0,0,120"/>
                </Property>
              </Properties>
              <Events>
                <EventHandler event="mousePressed" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="T_PlayerMousePressed"/>
                <EventHandler event="mouseReleased" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="T_PlayerMouseReleased"/>
              </Events>
              <AuxValues>
                <AuxValue name="JavaCodeGenerator_CreateCodeCustom" type="java.lang.String" value="new JTable(DTM);"/>
              </AuxValues>
            </Component>
          </SubComponents>
        </Container>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="jPanel2">
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
          <BorderConstraints direction="South"/>
        </Constraint>
      </Constraints>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" attributes="0">
                  <Component id="CB_Channel" min="-2" max="-2" attributes="0"/>
                  <EmptySpace max="-2" attributes="0"/>
                  <Component id="TF_Target" min="-2" pref="68" max="-2" attributes="0"/>
                  <EmptySpace max="-2" attributes="0"/>
                  <Component id="TF_Msg" min="-2" pref="310" max="-2" attributes="0"/>
                  <EmptySpace max="-2" attributes="0"/>
                  <Component id="B_Submit" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="175" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" attributes="0">
                  <EmptySpace min="6" pref="6" max="6" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="CB_Channel" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="TF_Target" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="TF_Msg" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="B_Submit" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JComboBox" name="CB_Channel">
          <Properties>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="2">
                <StringItem index="0" value="�T���W�D"/>
                <StringItem index="1" value="�K�y"/>
              </StringArray>
            </Property>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Target">
        </Component>
        <Component class="javax.swing.JButton" name="B_Submit">
          <Properties>
            <Property name="text" type="java.lang.String" value="�o�e"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="B_SubmitActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JTextField" name="TF_Msg">
          <Events>
            <EventHandler event="keyPressed" listener="java.awt.event.KeyListener" parameters="java.awt.event.KeyEvent" handler="TF_MsgKeyPressed"/>
          </Events>
        </Component>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
