package com.lineage.server.serverpackets;

import com.lineage.server.model.Instance.L1PetInstance;
import java.util.Iterator;
import java.util.List;
import com.lineage.server.model.Instance.L1ItemInstance;
import java.util.ArrayList;
import com.lineage.server.model.Instance.L1PcInstance;

public class S_PetList extends ServerBasePacket {
	private byte[] _byte;

	public S_PetList(final int npcObjId, final L1PcInstance pc) {
		this._byte = null;
		this.buildPacket(npcObjId, pc);
	}

	private void buildPacket(final int npcObjId, final L1PcInstance pc) {
		final List<L1ItemInstance> amuletList = new ArrayList();
		final Iterator<L1ItemInstance> iterator = pc.getInventory().getItems().iterator();
		while (iterator.hasNext()) {
			final Object itemObject = iterator.next();
			final L1ItemInstance item = (L1ItemInstance) itemObject;
			switch (item.getItem().getItemId()) {
			case 40314:
			case 40316: {
				if (this.isWithdraw(pc, item)) {
					continue;
				}
				amuletList.add(item);
			}
			default: {
				continue;
			}
			}
		}
		if (amuletList.size() != 0) {
			this.writeC(83);
			this.writeD(70);
			this.writeH(amuletList.size());
			final Iterator<L1ItemInstance> iterator2 = amuletList.iterator();
			while (iterator2.hasNext()) {
				final L1ItemInstance item2 = iterator2.next();
				this.writeD(item2.getId());
				this.writeC((int) Math.min(item2.getCount(), 2000000000L));
			}
		}
	}

	private boolean isWithdraw(final L1PcInstance pc, final L1ItemInstance item) {
		final Object[] petlist = pc.getPetList().values().toArray();
		final Object[] array;
		final int length = (array = petlist).length;
		int i = 0;
		while (i < length) {
			final Object petObject = array[i];
			if (petObject instanceof L1PetInstance) {
				final L1PetInstance pet = (L1PetInstance) petObject;
				if (item.getId() == pet.getItemObjId()) {
					return true;
				}
			}
			++i;
		}
		return false;
	}

	@Override
	public byte[] getContent() {
		if (this._byte == null) {
			this._byte = this.getBytes();
		}
		return this._byte;
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
