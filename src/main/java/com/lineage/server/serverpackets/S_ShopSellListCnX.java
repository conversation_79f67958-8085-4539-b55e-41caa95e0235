package com.lineage.server.serverpackets;

import com.lineage.server.templates.L1Item;
import java.util.Iterator;
import java.util.ArrayList;
import com.lineage.server.model.Instance.L1ItemStatus;
import com.lineage.server.templates.L1ShopItem;
import com.lineage.server.datatables.ShopCnTable;
import com.lineage.server.model.Instance.L1PcInstance;

public class S_ShopSellListCnX extends ServerBasePacket {
	private byte[] _byte;

	public S_ShopSellListCnX(final L1PcInstance pc, final int tgObjid) {
		this._byte = null;
		this.buildPacket(pc, tgObjid);
	}

	private void buildPacket(final L1PcInstance pc, final int tgObjid) {
		this.writeC(70);
		this.writeD(tgObjid);
		final ArrayList<L1ShopItem> shopItems = ShopCnTable.get().get(1);
		if (shopItems.size() <= 0) {
			this.writeH(0);
			return;
		}
		this.writeH(shopItems.size());
		int i = 0;
		final Iterator<L1ShopItem> iterator = shopItems.iterator();
		while (iterator.hasNext()) {
			final L1ShopItem shopItem = iterator.next();
			++i;
			pc.get_otherList().add_cnList(i, shopItem);
			final L1Item item = shopItem.getItem();
			this.writeD(i);
			this.writeH(item.getGfxId());
			this.writeD(shopItem.getPrice());
			if (shopItem.getPackCount() > 1) {
				this.writeS(String.valueOf(item.getNameId()) + " (" + shopItem.getPackCount() + ")");
			} else {
				this.writeS(item.getNameId());
			}
			final L1ItemStatus itemInfo = new L1ItemStatus(item);
			final byte[] status = itemInfo.getStatusBytes(true).getBytes();
			this.writeC(status.length);
			final byte[] array;
			final int length = (array = status).length;
			int j = 0;
			while (j < length) {
				final byte b = array[j];
				this.writeC(b);
				++j;
			}
		}
		this.writeH(6100);
	}

	@Override
	public byte[] getContent() {
		if (this._byte == null) {
			this._byte = this.getBytes();
		}
		return this._byte;
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
