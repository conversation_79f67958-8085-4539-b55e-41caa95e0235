package com.lineage.server.serverpackets;

import com.lineage.config.ConfigOther;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.L1Object;
import com.lineage.server.utils.Random;
import com.lineage.server.world.World;

import java.util.concurrent.atomic.AtomicInteger;

public class S_UseAttackSkill extends ServerBasePacket {
    private static final AtomicInteger _sequentialNumber;

    static {
        _sequentialNumber = new AtomicInteger(0);
    }

    private byte[] _byte;

    public S_UseAttackSkill(L1Character cha, int targetobj, int spellgfx, int x, int y,
                            int actionId, boolean motion) {
        _byte = null;
        buildPacket(cha, targetobj, spellgfx, x, y, actionId, 0, motion);
    }

    public S_UseAttackSkill(L1Character cha, int targetobj, int spellgfx, int x, int y,
                            int actionId, int dmg) {
        _byte = null;
        buildPacket(cha, targetobj, spellgfx, x, y, actionId, dmg, true);
    }

    public S_UseAttackSkill(L1Character cha, int targetobj, int x, int y, int[] data,
                            boolean withCastMotion) {
        _byte = null;
        buildPacket(cha, targetobj, x, y, data, withCastMotion);
    }

    private static int calcheading(int myx, int myy, int tx, int ty) {
        int newheading = 0;
        if (tx > myx && ty > myy) {
            newheading = 3;
        }
        if (tx < myx && ty < myy) {
            newheading = 7;
        }
        if (tx > myx && ty == myy) {
            newheading = 2;
        }
        if (tx < myx && ty == myy) {
            newheading = 6;
        }
        if (tx == myx && ty < myy) {
            newheading = 0;
        }
        if (tx == myx && ty > myy) {
            newheading = 4;
        }
        if (tx < myx && ty > myy) {
            newheading = 5;
        }
        if (tx > myx && ty < myy) {
            newheading = 1;
        }
        return newheading;
    }

    private int SpecialActid(L1Character cha) {
        int actionId = 18;
        int tempgfxid = cha.getTempCharGfx();
        if (Random.nextInt(100) < 25) {
            if (tempgfxid == 13727 || tempgfxid == 13729) {
                actionId = 31;
            }
            if (tempgfxid == 13731 || tempgfxid == 13733) {
                actionId = 31;
            }
        }
        if (tempgfxid == 5727 || tempgfxid == 5730) {
            actionId = 19;
        }
        if (tempgfxid == 5733 || tempgfxid == 5736) {
            actionId = 1;
        }
        return actionId;
    }

    private void buildPacket(L1Character cha, int targetobj, int x, int y, int[] data,
                             boolean withCastMotion) {
        if (cha instanceof L1PcInstance && cha.hasSkillEffect(67) && data[0] == 18) {
            int tempchargfx = cha.getTempCharGfx();
            if (tempchargfx == 5727 || tempchargfx == 5730) {
                data[0] = 19;
            } else if (tempchargfx == 5733 || tempchargfx == 5736) {
                data[0] = 1;
            }
        }
        if (cha.getTempCharGfx() == 4013) {
            data[0] = 1;
        }
        int newheading = calcheading(cha.getX(), cha.getY(), x, y);
        cha.setHeading(newheading);
        writeC(30);
        writeC(data[0]);
        writeD(withCastMotion ? cha.getId() : 0);
        writeD(targetobj);
        writeH(data[1]);
        writeC(newheading);
        writeD(S_UseAttackSkill._sequentialNumber.incrementAndGet());
        writeH(data[2]);
        writeC(data[3]);
        writeH(cha.getX());
        writeH(cha.getY());
        writeH(x);
        writeH(y);
        writeC(0);
        writeC(0);
        writeC(0);
    }

    private void buildPacket(L1Character cha, int targetobj, int spellgfx, int x, int y,
                             int actionId, int dmg, boolean withCastMotion) {
        if (cha instanceof L1PcInstance && cha.hasSkillEffect(67) && actionId == 18) {
            actionId = SpecialActid(cha);
        }
        if (cha.getTempCharGfx() == 4013) {
            actionId = 1;
        }
        int newheading = calcheading(cha.getX(), cha.getY(), x, y);
        cha.setHeading(newheading);
        writeC(30);
        writeC(actionId);
        writeD(withCastMotion ? cha.getId() : 0);
        L1Object obj = World.get().findObject(targetobj);
        if (obj instanceof L1NpcInstance) {
            L1NpcInstance npc = (L1NpcInstance) obj;
            int[] bow_GFX_Arrow = ConfigOther.AtkNo;
            int[] array;
            int n = (array = bow_GFX_Arrow).length;
            int i = 0;
            while (i < n) {
                int gfx = array[i];
                if (npc.getTempCharGfx() == gfx) {
                    targetobj = 0;
                }
                ++i;
            }
            switch (npc.getTempCharGfx()) {
                case 2544:
                case 10913: {
                    targetobj = 0;
                    break;
                }
            }
        }
        if (obj instanceof L1PcInstance) {
            L1PcInstance pc1 = (L1PcInstance) obj;
            int[] atkpc = ConfigOther.AtkNo_pc;
            int[] array;
            int n = (array = atkpc).length;
            int i = 0;
            while (i < n) {
                int gfx = array[i];
                if (pc1.getTempCharGfx() == gfx) {
                    targetobj = 0;
                }
                ++i;
            }
        }
        writeD(targetobj);

        L1Object target = World.get().findObject(targetobj);
        if (target instanceof L1PcInstance) {
            L1PcInstance tg = (L1PcInstance) target;
            if (tg.getTempCharGfx() == 23353 || tg.getTempCharGfx() == 23759) {
                dmg = 0;
            }
        }

        if (dmg > 0) {
            writeH(10);
        } else {
            writeH(0);
        }
        writeC(newheading);
        writeD(S_UseAttackSkill._sequentialNumber.incrementAndGet());
        writeH(spellgfx);
        writeC(0);
        writeH(cha.getX());
        writeH(cha.getY());
        writeH(x);
        writeH(y);
        writeD(0);
        writeC(0);
    }

    @Override
    public byte[] getContent() {
        if (_byte == null) {
            _byte = _bao.toByteArray();
        } else {
            int seq = 0;
            synchronized (this) {
                seq = S_UseAttackSkill._sequentialNumber.incrementAndGet();
            }
            _byte[13] = (byte) (seq & 0xFF);
            _byte[14] = (byte) (seq >> 8 & 0xFF);
            _byte[15] = (byte) (seq >> 16 & 0xFF);
            _byte[16] = (byte) (seq >> 24 & 0xFF);
        }
        return _byte;
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }
}
