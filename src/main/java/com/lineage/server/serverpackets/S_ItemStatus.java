package com.lineage.server.serverpackets;

import com.lineage.server.model.Instance.L1ItemStatus;
import com.lineage.server.model.Instance.L1ItemInstance;

public class S_ItemStatus extends ServerBasePacket {
	private byte[] _byte;

	public S_ItemStatus(final L1ItemInstance item) {
		this._byte = null;
		if (item == null) {
			return;
		}
		this.buildPacket(item);
	}

	private void buildPacket(final L1ItemInstance item) {
		this.writeC(24);
		this.writeD(item.getId());
		this.writeS(item.getViewName());
		final int count = (int) Math.min(item.getCount(), 2000000000L);
		this.writeD(count);
		if (!item.isIdentified()) {
			this.writeC(0);
		} else {
			final L1ItemStatus itemInfo = new L1ItemStatus(item);
			final byte[] status = itemInfo.getStatusBytes(false).getBytes();
			this.writeC(status.length);
			final byte[] array;
			final int length = (array = status).length;
			int i = 0;
			while (i < length) {
				final byte b = array[i];
				this.writeC(b);
				++i;
			}
		}
	}

	public S_ItemStatus(final L1ItemInstance item, final long count) {
		this._byte = null;
		this.writeC(24);
		this.writeD(item.getId());
		this.writeS(item.getNumberedViewName(count));
		final int out_count = (int) Math.min(count, 2000000000L);
		this.writeD(out_count);
		if (!item.isIdentified()) {
			this.writeC(0);
		} else {
			final L1ItemStatus itemInfo = new L1ItemStatus(item);
			final byte[] status = itemInfo.getStatusBytes(false).getBytes();
			this.writeC(status.length);
			final byte[] array;
			final int length = (array = status).length;
			int i = 0;
			while (i < length) {
				final byte b = array[i];
				this.writeC(b);
				++i;
			}
		}
	}

	public S_ItemStatus(final L1ItemInstance item, final boolean isIdentified) {
		this._byte = null;
		this.writeC(24);
		this.writeD(item.getId());
		this.writeS(item.getViewName());
		final int count = (int) Math.min(item.getCount(), 2000000000L);
		this.writeD(count);
		if (!isIdentified) {
			this.writeC(0);
		} else {
			final L1ItemStatus itemInfo = new L1ItemStatus(item);
			final byte[] status = itemInfo.getStatusBytes(true).getBytes();
			this.writeC(status.length);
			final byte[] array;
			final int length = (array = status).length;
			int i = 0;
			while (i < length) {
				final byte b = array[i];
				this.writeC(b);
				++i;
			}
		}
	}

	@Override
	public byte[] getContent() {
		if (this._byte == null) {
			this._byte = this.getBytes();
		}
		return this._byte;
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
