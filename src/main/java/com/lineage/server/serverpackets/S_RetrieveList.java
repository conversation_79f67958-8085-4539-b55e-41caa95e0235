package com.lineage.server.serverpackets;

import java.util.Iterator;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;

public class S_RetrieveList extends ServerBasePacket {
	private byte[] _byte;

	public S_RetrieveList(final int objid, final L1PcInstance pc) {
		this._byte = null;
		if (pc.getInventory().getSize() < 180) {
			final int size = pc.getDwarfInventory().getSize();
			if (size > 0) {
				this.writeC(176);
				this.writeD(objid);
				this.writeH(size);
				this.writeC(3);
				final Iterator<?> localIterator = pc.getDwarfInventory().getItems().iterator();
				while (localIterator.hasNext()) {
					final Object itemObject = localIterator.next();
					final L1ItemInstance item = (L1ItemInstance) itemObject;
					this.writeD(item.getId());
					int i = item.getItem().getUseType();
					if (i < 0) {
						i = 0;
					}
					this.writeC(i);
					this.writeH(item.get_gfxid());
					this.writeC(item.getBless());
					this.writeD((int) Math.min(item.getCount(), 2000000000L));
					this.writeC(item.isIdentified() ? 1 : 0);
					this.writeS(item.getViewName());
				}
				this.writeH(30);
				this.writeD(0);
			} else {
				pc.sendPackets(new S_ServerMessage(1625));
			}
		} else {
			pc.sendPackets(new S_ServerMessage(263));
		}
	}

	@Override
	public byte[] getContent() {
		if (this._byte == null) {
			this._byte = this.getBytes();
		}
		return this._byte;
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
