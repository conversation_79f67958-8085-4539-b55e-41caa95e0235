package com.lineage.server.serverpackets;

public class S_CommonInfo extends ServerBasePacket {
	private byte[] _byte;

	public S_CommonInfo(final int type, final String[] info) {
		this._byte = null;
		this.buildPacket(type, info);
	}

	private void buildPacket(final int type, final String[] info) {
		this.writeH(type);
		if (info == null) {
			this.writeC(0);
		} else {
			this.writeC(info.length);
			int i = 0;
			while (i < info.length) {
				this.writeS(info[i]);
				++i;
			}
		}
	}

	@Override
	public byte[] getContent() {
		if (this._byte == null) {
			this._byte = this.getBytes();
		}
		return this._byte;
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
