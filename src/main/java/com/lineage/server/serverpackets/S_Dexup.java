package com.lineage.server.serverpackets;

import com.lineage.server.model.Instance.L1PcInstance;

public class S_<PERSON>up extends ServerBasePacket {
	private byte[] _byte;

	public S_Dexup(final L1PcInstance pc, final int type, final int time) {
		this._byte = null;
		this.writeC(188);
		this.writeH(time);
		this.writeC(pc.getDex());
		this.writeC(type);
	}

	@Override
	public byte[] getContent() {
		if (this._byte == null) {
			this._byte = this.getBytes();
		}
		return this._byte;
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
