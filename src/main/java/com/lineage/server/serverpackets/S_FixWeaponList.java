package com.lineage.server.serverpackets;

import java.util.ArrayList;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.Iterator;
import com.lineage.server.model.Instance.L1ItemInstance;
import java.util.List;

public class S_FixWeaponList extends ServerBasePacket {
	private byte[] _byte;

	public S_FixWeaponList(final List<L1ItemInstance> weaponList) {
		this._byte = null;
		this.writeC(83);
		this.writeD(200);
		this.writeH(weaponList.size());
		final Iterator<L1ItemInstance> iterator = weaponList.iterator();
		while (iterator.hasNext()) {
			final L1ItemInstance weapon = iterator.next();
			this.writeD(weapon.getId());
			this.writeC(weapon.get_durability());
		}
	}

	public S_FixWeaponList(final L1PcInstance pc) {
		this._byte = null;
		this.buildPacket(pc);
	}

	private void buildPacket(final L1PcInstance pc) {
		this.writeC(83);
		this.writeD(200);
		final List<L1ItemInstance> weaponList = new ArrayList();
		final List<L1ItemInstance> itemList = pc.getInventory().getItems();
		final Iterator<L1ItemInstance> iterator = itemList.iterator();
		while (iterator.hasNext()) {
			final L1ItemInstance item = iterator.next();
			switch (item.getItem().getType2()) {
			case 1: {
				if (item.get_durability() <= 0) {
					continue;
				}
				weaponList.add(item);
			}
			default: {
				continue;
			}
			}
		}
		this.writeH(weaponList.size());
		final Iterator<L1ItemInstance> iterator2 = weaponList.iterator();
		while (iterator2.hasNext()) {
			final L1ItemInstance weapon = iterator2.next();
			this.writeD(weapon.getId());
			this.writeC(weapon.get_durability());
		}
	}

	public S_FixWeaponList(final L1ItemInstance weapon) {
		this._byte = null;
		this.writeC(83);
		this.writeD(200);
		this.writeH(1);
		this.writeD(weapon.getId());
		this.writeC(weapon.get_durability());
	}

	@Override
	public byte[] getContent() {
		if (this._byte == null) {
			this._byte = this.getBytes();
		}
		return this._byte;
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
