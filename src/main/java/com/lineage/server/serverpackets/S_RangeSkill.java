package com.lineage.server.serverpackets;

import com.lineage.config.ConfigOther;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.L1Object;
import com.lineage.server.model.skill.TargetStatus;
import com.lineage.server.world.World;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.concurrent.atomic.AtomicInteger;

public class S_RangeSkill extends ServerBasePacket {
    public static final int TYPE_NODIR = 0;
    public static final int TYPE_DIR = 8;
    private static final AtomicInteger _sequentialNumber;

    static {
        _sequentialNumber = new AtomicInteger(9000000);
    }

    private byte[] _byte;

    public S_RangeSkill(L1Character cha, ArrayList<TargetStatus> targetList, int spellgfx,
                        int actionId, int type) {
        _byte = null;
        writeC(42);
        writeC(actionId);
        writeD(cha.getId());
        writeH(cha.getX());
        writeH(cha.getY());
        switch (type) {
            case 0: {
                writeC(cha.getHeading());
                break;
            }
            case 8: {
                int newHeading = calcheading(cha.getX(), cha.getY(), targetList.get(0).getTarget().getX(),
                        targetList.get(0).getTarget().getY());
                cha.setHeading(newHeading);
                writeC(cha.getHeading());
                break;
            }
        }
        writeD(S_RangeSkill._sequentialNumber.incrementAndGet());
        writeH(spellgfx);
        writeC(type);
        writeH(0);
        writeH(targetList.size());

        Iterator<TargetStatus> iterator = targetList.iterator();
        while (iterator.hasNext()) {
            TargetStatus target = iterator.next();
            int targetobj = target.getTarget().getId();
            L1Object obj = World.get().findObject(targetobj);
            if (obj instanceof L1NpcInstance) {
                L1NpcInstance npc = (L1NpcInstance) obj;
                int[] bow_GFX_Arrow = ConfigOther.AtkNo;
                int[] array;
                int n = (array = bow_GFX_Arrow).length;
                int i = 0;
                while (i < n) {
                    int gfx = array[i];
                    if (npc.getTempCharGfx() == gfx) {
                        targetobj = 0;
                    }
                    ++i;
                }
                switch (npc.getTempCharGfx()) {
                    case 2544:
                    case 45024: {
                        targetobj = 0;
                        break;
                    }
                }
            }
            if (obj instanceof L1PcInstance) {
                L1PcInstance pc1 = (L1PcInstance) obj;
                int[] atkpc = ConfigOther.AtkNo_pc;
                int[] array;
                int n = (array = atkpc).length;
                int i = 0;
                while (i < n) {
                    int gfx = array[i];
                    if (pc1.getTempCharGfx() == gfx) {
                        targetobj = 0;
                    }
                    ++i;
                }
            }
            writeD(targetobj);
            if (target.isCalc()) {
                int dmg = 0x20;
                if (target.getTarget() instanceof L1PcInstance) {
                    L1PcInstance tgpc = (L1PcInstance) target.getTarget();
                    if (tgpc.getTempCharGfx() == 23353 || tgpc.getTempCharGfx() == 23759) {
                        dmg = 0;
                    }
                }
                writeH(dmg);
            } else {
                writeH(0);
            }
        }
    }

    private static int calcheading(int myx, int myy, int tx, int ty) {
        int newheading = 0;
        if (tx > myx && ty > myy) {
            newheading = 3;
        }
        if (tx < myx && ty < myy) {
            newheading = 7;
        }
        if (tx > myx && ty == myy) {
            newheading = 2;
        }
        if (tx < myx && ty == myy) {
            newheading = 6;
        }
        if (tx == myx && ty < myy) {
            newheading = 0;
        }
        if (tx == myx && ty > myy) {
            newheading = 4;
        }
        if (tx < myx && ty > myy) {
            newheading = 5;
        }
        if (tx > myx && ty < myy) {
            newheading = 1;
        }
        return newheading;
    }

    @Override
    public byte[] getContent() {
        if (_byte == null) {
            _byte = getBytes();
        }
        return _byte;
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }
}
