package com.lineage.server.timecontroller.pet;

import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PetInstance;
import com.lineage.server.world.WorldPet;
import com.lineage.server.thread.GeneralThreadPool;
import org.apache.commons.logging.LogFactory;
import java.util.concurrent.ScheduledFuture;
import org.apache.commons.logging.Log;
import java.util.TimerTask;

public class PetMprTimer extends TimerTask {
	private static final Log _log;
	private ScheduledFuture<?> _timer;
	private static int _time;

	static {
		_log = LogFactory.getLog(PetMprTimer.class);
		_time = 0;
	}

	public void start() {
		PetMprTimer._time = 0;
		final int timeMillis = 1000;
		this._timer = GeneralThreadPool.get().scheduleAtFixedRate(this, 1000L, 1000L);
	}

	@Override
	public void run() {
		try {
			++PetMprTimer._time;
			final Collection<L1PetInstance> allPet = WorldPet.get().all();
			if (allPet.isEmpty()) {
				return;
			}
			final Iterator<L1PetInstance> iter = allPet.iterator();
			while (iter.hasNext()) {
				final L1PetInstance pet = iter.next();
				if (MprPet.mpUpdate(pet, PetMprTimer._time)) {
					Thread.sleep(5L);
				}
			}
		} catch (Exception e) {
			PetMprTimer._log.error("Pet MP自然回復時間軸異常重啟", e);
			GeneralThreadPool.get().cancel(this._timer, false);
			final PetMprTimer petMprTimer = new PetMprTimer();
			petMprTimer.start();
		}
	}
}
