package com.lineage.server.timecontroller;

import com.lineage.server.timecontroller.npc.*;

public class StartTimer_Npc {
    public void start() throws InterruptedException {
        NpcChatTimer npcChatTimeController = new NpcChatTimer();
        npcChatTimeController.start();
        Thread.sleep(50L);
        NpcHprTimer npcHprTimer = new NpcHprTimer();
        npcHprTimer.start();
        Thread.sleep(50L);
        NpcMprTimer npcMprTimer = new NpcMprTimer();
        npcMprTimer.start();
        Thread.sleep(50L);
        NpcDeleteTimer npcDeleteTimer = new NpcDeleteTimer();
        npcDeleteTimer.start();
        Thread.sleep(50L);
        NpcDeadTimer npcDeadTimer = NpcDeadTimer.get();
        npcDeadTimer.start();
        Thread.sleep(50L);
        NpcDigestItemTimer digestItemTimer = new NpcDigestItemTimer();
        digestItemTimer.start();
        Thread.sleep(50L);
        NpcSpawnBossTimer bossTimer = new NpcSpawnBossTimer();
        bossTimer.start();
        Thread.sleep(50L);
        NpcShopTimer shopTimer = new NpcShopTimer();
        shopTimer.start();
        Thread.sleep(50L);
        NpcRestTimer restTimer = new NpcRestTimer();
        restTimer.start();
        Thread.sleep(50L);
        NpcWorkTimer workTimer = new NpcWorkTimer();
        workTimer.start();
        Thread.sleep(50L);
        NpcBowTimer bow = new NpcBowTimer();
        bow.start();
    }
}
