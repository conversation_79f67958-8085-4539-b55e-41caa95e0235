package com.lineage.server.timecontroller.item;

import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1Teleport;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.TimerTask;

/**
 * "穿雲箭視窗關閉
 *
 * <AUTHOR>
 */
public class AllCallTimer extends TimerTask {

    private static final Log _log = LogFactory.getLog(AllCallTimer.class);

    public static List<L1PcInstance> pcInstanceList = new ArrayList<>();

    public void start(List<L1PcInstance> _pcInstanceList) {
        pcInstanceList = new ArrayList<>();
        pcInstanceList = _pcInstanceList;
        run();
    }

    @Override
    public void run() {
        try {
            if (pcInstanceList.size() > 0) {
                Thread.sleep(5000);
                for (L1PcInstance pc : pcInstanceList) {
                    L1Teleport.teleport(pc, pc.getX(), pc.getY(), pc.getMapId(), pc.getHeading(), false);
                }
            }
        } catch (Exception e) {
            _log.error("穿雲箭視窗關閉異常", e);
        }
    }
}
