package com.lineage.server.timecontroller.pc;

import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_Disconnect;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.world.World;
import com.lineage.server.thread.PcOtherThreadPool;
import org.apache.commons.logging.LogFactory;
import java.util.concurrent.ScheduledFuture;
import org.apache.commons.logging.Log;
import java.util.TimerTask;

public class PcDeleteTimer extends TimerTask {
	private static final Log _log;
	private ScheduledFuture<?> _timer;

	static {
		_log = LogFactory.getLog(PcDeleteTimer.class);
	}

	public void start() {
		final int timeMillis = 1100;
		this._timer = PcOtherThreadPool.get().scheduleAtFixedRate(this, 1100L, 1100L);
	}

	@Override
	public void run() {
		try {
			final Collection<L1PcInstance> all = World.get().getAllPlayers();
			if (all.isEmpty()) {
				return;
			}
			final Iterator<L1PcInstance> iter = all.iterator();
			while (iter.hasNext()) {
				final L1PcInstance tgpc = iter.next();
				if (!tgpc.isDead()) {
					continue;
				}
				if (tgpc.get_delete_time() <= 0) {
					continue;
				}
				final int newtime = tgpc.get_delete_time() - 1;
				tgpc.set_delete_time(newtime);
				if (tgpc.get_delete_time() > 0) {
					continue;
				}
				tgpc.sendPackets(new S_Disconnect());
			}
		} catch (Exception e) {
			PcDeleteTimer._log.error("PC 死亡刪除處理時間軸異常重啟", e);
			PcOtherThreadPool.get().cancel(this._timer, false);
			final PcDeleteTimer pcHprTimer = new PcDeleteTimer();
			pcHprTimer.start();
		}
	}
}
