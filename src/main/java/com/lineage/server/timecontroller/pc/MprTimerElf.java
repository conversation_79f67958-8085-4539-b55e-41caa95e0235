package com.lineage.server.timecontroller.pc;

import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.world.WorldElf;
import com.lineage.server.thread.PcOtherThreadPool;
import org.apache.commons.logging.LogFactory;
import java.util.concurrent.ScheduledFuture;
import org.apache.commons.logging.Log;
import java.util.TimerTask;

public class MprTimerElf extends TimerTask {
	private static final Log _log;
	private ScheduledFuture<?> _timer;

	static {
		_log = LogFactory.getLog(MprTimerElf.class);
	}

	public void start() {
		final int timeMillis = 1000;
		this._timer = PcOtherThreadPool.get().scheduleAtFixedRate(this, 1000L, 1000L);
	}

	@Override
	public void run() {
		try {
			final Collection<L1PcInstance> allPc = WorldElf.get().all();
			if (allPc.isEmpty()) {
				return;
			}
			final Iterator<L1PcInstance> iter = allPc.iterator();
			while (iter.hasNext()) {
				final L1PcInstance tgpc = iter.next();
				final MprExecutor mpr = MprExecutor.get();
				if (mpr.check(tgpc)) {
					mpr.checkRegenMp(tgpc);
					Thread.sleep(1L);
				}
			}
		} catch (Exception e) {
			MprTimerElf._log.error("Pc(精靈) MP自然回復時間軸異常重啟", e);
			PcOtherThreadPool.get().cancel(this._timer, false);
			final MprTimerElf mprElf = new MprTimerElf();
			mprElf.start();
		}
	}
}
