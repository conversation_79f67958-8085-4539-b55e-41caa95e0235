package com.lineage.server.timecontroller.skill;

import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.model.Instance.L1EffectType;
import com.lineage.server.model.Instance.L1EffectInstance;
import com.lineage.server.world.WorldEffect;
import com.lineage.server.thread.GeneralThreadPool;
import org.apache.commons.logging.LogFactory;
import java.util.concurrent.ScheduledFuture;
import org.apache.commons.logging.Log;
import java.util.TimerTask;

public class EffectCubeEruptionTimer extends TimerTask {
	private static final Log _log;
	private ScheduledFuture<?> _timer;

	static {
		_log = LogFactory.getLog(EffectCubeEruptionTimer.class);
	}

	public void start() {
		final int timeMillis = 500;
		this._timer = GeneralThreadPool.get().scheduleAtFixedRate(this, 500L, 500L);
	}

	@Override
	public void run() {
		try {
			final Collection allNpc = WorldEffect.get().all();
			if (allNpc.isEmpty()) {
				return;
			}
			final Iterator iter = allNpc.iterator();
			while (iter.hasNext()) {
				final L1EffectInstance effect = (L1EffectInstance) iter.next();
				if (effect.effectType() == L1EffectType.isCubeEruption) {
					EffectCubeExecutor.get().cubeBurn(effect);
					Thread.sleep(1L);
				}
			}
		} catch (Exception e) {
			EffectCubeEruptionTimer._log.error("Npc L1Effect幻術師技能(立方：地裂)狀態送出時間軸異常重啟", e);
			GeneralThreadPool.get().cancel(this._timer, false);
			final EffectCubeEruptionTimer cubeEruptionTimer = new EffectCubeEruptionTimer();
			cubeEruptionTimer.start();
		}
	}
}
