package com.lineage.server.timecontroller.npc;

import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.thread.GeneralThreadPool;
import com.lineage.server.world.WorldNpc;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Collection;
import java.util.TimerTask;
import java.util.concurrent.ScheduledFuture;
import java.util.function.Consumer;
import java.util.stream.Stream;

/**
 * 怪物死掉重生Timer
 * NpcDeadCount 分子為1000
 * 丹丹 Kevin
 */
public class NpcDeadTimer extends TimerTask {
    private static final Log _log = LogFactory.getLog(NpcDeadTimer.class);
    private static final int NpcDeadCount = 4; //1000 / 4 = 250 代表一秒清250隻
    private static NpcDeadTimer _instance;
    private static ConsumRecycleNpcTask _task;
    private ScheduledFuture<?> _timer;

    public static NpcDeadTimer get() {
        if (_instance == null) {
            _instance = new NpcDeadTimer();
        }
        if (_task == null) {
            _task = new ConsumRecycleNpcTask();
        }

        return _instance;
    }

    public void start() {
        final int timeMillis = 2000;
        _timer = GeneralThreadPool.get().scheduleAtFixedRate(this, timeMillis, timeMillis);
    }

    @Override
    public void run() {
        try {
            Collection<L1NpcInstance> allMob = WorldNpc.get().all();
            // 不包含元素
            if (allMob.isEmpty()) {
                return;
            }
            Stream<L1NpcInstance> stream = allMob.parallelStream();
            stream.forEach(_task);

        } catch (Exception e) {
            NpcDeadTimer._log.error("NPC死亡移除時間軸異常重啟", e);
            GeneralThreadPool.get().cancel(_timer, false);
            NpcDeadTimer npcDeadTimer = new NpcDeadTimer();
            npcDeadTimer.start();
        }
    }

    public static class ConsumRecycleNpcTask implements Consumer<L1NpcInstance> {
        @Override
        public void accept(L1NpcInstance npc) {
            try {
                if (npc == null) {
                    return;
                }
                if (npc.getMaxHp() <= 0) {
                    return;
                }
                // 未死亡(復活可能)
                if (!npc.isDead()) {
                    return;
                }

                if (npc.get_deadTimerTemp() == -1) {
                    return;

                } else {
                    int time = npc.get_deadTimerTemp() - 2;

                    // 避免跟 -1 情況撞到
                    if (time < 0) {
                        time = 0;
                    }
                    npc.set_deadTimerTemp(time);
                }

                if (npc.get_deadTimerTemp() <= 0) {
                    npc.deleteMe();
                }

                Thread.sleep(NpcDeadCount);
            } catch (Exception e) {
                _log.error("NPC死亡時間軸異常重啟", e);
            }
        }
    }
}
