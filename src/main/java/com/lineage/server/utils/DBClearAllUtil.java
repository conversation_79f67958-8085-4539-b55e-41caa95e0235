package com.lineage.server.utils;

import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import java.sql.SQLException;
import com.lineage.DatabaseFactory;
import com.lineage.DatabaseFactoryLogin;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class DBClearAllUtil {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(DBClearAllUtil.class);
	}

	@SuppressWarnings("resource")
	public static void start() {
		try {
			Connection cn = null;
			PreparedStatement ps = null;
			try {
				cn = DatabaseFactoryLogin.get().getConnection();
				System.out.print("刪除玩家帳號.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `accounts`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除封鎖紀錄.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `ban_ip`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("更新最大編號.....");
				ps = cn.prepareStatement("UPDATE `server_info` SET maxid = minid");
				ps.execute();
				System.out.println("ok!");
				cn = DatabaseFactory.get().getConnection();
				System.out.print("刪除玩家資料.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `characters`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除玩家銀行.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_bank`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除玩家時間武器附魔.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_weapon_time`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除玩家好友.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_buddys`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除玩家狀態.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_buff`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除玩家陣營...");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_c1`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除快速鍵紀錄...");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_config`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除玩家妖精倉庫.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_elf_warehouse`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除背包倉庫.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_items`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除物品期限.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_items_time`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除物品打洞.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_item_power`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除玩家信建.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_mail`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除玩家其他.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_other`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除玩家寵物.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_pets`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除玩家任務.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_quests`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除託售紀錄.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_shop`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除託售紀錄.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_shopinfo`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除玩家技能.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_skills`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除記憶座標.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_teleport`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除記憶座標記錄.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_teleport_config`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除玩家VIP.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_vip`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除倉庫.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_warehouse`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除個人倉庫.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_warehouse_for_cha`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除血盟資料.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `clan_data`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除盟輝圖檔.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `clan_emblem`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除血盟相關資料1.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `clan_matching_apclist`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除血盟相關資料1.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `clan_matching_list`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除血盟相關資料2.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `clan_members`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除血盟相關資料3.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `clan_warehouse`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除血盟相關資料3.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `clan_warehouse_history`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除佈告欄資料...");
				ps = cn.prepareStatement("TRUNCATE TABLE `server_board`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除賭狗紀錄.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_gambling`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除家具紀錄.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `spawnlist_furniture`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除other_enchant.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_衝裝1`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除other_shifting.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_交易1`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除ezpay.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `ezpay`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_bug回報.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_bug回報`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_gm_指令.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_gm_指令`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_加速.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_加速`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_攻城獎勵.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_攻城獎勵`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_丟怪.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_丟怪`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_丟物.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_丟物`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("紀錄_玩家洗血.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_洗血`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("紀錄_回憶蠟燭.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_回憶蠟燭`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_交易.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_交易`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_刪物.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_刪物`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_妖倉.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_妖倉`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_改密.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_改密`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除w_炫色_記錄資料.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_炫色_記錄資料`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_更名.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_更名`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_個倉.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_個倉`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_殺人.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_殺人`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_盟倉.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_盟倉`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_噴裝.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_噴裝`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_衝裝.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_衝裝`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_撿物.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_撿物`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_寶箱.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_寶箱`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_隨機禮物.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_隨機禮物`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_角色_擺攤.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_擺攤`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_消費.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_消費`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_掉寶.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_掉寶`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_聊天.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_聊天`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_裝武_保護.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_裝武_保護`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_贊助_滿額禮.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_贊助_滿額禮`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除除錯_封包.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_除錯_封包`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除除錯_傳送.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_除錯_傳送`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除紀錄_物品_時間.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_物品_時間`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除除錯_傳送.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_角色_ai驗證`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除character_內掛.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_內掛`");
				ps.execute();
				System.out.println("ok!");
				System.out.print("刪除character_天賦紀錄.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_天賦紀錄`");
				ps.execute();
				System.out.println("ok!");
				ps = cn.prepareStatement("TRUNCATE TABLE `character_離線擺攤`");
				ps.execute();
				ps = cn.prepareStatement("TRUNCATE TABLE `characters_帳號使用道具`");
				ps.execute();
				ps = cn.prepareStatement("TRUNCATE TABLE `characters_腳色使用道具`");
				ps.execute();
				ps = cn.prepareStatement("TRUNCATE TABLE `character_bad_buddy`");
				ps.execute();
				System.out.print("復原拍賣公告.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `server_board_auction`");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262145', '$1242 1$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  1', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262146', '$1242 2$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  2', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262147', '$1242 3$1195', '120', '2010-09-13 21:00:00', '100000', '$1242  3', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262148', '$1242 4$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  4', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262149', '$1242 5$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  5', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262150', '$1242 6$1195', '120', '2010-09-13 21:00:00', '100000', '$1242  6', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262151', '$1242 7$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  7', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262152', '$1242 8$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  8', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262153', '$1242 9$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  9', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262154', '$1242 10$1195', '120', '2010-09-13 21:00:00', '100000', '$1242  10', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262155', '$1242 11$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  11', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262156', '$1242 12$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  12', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262157', '$1242 13$1195', '120', '2010-09-13 21:00:00', '100000', '$1242  13', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262158', '$1242 14$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  14', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262159', '$1242 15$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  15', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262160', '$1242 16$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  16', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262161', '$1242 17$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  17', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262162', '$1242 18$1195', '120', '2010-09-13 21:00:00', '100000', '$1242  18', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262163', '$1242 19$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  19', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262164', '$1242 20$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  20', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262165', '$1242 21$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  21', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262166', '$1242 22$1195', '120', '2010-09-13 21:00:00', '100000', '$1242  22', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262167', '$1242 23$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  23', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262168', '$1242 24$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  24', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262169', '$1242 25$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  25', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262170', '$1242 26$1195', '120', '2010-09-13 21:00:00', '100000', '$1242  26', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262171', '$1242 27$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  27', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262172', '$1242 28$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  28', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262173', '$1242 29$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  29', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262175', '$1242 31$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  31', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262176', '$1242 32$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  32', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262177', '$1242 33$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  33', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262178', '$1242 34$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  34', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262179', '$1242 35$1195', '120', '2010-09-13 21:00:00', '100000', '$1242  35', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262180', '$1242 36$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  36', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262181', '$1242 37$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  37', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262182', '$1242 38$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  38', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262184', '$1242 40$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  40', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262185', '$1242 41$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  41', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262186', '$1242 42$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  42', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262187', '$1242 43$1195', '120', '2010-09-13 21:00:00', '100000', '$1242  43', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262188', '$1242 44$1195', '120', '2010-09-13 21:00:00', '100000', '$1242  44', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262189', '$1242 45$1195', '78', '2010-09-13 21:00:00', '100000', '$1242  45', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('327681', '$1513 1$1195', '0', '2010-09-13 21:00:00', '100000', '$1513  1', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('327682', '$1513 2$1195', '0', '2010-09-13 21:00:00', '100000', '$1513  2', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('327683', '$1513 3$1195', '0', '2010-09-13 21:00:00', '100000', '$1513  3', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('327684', '$1513 4$1195', '0', '2010-09-13 21:00:00', '100000', '$1513  4', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('327685', '$1513 5$1195', '0', '2010-09-13 21:00:00', '100000', '$1513  5', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('327686', '$1513 6$1195', '0', '2010-09-13 21:00:00', '100000', '$1513  6', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('327687', '$1513 7$1195', '0', '2010-09-13 21:00:00', '100000', '$1513  7', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('327688', '$1513 8$1195', '0', '2010-09-13 21:00:00', '100000', '$1513  8', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('327689', '$1513 9$1195', '0', '2010-09-13 21:00:00', '100000', '$1513  9', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('327690', '$1513 10$1195', '0', '2010-09-13 21:00:00', '100000', '$1513  10', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('327691', '$1513 11$1195', '0', '2010-09-13 21:00:00', '100000', '$1513  11', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('524289', '$381 1$1195', '48', '2010-09-13 21:00:00', '100000', '$381  1', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('524290', '$381 2$1195', '71', '2010-09-13 21:00:00', '100000', '$381  2', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('524291', '$381 3$1195', '48', '2010-09-13 21:00:00', '100000', '$381  3', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('524292', '$381 4$1195', '48', '2010-09-13 21:00:00', '100000', '$381  4', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('524293', '$381 5$1195', '82', '2010-09-13 21:00:00', '100000', '$381  5', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('524294', '$381 6$1195', '40', '2010-09-13 21:00:00', '100000', '$381  6', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262174', '$1242 29$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  30', '', '0', '', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_board_auction` VALUES ('262183', '$1242 39$1195', '45', '2010-09-13 21:00:00', '100000', '$1242  39', '', '0', '', '0')");
				ps.execute();
				System.out.println("ok!");
				System.out.print("復原城堡數據.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `server_castle`");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_castle` VALUES ('1', '肯特城', '2008-09-15 00:00:00', '50', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_castle` VALUES ('2', '妖魔城', '2008-09-15 00:00:00', '50', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_castle` VALUES ('3', '風木城', '2008-09-15 00:00:00', '50', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_castle` VALUES ('4', '奇岩城', '2008-09-15 00:00:00', '50', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_castle` VALUES ('5', '海音城', '2008-09-15 00:00:00', '50', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_castle` VALUES ('6', '侏儒城', '2008-09-15 00:00:00', '50', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_castle` VALUES ('7', '亞丁城', '2008-09-15 00:00:00', '50', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_castle` VALUES ('8', '狄亞得要塞', '2008-09-15 00:00:00', '50', '0')");
				ps.execute();
				System.out.println("ok!");
				System.out.print("復原小屋數據.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `server_house`");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262145', '奇岩血盟小屋1', '78', '1', '50527', '1', '0', '2010-09-01 00:00:00', '33368', '33375', '32651', '32654', '33373', '33375', '32655', '32657', '4', '5068', '33374', '32657')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262146', '奇岩血盟小屋2', '45', '2', '50505', '1', '0', '2010-09-01 00:00:00', '33381', '33387', '32653', '32656', '0', '0', '0', '0', '4', '5069', '33384', '32655')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262147', '奇岩血盟小屋3', '120', '3', '50519', '1', '0', '2010-09-01 00:00:00', '33392', '33404', '32650', '32656', '0', '0', '0', '0', '4', '5070', '33395', '32656')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262148', '奇岩血盟小屋4', '45', '4', '50545', '1', '0', '2010-09-01 00:00:00', '33427', '33430', '32656', '32662', '0', '0', '0', '0', '4', '5071', '33428', '32659')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262149', '奇岩血盟小屋5', '78', '5', '50531', '1', '0', '2010-09-01 00:00:00', '33439', '33445', '32665', '32667', '33442', '33445', '32668', '32672', '4', '5072', '33439', '32666')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262150', '奇岩血盟小屋6', '120', '6', '50529', '1', '0', '2010-09-01 00:00:00', '33454', '33466', '32648', '32654', '0', '0', '0', '0', '4', '5073', '33457', '32654')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262151', '奇岩血盟小屋7', '45', '7', '50516', '1', '0', '2010-09-01 00:00:00', '33476', '33479', '32665', '32671', '0', '0', '0', '0', '4', '5074', '33477', '32668')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262152', '奇岩血盟小屋8', '78', '8', '50538', '1', '0', '2010-09-01 00:00:00', '33471', '33477', '32678', '32680', '33474', '33477', '32681', '32685', '4', '5075', '33471', '32679')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262153', '奇岩血盟小屋9', '78', '9', '50518', '1', '0', '2010-09-01 00:00:00', '33453', '33460', '32694', '32697', '33458', '33460', '32698', '32700', '4', '5076', '33459', '32700')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262154', '奇岩血盟小屋10', '120', '10', '50509', '1', '0', '2010-09-01 00:00:00', '33421', '33433', '32685', '32691', '0', '0', '0', '0', '4', '5077', '33424', '32691')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262155', '奇岩血盟小屋11', '78', '11', '50536', '1', '0', '2010-09-01 00:00:00', '33409', '33415', '32674', '32676', '33412', '33415', '32677', '32681', '4', '5078', '33409', '32675')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262156', '奇岩血盟小屋12', '78', '12', '50520', '1', '0', '2010-09-01 00:00:00', '33414', '33421', '32703', '32706', '33419', '33421', '32707', '32709', '4', '5079', '33420', '32709')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262157', '奇岩血盟小屋13', '120', '13', '50543', '1', '0', '2010-09-01 00:00:00', '33372', '33384', '32692', '32698', '0', '0', '0', '0', '4', '5080', '33375', '32698')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262158', '奇岩血盟小屋14', '78', '14', '50526', '1', '0', '2010-09-01 00:00:00', '33362', '33365', '32681', '32687', '0', '0', '0', '0', '4', '5081', '33363', '32684')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262159', '奇岩血盟小屋15', '45', '15', '50512', '1', '0', '2010-09-01 00:00:00', '33360', '33366', '32669', '32671', '33363', '33366', '32672', '32676', '4', '5082', '33360', '32670')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262160', '奇岩血盟小屋16', '78', '16', '50510', '1', '0', '2010-09-01 00:00:00', '33341', '33347', '32660', '32662', '33344', '33347', '32663', '32667', '4', '5083', '33341', '32661')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262161', '奇岩血盟小屋17', '45', '17', '50504', '1', '0', '2010-09-01 00:00:00', '33345', '33348', '32672', '32678', '0', '0', '0', '0', '4', '5084', '33346', '32675')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262162', '奇岩血盟小屋18', '120', '18', '50525', '1', '0', '2010-09-01 00:00:00', '33338', '33350', '32704', '32711', '0', '0', '0', '0', '4', '5085', '33341', '32710')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262163', '奇岩血盟小屋19', '78', '19', '50534', '1', '0', '2010-09-01 00:00:00', '33349', '33356', '32728', '32731', '33354', '33356', '32732', '32734', '4', '5086', '33355', '32734')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262164', '奇岩血盟小屋20', '78', '20', '50540', '1', '0', '2010-09-01 00:00:00', '33366', '33372', '32713', '32715', '33369', '33372', '32716', '32720', '4', '5087', '33366', '32714')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262165', '奇岩血盟小屋21', '45', '21', '50515', '1', '0', '2010-09-01 00:00:00', '33380', '33383', '32712', '32718', '0', '0', '0', '0', '4', '5088', '33381', '32715')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262166', '奇岩血盟小屋22', '120', '22', '50513', '1', '0', '2010-09-01 00:00:00', '33401', '33413', '32733', '32739', '0', '0', '0', '0', '4', '5089', '33404', '32739')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262167', '奇岩血盟小屋23', '78', '23', '50528', '1', '0', '2010-09-01 00:00:00', '33424', '33430', '32717', '32719', '33427', '33430', '32720', '32724', '4', '5090', '33424', '32718')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262168', '奇岩血盟小屋24', '45', '24', '50533', '1', '0', '2010-09-01 00:00:00', '33448', '33451', '32729', '32735', '0', '0', '0', '0', '4', '5091', '33449', '32732')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262169', '奇岩血盟小屋25', '45', '25', '50542', '1', '0', '2010-09-01 00:00:00', '33404', '33407', '32754', '32760', '0', '0', '0', '0', '4', '5092', '33405', '32757')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262170', '奇岩血盟小屋26', '120', '26', '50511', '1', '0', '2010-09-01 00:00:00', '33363', '33375', '32755', '32761', '0', '0', '0', '0', '4', '5093', '33366', '32761')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262171', '奇岩血盟小屋27', '78', '27', '50501', '1', '0', '2010-09-01 00:00:00', '33351', '33357', '32774', '32776', '33354', '33357', '32777', '32781', '4', '5094', '33351', '32775')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262172', '奇岩血盟小屋28', '45', '28', '50503', '1', '0', '2010-09-01 00:00:00', '33355', '33361', '32787', '32790', '0', '0', '0', '0', '4', '5095', '33358', '32789')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262173', '奇岩血盟小屋29', '78', '29', '50508', '1', '0', '2010-09-01 05:00:00', '33366', '33373', '32786', '32789', '33371', '33373', '32790', '32792', '4', '5096', '33372', '32792')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262174', '奇岩血盟小屋30', '45', '30', '50514', '1', '0', '2010-09-01 00:00:00', '33383', '33386', '32773', '32779', '0', '0', '0', '0', '4', '5097', '33384', '32776')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262175', '奇岩血盟小屋31', '78', '31', '50532', '1', '0', '2010-09-01 00:00:00', '33397', '33404', '32788', '32791', '33402', '33404', '32792', '32794', '4', '5098', '33403', '32794')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262176', '奇岩血盟小屋32', '78', '32', '50544', '1', '0', '2010-09-01 00:00:00', '33479', '33486', '32788', '32791', '33484', '33486', '32792', '32794', '4', '5099', '33485', '32794')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262177', '奇岩血盟小屋33', '45', '33', '50524', '1', '0', '2010-09-01 00:00:00', '33498', '33501', '32801', '32807', '0', '0', '0', '0', '4', '5100', '33499', '32804')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262178', '奇岩血盟小屋34', '45', '34', '50535', '1', '0', '2010-09-01 00:00:00', '33379', '33385', '32802', '32805', '0', '0', '0', '0', '4', '5101', '33382', '32804')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262179', '奇岩血盟小屋35', '120', '35', '50521', '1', '0', '2010-09-01 00:00:00', '33373', '33385', '32822', '32829', '0', '0', '0', '0', '4', '5102', '33376', '32828')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262180', '奇岩血盟小屋36', '45', '36', '50517', '1', '0', '2010-09-01 00:00:00', '33398', '33401', '32810', '32816', '0', '0', '0', '0', '4', '5103', '33399', '32813')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262181', '奇岩血盟小屋37', '78', '37', '50537', '1', '0', '2010-09-01 00:00:00', '33397', '33403', '32821', '32823', '33400', '33403', '32824', '32828', '4', '5104', '33397', '32822')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262182', '奇岩血盟小屋38', '78', '38', '50539', '1', '0', '2010-09-01 00:00:00', '33431', '33438', '32838', '32841', '33436', '33438', '32842', '32844', '4', '5105', '33437', '32844')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262183', '奇岩血盟小屋39', '45', '39', '50507', '1', '1', '2010-09-01 00:00:00', '33456', '33462', '32838', '32841', '0', '0', '0', '0', '4', '5106', '33459', '32840')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262184', '奇岩血盟小屋40', '78', '40', '50530', '1', '0', '2010-09-01 00:00:00', '33385', '33392', '32845', '32848', '33390', '33392', '32849', '32851', '4', '5107', '33391', '32851')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262185', '奇岩血盟小屋41', '78', '41', '50502', '1', '0', '2010-09-01 00:00:00', '33399', '33405', '32859', '32861', '33402', '33405', '32862', '32866', '4', '5108', '33399', '32860')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262186', '奇岩血盟小屋42', '45', '42', '50506', '1', '0', '2010-09-01 00:00:00', '33414', '33417', '32850', '32856', '0', '0', '0', '0', '4', '5109', '33415', '32853')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262187', '奇岩血盟小屋43', '120', '43', '50522', '1', '0', '2010-09-01 00:00:00', '33372', '33384', '32867', '32873', '0', '0', '0', '0', '4', '5110', '33375', '32873')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262188', '奇岩血盟小屋44', '120', '44', '50541', '1', '0', '2010-09-01 00:00:00', '33425', '33437', '32865', '32871', '0', '0', '0', '0', '4', '5111', '33428', '32871')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('262189', '奇岩血盟小屋45', '78', '45', '50523', '1', '0', '2010-09-01 00:00:00', '33443', '33449', '32869', '32871', '33446', '33449', '32872', '32876', '4', '5112', '33443', '32870')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('327681', '海音血盟小屋1', '0', '1', '50620', '1', '0', '2010-09-01 00:00:00', '33599', '33601', '33213', '33214', '33602', '33610', '33213', '33218', '4', '5113', '33609', '33217')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('327682', '海音血盟小屋2', '0', '2', '50623', '1', '0', '2010-09-01 00:00:00', '33627', '33632', '33206', '33209', '0', '0', '0', '0', '4', '5114', '33630', '33209')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('327683', '海音血盟小屋3', '0', '3', '50619', '1', '0', '2010-09-01 00:00:00', '33626', '33627', '33225', '33227', '33628', '33632', '33221', '33230', '4', '5115', '33628', '33226')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('327684', '海音血盟小屋4', '0', '4', '50621', '1', '0', '2010-09-01 00:00:00', '33628', '33636', '33241', '33244', '33632', '33635', '33245', '33250', '4', '5116', '33633', '33248')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('327685', '海音血盟小屋5', '0', '5', '50622', '1', '0', '2010-09-01 00:00:00', '33616', '33621', '33262', '33265', '0', '0', '0', '0', '4', '5117', '33619', '33265')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('327686', '海音血盟小屋6', '0', '6', '50624', '1', '0', '2010-09-01 00:00:00', '33570', '33580', '33228', '33232', '33574', '33576', '33233', '33234', '4', '5118', '33575', '33233')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('327687', '海音血盟小屋7', '0', '7', '50617', '1', '0', '2010-09-01 00:00:00', '33583', '33588', '33305', '33314', '33587', '33588', '33315', '33316', '4', '5119', '33584', '33306')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('327688', '海音血盟小屋8', '0', '8', '50614', '1', '0', '2010-09-01 00:00:00', '33577', '33578', '33337', '33337', '33579', '33588', '33335', '33339', '4', '5120', '33581', '33338')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('327689', '海音血盟小屋9', '0', '9', '50618', '1', '0', '2010-09-01 00:00:00', '33615', '33623', '33374', '33377', '33619', '33622', '33378', '33383', '4', '5121', '33620', '33381')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('327690', '海音血盟小屋10', '0', '10', '50616', '1', '0', '2010-09-01 00:00:00', '33624', '33625', '33397', '33399', '33626', '33630', '33393', '33403', '4', '5122', '33625', '33398')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('327691', '海音血盟小屋11', '0', '11', '50615', '1', '0', '2010-09-01 21:00:00', '33621', '33622', '33444', '33444', '33622', '33632', '33442', '33446', '4', '5123', '33625', '33445')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458753', '亞丁血盟小屋1', '51', '1', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458754', '亞丁血盟小屋2', '24', '2', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458755', '亞丁血盟小屋3', '24', '3', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458756', '亞丁血盟小屋4', '56', '4', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458757', '亞丁血盟小屋5', '24', '5', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458758', '亞丁血盟小屋6', '63', '6', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458759', '亞丁血盟小屋7', '24', '7', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458760', '亞丁血盟小屋8', '63', '8', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458761', '亞丁血盟小屋9', '24', '9', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458762', '亞丁血盟小屋10', '24', '10', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458763', '亞丁血盟小屋11', '24', '11', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458764', '亞丁血盟小屋12', '24', '12', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458765', '亞丁血盟小屋13', '24', '13', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458766', '亞丁血盟小屋14', '63', '14', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458767', '亞丁血盟小屋15', '24', '15', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458768', '亞丁血盟小屋16', '56', '16', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458769', '亞丁血盟小屋17', '63', '17', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458770', '亞丁血盟小屋18', '24', '18', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458771', '亞丁血盟小屋19', '32', '19', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458772', '亞丁血盟小屋20', '64', '20', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458773', '亞丁血盟小屋21', '24', '21', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458774', '亞丁血盟小屋22', '20', '22', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458775', '亞丁血盟小屋23', '24', '23', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458776', '亞丁血盟小屋24', '56', '24', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458777', '亞丁血盟小屋25', '24', '25', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458778', '亞丁血盟小屋26', '20', '26', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458779', '亞丁血盟小屋27', '42', '27', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458780', '亞丁血盟小屋28', '15', '28', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458781', '亞丁血盟小屋29', '24', '29', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458782', '亞丁血盟小屋30', '24', '30', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458783', '亞丁血盟小屋31', '42', '31', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458784', '亞丁血盟小屋32', '48', '32', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458785', '亞丁血盟小屋33', '24', '33', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458786', '亞丁血盟小屋34', '15', '34', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458787', '亞丁血盟小屋35', '24', '35', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458788', '亞丁血盟小屋36', '24', '36', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458789', '亞丁血盟小屋37', '64', '37', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458790', '亞丁血盟小屋38', '15', '38', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458791', '亞丁血盟小屋39', '15', '39', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458792', '亞丁血盟小屋40', '24', '40', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458793', '亞丁血盟小屋41', '24', '41', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458794', '亞丁血盟小屋42', '29', '42', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458795', '亞丁血盟小屋43', '24', '43', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458796', '亞丁血盟小屋44', '15', '44', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458797', '亞丁血盟小屋45', '56', '45', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458798', '亞丁血盟小屋46', '15', '46', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458799', '亞丁血盟小屋47', '24', '47', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458800', '亞丁血盟小屋48', '24', '48', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458801', '亞丁血盟小屋49', '24', '49', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458802', '亞丁血盟小屋50', '48', '50', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458803', '亞丁血盟小屋51', '24', '51', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458804', '亞丁血盟小屋52', '24', '52', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458805', '亞丁血盟小屋53', '54', '53', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458806', '亞丁血盟小屋54', '24', '54', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458807', '亞丁血盟小屋55', '63', '55', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458808', '亞丁血盟小屋56', '63', '56', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458809', '亞丁血盟小屋57', '42', '57', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458810', '亞丁血盟小屋58', '24', '58', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458811', '亞丁血盟小屋59', '24', '59', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458812', '亞丁血盟小屋60', '63', '60', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458813', '亞丁血盟小屋61', '24', '61', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458814', '亞丁血盟小屋62', '21', '62', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458815', '亞丁血盟小屋63', '63', '63', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458816', '亞丁血盟小屋64', '24', '64', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458817', '亞丁血盟小屋65', '24', '65', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458818', '亞丁血盟小屋66', '48', '66', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('458819', '亞丁血盟小屋67', '24', '67', '0', '1', '0', '2010-09-01 00:00:00', '0', '0', '0', '0', '0', '0', '0', '0', '4', '0', '0', '0')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('524289', '古魯丁血盟小屋1', '48', '1', '50626', '1', '0', '2010-03-01 00:00:00', '32559', '32566', '32669', '32676', '0', '0', '0', '0', '4', '0', '32564', '32675')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('524290', '古魯丁血盟小屋2', '71', '2', '50627', '1', '0', '2010-09-01 00:00:00', '32548', '32556', '32705', '32716', '32547', '32547', '32710', '32716', '4', '0', '32549', '32707')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('524291', '古魯丁血盟小屋3', '48', '3', '50628', '1', '0', '2010-09-01 00:00:00', '32537', '32544', '32781', '32791', '0', '0', '0', '0', '4', '0', '32538', '32782')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('524292', '古魯丁血盟小屋4', '48', '4', '50629', '1', '0', '2010-09-01 00:00:00', '32550', '32560', '32780', '32787', '0', '0', '0', '0', '4', '0', '32558', '32786')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('524293', '古魯丁血盟小屋5', '82', '5', '50630', '1', '0', '2010-09-01 00:00:00', '32535', '32543', '32807', '32818', '32534', '32534', '32812', '32818', '4', '0', '32536', '32809')");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_house` VALUES ('524294', '古魯丁血盟小屋6', '40', '6', '50631', '1', '0', '2010-09-01 00:00:00', '32553', '32560', '32814', '32821', '0', '0', '0', '0', '4', '0', '32554', '32819')");
				ps.execute();
				System.out.println("ok!");
				System.out.print("復原村莊數據.....");
				ps = cn.prepareStatement("TRUNCATE TABLE `server_town`");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_town` VALUES ('1', '說話之島村莊', '0', null, '0', '0', '0', '0', '0', '0', null)");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_town` VALUES ('2', '銀騎士村莊', '0', null, '0', '0', '0', '0', '0', '0', null)");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_town` VALUES ('3', '古魯丁村莊', '0', null, '0', '0', '0', '0', '0', '0', null)");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_town` VALUES ('4', '燃柳村莊', '0', null, '0', '0', '0', '0', '0', '0', null)");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_town` VALUES ('5', '風木村莊', '0', null, '0', '0', '0', '0', '0', '0', null)");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_town` VALUES ('6', '肯特村莊', '0', null, '0', '0', '0', '0', '0', '0', null)");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_town` VALUES ('7', '奇岩村莊', '0', null, '0', '0', '0', '0', '0', '0', null)");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_town` VALUES ('8', '海音村', '0', null, '0', '0', '0', '0', '0', '0', null)");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_town` VALUES ('9', '威頓村', '0', null, '0', '0', '0', '0', '0', '0', null)");
				ps.execute();
				ps = cn.prepareStatement(
						"INSERT INTO `server_town` VALUES ('10', '歐瑞村', '0', null, '0', '0', '0', '0', '0', '0', null)");
				ps.execute();
				System.out.println("ok!");
			} catch (SQLException e) {
				DBClearAllUtil._log.error(e.getLocalizedMessage(), e);
			} finally {
				SQLUtil.close(ps);
				SQLUtil.close(cn);
			}
		} catch (Exception e2) {
			DBClearAllUtil._log.error(e2.getLocalizedMessage(), e2);
		}
		update();
		update1();
	}

	public static void update() {
		Connection co = null;
		PreparedStatement ps = null;
		try {
			co = DatabaseFactoryLogin.get().getConnection();
			ps = co.prepareStatement("UPDATE `config_server` SET `設置`=? WHERE `id`=?");
			ps.setString(1, "false");
			ps.setString(2, "DBClearAll");
			ps.execute();
		} catch (SQLException e) {
			DBClearAllUtil._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(ps);
			SQLUtil.close(co);
		}
	}

	public static void update1() {
		Connection co = null;
		PreparedStatement ps = null;
		try {
			co = DatabaseFactoryLogin.get().getConnection();
			ps = co.prepareStatement("UPDATE `w_即時獎勵系統` SET `appear_People`=?");
			ps.setString(1, "0");
			ps.execute();
		} catch (SQLException e) {
			DBClearAllUtil._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(ps);
			SQLUtil.close(co);
		}
	}

	public static class DBClearQuesemap {
		public static void start() {
			try {
				Connection cn = null;
				PreparedStatement ps = null;
				try {
					cn = DatabaseFactoryLogin.get().getConnection();
					ps = cn.prepareStatement("TRUNCATE TABLE `character_Quest_Map`");
					ps.execute();
				} catch (SQLException e) {
					DBClearAllUtil._log.error(e.getLocalizedMessage(), e);
				} finally {
					SQLUtil.close(ps);
					SQLUtil.close(cn);
				}
			} catch (Exception e2) {
				DBClearAllUtil._log.error(e2.getLocalizedMessage(), e2);
			}
		}
	}
}
