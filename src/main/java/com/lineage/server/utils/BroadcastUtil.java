package com.lineage.server.utils;

import com.lineage.server.serverpackets.S_PacketBoxGree;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.world.World;

public class BroadcastUtil {
    private BroadcastUtil() {
    }

    public static void broadcast(Integer broadcastType, String sampleText, Object... text) {
        switch (broadcastType) {
            case 1:
                broadcastAll(sampleText, text);
                break;
            case 2:
                broadcastMessage(sampleText, text);
                break;
            case 3:
                broadcastBoxGree(sampleText, text);
                break;
        }
    }

    public static void broadcastAll(String sampleText, Object... text) {
        broadcastMessage(sampleText, text);
        broadcastBoxGree(sampleText, text);
    }

    public static void broadcastMessage(String sampleText, Object... text) {
        World.get().broadcastPacketToAll(
                new S_SystemMessage(String.format(sampleText, text)));
    }

    public static void broadcastBoxGree(String sampleText, Object... text) {
        World.get().broadcastPacketToAll(
                new S_PacketBoxGree(String.format(sampleText, text)));

    }
}
