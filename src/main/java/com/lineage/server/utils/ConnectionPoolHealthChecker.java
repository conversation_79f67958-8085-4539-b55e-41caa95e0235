package com.lineage.server.utils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.lineage.DatabaseFactory;
import com.lineage.DatabaseFactoryLogin;

/**
 * 連接池健康檢查工具
 * 定期執行健康檢查，確保連接池正常運作
 */
public class ConnectionPoolHealthChecker {
    private static final Log _log = LogFactory.getLog(ConnectionPoolHealthChecker.class);
    private static ConnectionPoolHealthChecker _instance;
    private ScheduledExecutorService _scheduler;
    private boolean _isRunning = false;
    
    // 健康檢查間隔（秒）
    private static final int HEALTH_CHECK_INTERVAL = 600; // 10分鐘
    private static final int CONNECTION_TEST_TIMEOUT = 5; // 5秒連接測試超時
    
    private ConnectionPoolHealthChecker() {
        _scheduler = Executors.newScheduledThreadPool(1);
    }
    
    public static synchronized ConnectionPoolHealthChecker getInstance() {
        if (_instance == null) {
            _instance = new ConnectionPoolHealthChecker();
        }
        return _instance;
    }
    
    /**
     * 開始健康檢查
     */
    public void startHealthCheck() {
        if (_isRunning) {
            _log.warn("連接池健康檢查已經在運行中");
            return;
        }
        
        _isRunning = true;
        _log.info("啟動連接池健康檢查服務");
        
        // 定期健康檢查
        _scheduler.scheduleAtFixedRate(this::performHealthCheck, 60, HEALTH_CHECK_INTERVAL, TimeUnit.SECONDS);
    }
    
    /**
     * 停止健康檢查
     */
    public void stopHealthCheck() {
        if (!_isRunning) {
            return;
        }
        
        _isRunning = false;
        _scheduler.shutdown();
        try {
            if (!_scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                _scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            _scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        _log.info("連接池健康檢查服務已停止");
    }
    
    /**
     * 執行健康檢查
     */
    private void performHealthCheck() {
        try {
            _log.info("開始執行連接池健康檢查");
            
            // 檢查主資料庫
            boolean mainDbHealthy = checkMainDatabaseHealth();
            
            // 檢查登入資料庫
            boolean loginDbHealthy = checkLoginDatabaseHealth();
            
            if (mainDbHealthy && loginDbHealthy) {
                _log.info("連接池健康檢查完成 - 所有連接池狀態正常");
            } else {
                _log.error("連接池健康檢查發現問題 - 主資料庫: " + 
                    (mainDbHealthy ? "正常" : "異常") + ", 登入資料庫: " + 
                    (loginDbHealthy ? "正常" : "異常"));
            }
            
        } catch (Exception e) {
            _log.error("執行連接池健康檢查時發生錯誤", e);
        }
    }
    
    /**
     * 檢查主資料庫健康狀態
     */
    private boolean checkMainDatabaseHealth() {
        try {
            DatabaseFactory factory = DatabaseFactory.get();
            return testDatabaseConnection(factory, "主資料庫");
        } catch (Exception e) {
            _log.error("檢查主資料庫健康狀態時發生錯誤", e);
            return false;
        }
    }
    
    /**
     * 檢查登入資料庫健康狀態
     */
    private boolean checkLoginDatabaseHealth() {
        try {
            DatabaseFactoryLogin factory = DatabaseFactoryLogin.get();
            return testLoginDatabaseConnection(factory, "登入資料庫");
        } catch (Exception e) {
            _log.error("檢查登入資料庫健康狀態時發生錯誤", e);
            return false;
        }
    }
    
    /**
     * 測試主資料庫連接
     */
    private boolean testDatabaseConnection(DatabaseFactory factory, String dbName) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 獲取連接
            conn = factory.getConnection();
            if (conn == null) {
                _log.error(dbName + " - 無法獲取資料庫連接");
                return false;
            }
            
            // 測試連接有效性
            if (conn.isClosed()) {
                _log.error(dbName + " - 獲取的連接已關閉");
                return false;
            }
            
            // 執行測試查詢
            pstmt = conn.prepareStatement("SELECT 1 as test_result");
            rs = pstmt.executeQuery();
            
            if (!rs.next() || rs.getInt("test_result") != 1) {
                _log.error(dbName + " - 測試查詢失敗");
                return false;
            }
            
            long endTime = System.currentTimeMillis();
            long responseTime = endTime - startTime;
            
            if (responseTime > CONNECTION_TEST_TIMEOUT * 1000) {
                _log.warn(String.format("%s - 連接響應時間過長: %d ms", dbName, responseTime));
            } else {
                _log.debug(String.format("%s - 連接測試成功，響應時間: %d ms", dbName, responseTime));
            }
            
            return true;
            
        } catch (SQLException e) {
            _log.error(dbName + " - 資料庫連接測試失敗", e);
            return false;
        } catch (Exception e) {
            _log.error(dbName + " - 連接測試過程中發生未預期錯誤", e);
            return false;
        } finally {
            // 確保資源正確關閉
            SQLUtil.close(rs);
            SQLUtil.close(pstmt);
            SQLUtil.close(conn);
        }
    }
    
    /**
     * 測試登入資料庫連接
     */
    private boolean testLoginDatabaseConnection(DatabaseFactoryLogin factory, String dbName) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 獲取連接
            conn = factory.getConnection();
            if (conn == null) {
                _log.error(dbName + " - 無法獲取資料庫連接");
                return false;
            }
            
            // 測試連接有效性
            if (conn.isClosed()) {
                _log.error(dbName + " - 獲取的連接已關閉");
                return false;
            }
            
            // 執行測試查詢
            pstmt = conn.prepareStatement("SELECT 1 as test_result");
            rs = pstmt.executeQuery();
            
            if (!rs.next() || rs.getInt("test_result") != 1) {
                _log.error(dbName + " - 測試查詢失敗");
                return false;
            }
            
            long endTime = System.currentTimeMillis();
            long responseTime = endTime - startTime;
            
            if (responseTime > CONNECTION_TEST_TIMEOUT * 1000) {
                _log.warn(String.format("%s - 連接響應時間過長: %d ms", dbName, responseTime));
            } else {
                _log.debug(String.format("%s - 連接測試成功，響應時間: %d ms", dbName, responseTime));
            }
            
            return true;
            
        } catch (SQLException e) {
            _log.error(dbName + " - 資料庫連接測試失敗", e);
            return false;
        } catch (Exception e) {
            _log.error(dbName + " - 連接測試過程中發生未預期錯誤", e);
            return false;
        } finally {
            // 確保資源正確關閉
            SQLUtil.close(rs);
            SQLUtil.close(pstmt);
            SQLUtil.close(conn);
        }
    }
    
    /**
     * 手動執行健康檢查
     */
    public boolean performManualHealthCheck() {
        _log.info("執行手動連接池健康檢查");
        
        boolean mainDbHealthy = checkMainDatabaseHealth();
        boolean loginDbHealthy = checkLoginDatabaseHealth();
        
        boolean overallHealthy = mainDbHealthy && loginDbHealthy;
        
        _log.info("手動健康檢查結果 - 整體狀態: " + (overallHealthy ? "正常" : "異常") +
            ", 主資料庫: " + (mainDbHealthy ? "正常" : "異常") +
            ", 登入資料庫: " + (loginDbHealthy ? "正常" : "異常"));
        
        return overallHealthy;
    }
    
    /**
     * 獲取健康檢查報告
     */
    public String getHealthReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 連接池健康檢查報告 ===\n");
        
        boolean mainDbHealthy = checkMainDatabaseHealth();
        boolean loginDbHealthy = checkLoginDatabaseHealth();
        
        report.append("主資料庫狀態: ").append(mainDbHealthy ? "正常" : "異常").append("\n");
        report.append("登入資料庫狀態: ").append(loginDbHealthy ? "正常" : "異常").append("\n");
        report.append("整體狀態: ").append((mainDbHealthy && loginDbHealthy) ? "正常" : "異常").append("\n");
        
        return report.toString();
    }
}
