package com.lineage.server.utils;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * 記憶體管理器
 * 監控和優化記憶體使用
 */
public class MemoryManager {
    private static final Log _log = LogFactory.getLog(MemoryManager.class);
    private static MemoryManager _instance;
    
    private ScheduledExecutorService _scheduler;
    private MemoryMXBean _memoryBean;
    
    // 記憶體警告閾值
    private static final double MEMORY_WARNING_THRESHOLD = 0.8; // 80%
    private static final double MEMORY_CRITICAL_THRESHOLD = 0.95; // 95%
    
    // 監控間隔
    private static final int MONITOR_INTERVAL = 60; // 1分鐘
    private static final int GC_SUGGESTION_INTERVAL = 300; // 5分鐘
    
    private long _lastGcSuggestion = 0;
    private boolean _isMonitoring = false;
    
    private MemoryManager() {
        _scheduler = Executors.newScheduledThreadPool(1);
        _memoryBean = ManagementFactory.getMemoryMXBean();
    }
    
    public static synchronized MemoryManager getInstance() {
        if (_instance == null) {
            _instance = new MemoryManager();
        }
        return _instance;
    }
    
    /**
     * 開始記憶體監控
     */
    public void startMonitoring() {
        if (_isMonitoring) {
            _log.warn("記憶體監控已經在運行中");
            return;
        }
        
        _isMonitoring = true;
        _log.info("啟動記憶體監控服務");
        
        _scheduler.scheduleAtFixedRate(this::monitorMemory, 0, MONITOR_INTERVAL, TimeUnit.SECONDS);
    }
    
    /**
     * 停止記憶體監控
     */
    public void stopMonitoring() {
        if (!_isMonitoring) {
            return;
        }
        
        _isMonitoring = false;
        _scheduler.shutdown();
        try {
            if (!_scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                _scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            _scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        _log.info("記憶體監控服務已停止");
    }
    
    /**
     * 監控記憶體使用情況
     */
    private void monitorMemory() {
        try {
            MemoryUsage heapUsage = _memoryBean.getHeapMemoryUsage();
            MemoryUsage nonHeapUsage = _memoryBean.getNonHeapMemoryUsage();
            
            // 計算使用率
            double heapUsagePercent = (double) heapUsage.getUsed() / heapUsage.getMax();
            double nonHeapUsagePercent = (double) nonHeapUsage.getUsed() / nonHeapUsage.getMax();
            
            // 記錄基本信息
            _log.debug(String.format("記憶體使用情況 - 堆記憶體: %.2f%% (%d MB / %d MB), 非堆記憶體: %.2f%% (%d MB / %d MB)",
                heapUsagePercent * 100,
                heapUsage.getUsed() / 1024 / 1024,
                heapUsage.getMax() / 1024 / 1024,
                nonHeapUsagePercent * 100,
                nonHeapUsage.getUsed() / 1024 / 1024,
                nonHeapUsage.getMax() / 1024 / 1024));
            
            // 檢查警告條件
            checkMemoryWarnings(heapUsagePercent, nonHeapUsagePercent);
            
            // 建議垃圾回收
            suggestGarbageCollection(heapUsagePercent);
            
        } catch (Exception e) {
            _log.error("監控記憶體時發生錯誤", e);
        }
    }
    
    /**
     * 檢查記憶體警告
     */
    private void checkMemoryWarnings(double heapUsagePercent, double nonHeapUsagePercent) {
        // 堆記憶體警告
        if (heapUsagePercent >= MEMORY_CRITICAL_THRESHOLD) {
            _log.error(String.format("堆記憶體使用率過高！當前使用率: %.2f%%", heapUsagePercent * 100));
            
            // 執行緊急清理
            performEmergencyCleanup();
            
        } else if (heapUsagePercent >= MEMORY_WARNING_THRESHOLD) {
            _log.warn(String.format("堆記憶體使用率較高，當前使用率: %.2f%%", heapUsagePercent * 100));
        }
        
        // 非堆記憶體警告
        if (nonHeapUsagePercent >= MEMORY_CRITICAL_THRESHOLD) {
            _log.error(String.format("非堆記憶體使用率過高！當前使用率: %.2f%%", nonHeapUsagePercent * 100));
        } else if (nonHeapUsagePercent >= MEMORY_WARNING_THRESHOLD) {
            _log.warn(String.format("非堆記憶體使用率較高，當前使用率: %.2f%%", nonHeapUsagePercent * 100));
        }
    }
    
    /**
     * 建議垃圾回收
     */
    private void suggestGarbageCollection(double heapUsagePercent) {
        long currentTime = System.currentTimeMillis();
        
        // 如果記憶體使用率超過警告閾值且距離上次建議GC超過間隔時間
        if (heapUsagePercent >= MEMORY_WARNING_THRESHOLD && 
            currentTime - _lastGcSuggestion > GC_SUGGESTION_INTERVAL * 1000) {
            
            _log.info("建議執行垃圾回收，當前堆記憶體使用率: " + String.format("%.2f%%", heapUsagePercent * 100));
            System.gc();
            _lastGcSuggestion = currentTime;
        }
    }
    
    /**
     * 執行緊急清理
     */
    private void performEmergencyCleanup() {
        _log.warn("執行緊急記憶體清理");
        
        try {
            // 清理快取
            CacheManager.getInstance().clearAllCache();
            
            // 強制垃圾回收
            System.gc();
            
            // 等待一下讓GC完成
            Thread.sleep(1000);
            
            // 再次檢查記憶體使用情況
            MemoryUsage heapUsage = _memoryBean.getHeapMemoryUsage();
            double newUsagePercent = (double) heapUsage.getUsed() / heapUsage.getMax();
            
            _log.info(String.format("緊急清理完成，當前堆記憶體使用率: %.2f%%", newUsagePercent * 100));
            
        } catch (Exception e) {
            _log.error("執行緊急清理時發生錯誤", e);
        }
    }
    
    /**
     * 獲取記憶體使用報告
     */
    public String getMemoryReport() {
        MemoryUsage heapUsage = _memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = _memoryBean.getNonHeapMemoryUsage();
        
        StringBuilder report = new StringBuilder();
        report.append("=== 記憶體使用報告 ===\n");
        
        // 堆記憶體
        report.append("堆記憶體:\n");
        report.append(String.format("  已使用: %d MB\n", heapUsage.getUsed() / 1024 / 1024));
        report.append(String.format("  已分配: %d MB\n", heapUsage.getCommitted() / 1024 / 1024));
        report.append(String.format("  最大值: %d MB\n", heapUsage.getMax() / 1024 / 1024));
        report.append(String.format("  使用率: %.2f%%\n", (double) heapUsage.getUsed() / heapUsage.getMax() * 100));
        
        // 非堆記憶體
        report.append("非堆記憶體:\n");
        report.append(String.format("  已使用: %d MB\n", nonHeapUsage.getUsed() / 1024 / 1024));
        report.append(String.format("  已分配: %d MB\n", nonHeapUsage.getCommitted() / 1024 / 1024));
        if (nonHeapUsage.getMax() > 0) {
            report.append(String.format("  最大值: %d MB\n", nonHeapUsage.getMax() / 1024 / 1024));
            report.append(String.format("  使用率: %.2f%%\n", (double) nonHeapUsage.getUsed() / nonHeapUsage.getMax() * 100));
        } else {
            report.append("  最大值: 無限制\n");
        }
        
        // 系統記憶體
        Runtime runtime = Runtime.getRuntime();
        report.append("系統記憶體:\n");
        report.append(String.format("  總記憶體: %d MB\n", runtime.totalMemory() / 1024 / 1024));
        report.append(String.format("  可用記憶體: %d MB\n", runtime.freeMemory() / 1024 / 1024));
        report.append(String.format("  最大記憶體: %d MB\n", runtime.maxMemory() / 1024 / 1024));
        
        return report.toString();
    }
    
    /**
     * 手動執行垃圾回收
     */
    public void performGarbageCollection() {
        _log.info("手動執行垃圾回收");
        
        MemoryUsage beforeGc = _memoryBean.getHeapMemoryUsage();
        long beforeUsed = beforeGc.getUsed();
        
        System.gc();
        
        // 等待GC完成
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        MemoryUsage afterGc = _memoryBean.getHeapMemoryUsage();
        long afterUsed = afterGc.getUsed();
        long freed = beforeUsed - afterUsed;
        
        _log.info(String.format("垃圾回收完成，釋放了 %d MB 記憶體", freed / 1024 / 1024));
    }
    
    /**
     * 檢查記憶體是否健康
     */
    public boolean isMemoryHealthy() {
        MemoryUsage heapUsage = _memoryBean.getHeapMemoryUsage();
        double heapUsagePercent = (double) heapUsage.getUsed() / heapUsage.getMax();
        
        return heapUsagePercent < MEMORY_WARNING_THRESHOLD;
    }
    
    /**
     * 獲取記憶體使用率
     */
    public double getHeapUsagePercent() {
        MemoryUsage heapUsage = _memoryBean.getHeapMemoryUsage();
        return (double) heapUsage.getUsed() / heapUsage.getMax();
    }
}
