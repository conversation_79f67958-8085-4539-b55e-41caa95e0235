package com.lineage.server.utils;

public class RangeLong {
	public static StringBuilder scount(final long count) {
		final String xcount = String.valueOf(count);
		final StringBuilder newCount = new StringBuilder();
		newCount.append(xcount);
		int x = xcount.length();
		int index = xcount.length() / 3;
		if (x % 3 == 0) {
			--index;
		}
		int i = 0;
		while (i < index) {
			x -= 3;
			newCount.insert(x, ",");
			++i;
		}
		return newCount;
	}
}
