package com.lineage.server.utils;

public class IntRange {
	private int _low;
	private int _high;

	public IntRange(final int low, final int high) {
		this._low = low;
		this._high = high;
	}

	public IntRange(final IntRange range) {
		this(range._low, range._high);
	}

	public boolean includes(final int i) {
		return this._low <= i && i <= this._high;
	}

	public static boolean includes(final int i, final int low, final int high) {
		return low <= i && i <= high;
	}

	public int ensure(final int i) {
		int r = i;
		r = ((this._low <= r) ? r : this._low);
		r = ((r <= this._high) ? r : this._high);
		return r;
	}

	public static int ensure(final int n, final int low, final int high) {
		int r = n;
		r = ((low <= r) ? r : low);
		r = ((r <= high) ? r : high);
		return r;
	}

	public int randomValue() {
		return Random.nextInt(this.getWidth() + 1) + this._low;
	}

	public int getLow() {
		return this._low;
	}

	public int getHigh() {
		return this._high;
	}

	public int getWidth() {
		return this._high - this._low;
	}

	@Override
	public boolean equals(final Object obj) {
		if (!(obj instanceof IntRange)) {
			return false;
		}
		final IntRange range = (IntRange) obj;
		return this._low == range._low && this._high == range._high;
	}

	@Override
	public String toString() {
		return "low=" + this._low + ", high=" + this._high;
	}
}
