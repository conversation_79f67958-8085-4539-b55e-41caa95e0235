package com.lineage.server.utils;

import java.net.ServerSocket;
import java.net.Socket;
import java.nio.channels.Selector;
import java.nio.channels.SelectionKey;
import java.io.IOException;
import java.io.Closeable;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class StreamUtil {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(StreamUtil.class);
	}

	public static void close(final Closeable... closeables) {
		final int length = closeables.length;
		int i = 0;
		while (i < length) {
			final Closeable c = closeables[i];
			try {
				if (c != null) {
					c.close();
				}
			} catch (IOException ex) {
			}
			++i;
		}
	}

	public static void close(final SelectionKey... keys) {
		final int length = keys.length;
		int i = 0;
		while (i < length) {
			final SelectionKey key = keys[i];
			if (key != null) {
				key.cancel();
			}
			++i;
		}
	}

	public static void close(final Selector... selectors) {
		final int length = selectors.length;
		int i = 0;
		while (i < length) {
			final Selector selector = selectors[i];
			try {
				if (selector != null) {
					selector.close();
				}
			} catch (IOException e) {
				StreamUtil._log.error("關閉Selector發生異常", e);
			}
			++i;
		}
	}

	public static void close(final Socket csocket) {
		try {
			if (!csocket.isClosed()) {
				csocket.shutdownInput();
				csocket.shutdownOutput();
				csocket.close();
			}
		} catch (IOException e) {
			StreamUtil._log.error("關閉Socket發生異常", e);
		}
	}

	public static void close(final ServerSocket server) {
		try {
			if (!server.isClosed()) {
				server.close();
			}
		} catch (IOException e) {
			StreamUtil._log.error("關閉ServerSocket發生異常", e);
		}
	}

	public static void interrupt(final Thread thread) {
		try {
			if (thread.isAlive()) {
				thread.interrupt();
			}
		} catch (Exception e) {
			StreamUtil._log.error("關閉Thread發生異常", e);
		}
	}
}
