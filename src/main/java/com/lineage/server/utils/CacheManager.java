package com.lineage.server.utils;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * 快取管理器
 * 提供統一的快取管理功能，減少資料庫查詢
 */
public class CacheManager {
    private static final Log _log = LogFactory.getLog(CacheManager.class);
    private static CacheManager _instance;
    
    // 不同類型的快取
    private final Map<String, CacheEntry> _itemCache = new ConcurrentHashMap<>();
    private final Map<String, CacheEntry> _npcCache = new ConcurrentHashMap<>();
    private final Map<String, CacheEntry> _skillCache = new ConcurrentHashMap<>();
    private final Map<String, CacheEntry> _characterCache = new ConcurrentHashMap<>();
    
    // 快取清理排程器
    private ScheduledExecutorService _cleanupScheduler;
    
    // 快取配置
    private static final long DEFAULT_TTL = 300000; // 5分鐘
    private static final long CLEANUP_INTERVAL = 60000; // 1分鐘清理一次
    private static final int MAX_CACHE_SIZE = 10000; // 最大快取項目數
    
    private CacheManager() {
        _cleanupScheduler = Executors.newScheduledThreadPool(1);
        startCleanupTask();
    }
    
    public static synchronized CacheManager getInstance() {
        if (_instance == null) {
            _instance = new CacheManager();
        }
        return _instance;
    }
    
    /**
     * 快取項目類
     */
    private static class CacheEntry {
        private final Object value;
        private final long timestamp;
        private final long ttl;
        
        public CacheEntry(Object value, long ttl) {
            this.value = value;
            this.timestamp = System.currentTimeMillis();
            this.ttl = ttl;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > ttl;
        }
        
        public Object getValue() {
            return value;
        }
    }
    
    /**
     * 物品快取
     */
    public void putItem(String key, Object value) {
        putItem(key, value, DEFAULT_TTL);
    }
    
    public void putItem(String key, Object value, long ttl) {
        if (_itemCache.size() >= MAX_CACHE_SIZE) {
            cleanupExpiredEntries(_itemCache);
        }
        _itemCache.put(key, new CacheEntry(value, ttl));
    }
    
    public Object getItem(String key) {
        CacheEntry entry = _itemCache.get(key);
        if (entry != null && !entry.isExpired()) {
            return entry.getValue();
        } else if (entry != null) {
            _itemCache.remove(key);
        }
        return null;
    }
    
    /**
     * NPC 快取
     */
    public void putNpc(String key, Object value) {
        putNpc(key, value, DEFAULT_TTL);
    }
    
    public void putNpc(String key, Object value, long ttl) {
        if (_npcCache.size() >= MAX_CACHE_SIZE) {
            cleanupExpiredEntries(_npcCache);
        }
        _npcCache.put(key, new CacheEntry(value, ttl));
    }
    
    public Object getNpc(String key) {
        CacheEntry entry = _npcCache.get(key);
        if (entry != null && !entry.isExpired()) {
            return entry.getValue();
        } else if (entry != null) {
            _npcCache.remove(key);
        }
        return null;
    }
    
    /**
     * 技能快取
     */
    public void putSkill(String key, Object value) {
        putSkill(key, value, DEFAULT_TTL);
    }
    
    public void putSkill(String key, Object value, long ttl) {
        if (_skillCache.size() >= MAX_CACHE_SIZE) {
            cleanupExpiredEntries(_skillCache);
        }
        _skillCache.put(key, new CacheEntry(value, ttl));
    }
    
    public Object getSkill(String key) {
        CacheEntry entry = _skillCache.get(key);
        if (entry != null && !entry.isExpired()) {
            return entry.getValue();
        } else if (entry != null) {
            _skillCache.remove(key);
        }
        return null;
    }
    
    /**
     * 角色快取
     */
    public void putCharacter(String key, Object value) {
        putCharacter(key, value, DEFAULT_TTL);
    }
    
    public void putCharacter(String key, Object value, long ttl) {
        if (_characterCache.size() >= MAX_CACHE_SIZE) {
            cleanupExpiredEntries(_characterCache);
        }
        _characterCache.put(key, new CacheEntry(value, ttl));
    }
    
    public Object getCharacter(String key) {
        CacheEntry entry = _characterCache.get(key);
        if (entry != null && !entry.isExpired()) {
            return entry.getValue();
        } else if (entry != null) {
            _characterCache.remove(key);
        }
        return null;
    }
    
    /**
     * 清除特定快取
     */
    public void clearItemCache() {
        _itemCache.clear();
        _log.info("物品快取已清除");
    }
    
    public void clearNpcCache() {
        _npcCache.clear();
        _log.info("NPC快取已清除");
    }
    
    public void clearSkillCache() {
        _skillCache.clear();
        _log.info("技能快取已清除");
    }
    
    public void clearCharacterCache() {
        _characterCache.clear();
        _log.info("角色快取已清除");
    }
    
    /**
     * 清除所有快取
     */
    public void clearAllCache() {
        _itemCache.clear();
        _npcCache.clear();
        _skillCache.clear();
        _characterCache.clear();
        _log.info("所有快取已清除");
    }
    
    /**
     * 獲取快取統計信息
     */
    public String getCacheStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== 快取統計信息 ===\n");
        stats.append("物品快取: ").append(_itemCache.size()).append(" 項目\n");
        stats.append("NPC快取: ").append(_npcCache.size()).append(" 項目\n");
        stats.append("技能快取: ").append(_skillCache.size()).append(" 項目\n");
        stats.append("角色快取: ").append(_characterCache.size()).append(" 項目\n");
        
        int totalSize = _itemCache.size() + _npcCache.size() + _skillCache.size() + _characterCache.size();
        stats.append("總計: ").append(totalSize).append(" 項目\n");
        stats.append("最大容量: ").append(MAX_CACHE_SIZE).append(" 項目/類型\n");
        
        return stats.toString();
    }
    
    /**
     * 啟動清理任務
     */
    private void startCleanupTask() {
        _cleanupScheduler.scheduleAtFixedRate(this::performCleanup, 
            CLEANUP_INTERVAL, CLEANUP_INTERVAL, TimeUnit.MILLISECONDS);
        _log.info("快取清理任務已啟動，間隔: " + CLEANUP_INTERVAL + "ms");
    }
    
    /**
     * 執行清理
     */
    private void performCleanup() {
        try {
            int totalCleaned = 0;
            totalCleaned += cleanupExpiredEntries(_itemCache);
            totalCleaned += cleanupExpiredEntries(_npcCache);
            totalCleaned += cleanupExpiredEntries(_skillCache);
            totalCleaned += cleanupExpiredEntries(_characterCache);
            
            if (totalCleaned > 0) {
                _log.debug("快取清理完成，清除了 " + totalCleaned + " 個過期項目");
            }
        } catch (Exception e) {
            _log.error("快取清理過程中發生錯誤", e);
        }
    }
    
    /**
     * 清理過期項目
     */
    private int cleanupExpiredEntries(Map<String, CacheEntry> cache) {
        int cleaned = 0;
        for (Map.Entry<String, CacheEntry> entry : cache.entrySet()) {
            if (entry.getValue().isExpired()) {
                cache.remove(entry.getKey());
                cleaned++;
            }
        }
        return cleaned;
    }
    
    /**
     * 停止快取管理器
     */
    public void shutdown() {
        if (_cleanupScheduler != null) {
            _cleanupScheduler.shutdown();
            try {
                if (!_cleanupScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    _cleanupScheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                _cleanupScheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        clearAllCache();
        _log.info("快取管理器已停止");
    }
}
