package com.lineage.server.utils;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.lang.management.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 效能監控工具
 * 監控記憶體使用、GC狀況、執行緒狀態等
 */
public class PerformanceMonitor {
    private static final Log _log = LogFactory.getLog(PerformanceMonitor.class);
    
    private static PerformanceMonitor _instance;
    private final ScheduledExecutorService _scheduler;
    
    // JMX Beans
    private final MemoryMXBean memoryBean;
    private final ThreadMXBean threadBean;
    private final RuntimeMXBean runtimeBean;
    private final OperatingSystemMXBean osBean;
    
    private long lastGcTime = 0;
    private long lastGcCount = 0;
    
    private PerformanceMonitor() {
        _scheduler = Executors.newScheduledThreadPool(1);
        
        memoryBean = ManagementFactory.getMemoryMXBean();
        threadBean = ManagementFactory.getThreadMXBean();
        runtimeBean = ManagementFactory.getRuntimeMXBean();
        osBean = ManagementFactory.getOperatingSystemMXBean();
    }
    
    public static PerformanceMonitor getInstance() {
        if (_instance == null) {
            _instance = new PerformanceMonitor();
        }
        return _instance;
    }
    
    /**
     * 開始效能監控
     * @param intervalMinutes 監控間隔 (分鐘)
     */
    public void startMonitoring(int intervalMinutes) {
        _log.info("啟動效能監控，間隔: " + intervalMinutes + " 分鐘");
        
        _scheduler.scheduleAtFixedRate(this::logPerformanceStats, 
            intervalMinutes, intervalMinutes, TimeUnit.MINUTES);
    }
    
    /**
     * 記錄效能統計
     */
    private void logPerformanceStats() {
        try {
            StringBuilder stats = new StringBuilder();
            stats.append("\n=== 效能監控報告 ===\n");
            
            // 記憶體使用情況
            MemoryUsage heapMemory = memoryBean.getHeapMemoryUsage();
            MemoryUsage nonHeapMemory = memoryBean.getNonHeapMemoryUsage();
            
            stats.append("記憶體使用:\n");
            stats.append(String.format("  堆記憶體: %s / %s (%.1f%%)\n",
                formatBytes(heapMemory.getUsed()),
                formatBytes(heapMemory.getMax()),
                (double) heapMemory.getUsed() / heapMemory.getMax() * 100));
            
            stats.append(String.format("  非堆記憶體: %s / %s\n",
                formatBytes(nonHeapMemory.getUsed()),
                formatBytes(nonHeapMemory.getMax() > 0 ? nonHeapMemory.getMax() : nonHeapMemory.getCommitted())));
            
            // GC 統計
            long totalGcTime = 0;
            long totalGcCount = 0;
            for (GarbageCollectorMXBean gcBean : ManagementFactory.getGarbageCollectorMXBeans()) {
                totalGcTime += gcBean.getCollectionTime();
                totalGcCount += gcBean.getCollectionCount();
            }
            
            stats.append("垃圾回收:\n");
            stats.append(String.format("  總次數: %d (新增: %d)\n", 
                totalGcCount, totalGcCount - lastGcCount));
            stats.append(String.format("  總時間: %d ms (新增: %d ms)\n", 
                totalGcTime, totalGcTime - lastGcTime));
            
            lastGcTime = totalGcTime;
            lastGcCount = totalGcCount;
            
            // 執行緒統計
            stats.append("執行緒:\n");
            stats.append(String.format("  活躍執行緒: %d\n", threadBean.getThreadCount()));
            stats.append(String.format("  峰值執行緒: %d\n", threadBean.getPeakThreadCount()));
            stats.append(String.format("  守護執行緒: %d\n", threadBean.getDaemonThreadCount()));
            
            // 系統負載
            if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
                com.sun.management.OperatingSystemMXBean sunOsBean = 
                    (com.sun.management.OperatingSystemMXBean) osBean;
                
                stats.append("系統負載:\n");
                stats.append(String.format("  CPU 使用率: %.1f%%\n", 
                    sunOsBean.getProcessCpuLoad() * 100));
                stats.append(String.format("  系統 CPU 使用率: %.1f%%\n", 
                    sunOsBean.getSystemCpuLoad() * 100));
                stats.append(String.format("  可用記憶體: %s\n", 
                    formatBytes(sunOsBean.getFreePhysicalMemorySize())));
            }
            
            // 運行時間
            long uptime = runtimeBean.getUptime();
            stats.append(String.format("運行時間: %s\n", formatUptime(uptime)));
            
            stats.append("==================");
            
            _log.info(stats.toString());
            
        } catch (Exception e) {
            _log.error("效能監控錯誤", e);
        }
    }
    
    /**
     * 格式化位元組數
     */
    private String formatBytes(long bytes) {
        if (bytes < 0) return "N/A";
        
        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", size, units[unitIndex]);
    }
    
    /**
     * 格式化運行時間
     */
    private String formatUptime(long uptimeMs) {
        long seconds = uptimeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d天 %d小時 %d分鐘", 
                days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d小時 %d分鐘", hours, minutes % 60);
        } else {
            return String.format("%d分鐘", minutes);
        }
    }
    
    /**
     * 獲取當前記憶體使用率
     */
    public double getMemoryUsagePercent() {
        MemoryUsage heapMemory = memoryBean.getHeapMemoryUsage();
        return (double) heapMemory.getUsed() / heapMemory.getMax() * 100;
    }
    
    /**
     * 檢查記憶體是否過高
     */
    public boolean isMemoryHigh(double threshold) {
        return getMemoryUsagePercent() > threshold;
    }
    
    /**
     * 強制執行垃圾回收 (謹慎使用)
     */
    public void forceGC() {
        _log.warn("強制執行垃圾回收");
        System.gc();
    }
    
    /**
     * 停止監控
     */
    public void shutdown() {
        _log.info("停止效能監控");
        _scheduler.shutdown();
    }
}
