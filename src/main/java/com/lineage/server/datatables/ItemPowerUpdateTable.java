package com.lineage.server.datatables;

import java.util.Iterator;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import com.lineage.DatabaseFactory;
import com.lineage.server.utils.PerformanceTimer;
import java.util.HashMap;
import org.apache.commons.logging.LogFactory;
import java.util.ArrayList;
import com.lineage.server.templates.L1ItemPowerUpdate;
import java.util.Map;
import org.apache.commons.logging.Log;

public class ItemPowerUpdateTable {
	private static final Log _log;
	private static Map<Integer, L1ItemPowerUpdate> _updateMap;
	private static final ArrayList<Integer> _updateitemidList;
	private static ItemPowerUpdateTable _instance;

	static {
		_log = LogFactory.getLog(ItemPowerUpdateTable.class);
		_updateMap = new HashMap();
		_updateitemidList = new ArrayList();
	}

	public static ItemPowerUpdateTable get() {
		if (ItemPowerUpdateTable._instance == null) {
			ItemPowerUpdateTable._instance = new ItemPowerUpdateTable();
		}
		return ItemPowerUpdateTable._instance;
	}

	public static void reload() {
		ItemPowerUpdateTable._instance = new ItemPowerUpdateTable();
		ItemPowerUpdateTable._updateMap.clear();
		ItemPowerUpdateTable._instance.load();
	}

	public void load() {
		final PerformanceTimer timer = new PerformanceTimer();
		Connection con = null;
		PreparedStatement pstm = null;
		ResultSet rs = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("SELECT * FROM `server_item_power_update`");
			rs = pstm.executeQuery();
			while (rs.next()) {
				final int itemid = rs.getInt("itemid");
				if (ItemTable.get().getTemplate(itemid) == null) {
					ItemPowerUpdateTable._log.error("特殊物品升級資料錯誤: 沒有這個編號的道具:" + itemid);
					delete(itemid);
				} else {
					final int nedid = rs.getInt("nedid");
					final int type_id = rs.getInt("type_id");
					final int order_id = rs.getInt("order_id");
					final int mode = rs.getInt("mode");
					final int random = rs.getInt("random");
					final String allmsg = rs.getString("世界廣播");
					L1ItemPowerUpdate value = ItemPowerUpdateTable._updateMap.get(Integer.valueOf(itemid));
					value = new L1ItemPowerUpdate();
					value.set_itemid(itemid);
					value.set_nedid(nedid);
					value.set_type_id(type_id);
					value.set_order_id(order_id);
					value.set_mode(mode);
					value.set_random(random);
					value.setallmsg(allmsg);
					ItemPowerUpdateTable._updateMap.put(Integer.valueOf(itemid), value);
					ItemPowerUpdateTable._updateitemidList.add(Integer.valueOf(itemid));
				}
			}
		} catch (SQLException e) {
			ItemPowerUpdateTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
		ItemPowerUpdateTable._log
				.info("載入物品升級資料數量: " + ItemPowerUpdateTable._updateMap.size() + "(" + timer.get() + "ms)");
	}

	public int get_original_type(final int itemid) {
		int type_id = 0;
		Connection con = null;
		PreparedStatement pstm = null;
		ResultSet rs = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("SELECT * FROM `server_item_power_update` WHERE `itemid` =?");
			pstm.setInt(1, itemid);
			rs = pstm.executeQuery();
			while (rs.next()) {
				type_id = rs.getInt("type_id");
			}
		} catch (SQLException e) {
			ItemPowerUpdateTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
		return type_id;
	}

	public int get_original_itemid(final int typeid) {
		int itemid = 0;
		Connection con = null;
		PreparedStatement pstm = null;
		ResultSet rs = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement(
					"SELECT * FROM `server_item_power_update` WHERE `type_id` =? AND `order_id` = '0'");
			pstm.setInt(1, typeid);
			rs = pstm.executeQuery();
			while (rs.next()) {
				itemid = rs.getInt("itemid");
			}
		} catch (SQLException e) {
			ItemPowerUpdateTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
		return itemid;
	}

	public ArrayList<Integer> get_updeatitemidlist() {
		return ItemPowerUpdateTable._updateitemidList;
	}

	public static void delete(final int itemid) {
		Connection cn = null;
		PreparedStatement ps = null;
		try {
			cn = DatabaseFactory.get().getConnection();
			ps = cn.prepareStatement("DELETE FROM `server_item_power_update` WHERE `itemid`=?");
			ps.setInt(1, itemid);
			ps.execute();
		} catch (SQLException e) {
			ItemPowerUpdateTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(ps);
			SQLUtil.close(cn);
		}
	}

	public Map<Integer, L1ItemPowerUpdate> get_type_updatemap(final String key) {
		final Map<Integer, L1ItemPowerUpdate> updateMap = new HashMap();
		final L1ItemPowerUpdate tmp = ItemPowerUpdateTable._updateMap.get(key);
		if (tmp != null) {
			final int type_id = tmp.get_type_id();
			final Iterator<L1ItemPowerUpdate> iterator = ItemPowerUpdateTable._updateMap.values().iterator();
			while (iterator.hasNext()) {
				final L1ItemPowerUpdate value = iterator.next();
				if (value.get_type_id() == type_id) {
					updateMap.put(Integer.valueOf(value.get_order_id()), value);
				}
			}
		}
		return updateMap;
	}

	public L1ItemPowerUpdate get(final int key) {
		return ItemPowerUpdateTable._updateMap.get(Integer.valueOf(key));
	}

	public L1ItemPowerUpdate gettemplate(final String key) {
		return ItemPowerUpdateTable._updateMap.get(key);
	}

	public Map<Integer, L1ItemPowerUpdate> map() {
		return ItemPowerUpdateTable._updateMap;
	}

	public Map<Integer, L1ItemPowerUpdate> get_type_id(final int itemid) {
		final Map<Integer, L1ItemPowerUpdate> updateMap = new HashMap();
		final L1ItemPowerUpdate tmp = ItemPowerUpdateTable._updateMap.get(Integer.valueOf(itemid));
		if (tmp != null) {
			final int type_id = tmp.get_type_id();
			final Iterator<L1ItemPowerUpdate> iterator = ItemPowerUpdateTable._updateMap.values().iterator();
			while (iterator.hasNext()) {
				final L1ItemPowerUpdate value = iterator.next();
				if (value.get_type_id() == type_id) {
					updateMap.put(Integer.valueOf(value.get_order_id()), value);
				}
			}
		}
		return updateMap;
	}
}
