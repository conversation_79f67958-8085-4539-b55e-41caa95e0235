package com.lineage.server.datatables;

import com.lineage.DatabaseFactory;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.*;
import com.lineage.server.templates.L1yinhaisawen;
import com.lineage.server.utils.PerformanceTimer;
import com.lineage.server.utils.SQLUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;


/**
 * 殷海薩紋樣
 */
public class yinhaisaTable {
    private static final Log _log = LogFactory.getLog(yinhaisaTable.class);
    private static final Map<Integer, L1yinhaisawen> _List = new HashMap<>();
    private static yinhaisaTable _instance;

    private yinhaisaTable() {
        load();
    }

    public static yinhaisaTable get() {
        if (_instance == null) {
			_instance = new yinhaisaTable();
		}
        return _instance;
    }

    public void reload() {
        _List.clear();
        load();
    }

    private void load() {
        PerformanceTimer timer;
        Connection con;
        PreparedStatement pstm;
        ResultSet rs;
        timer = new PerformanceTimer();
        con = null;
        pstm = null;
        rs = null;
        try {
            con = DatabaseFactory.get().getConnection();
            pstm = con.prepareStatement("SELECT * FROM `殷海薩紋樣屬性設定`");
            int yinhaisa;
            L1yinhaisawen yinhaisawenyang;
            for (rs = pstm.executeQuery(); rs.next(); _List.put(Integer.valueOf(yinhaisa), yinhaisawenyang)) {
                yinhaisa = rs.getInt("等級");
                int add_str = rs.getInt("力量");  // 力量
                int add_dex = rs.getInt("敏捷");  // 敏捷
                int add_con = rs.getInt("体质");  // 体质
                int add_int = rs.getInt("智力");  // 智力
                int add_wis = rs.getInt("精神");  // 精神
                int add_cha = rs.getInt("魅力");  // 魅力
                int add_ac = rs.getInt("防御");     // 防御
                int add_hp = rs.getInt("血量");     // 最大血量
                int add_mp = rs.getInt("魔量");     // 最大魔量
                int add_hpr = rs.getInt("回血量");     // 回写速度
                int add_mpr = rs.getInt("回魔量");     // 回魔速度
                int add_dmg = rs.getInt("近战伤害");     // 增加近战伤害
                int add_hit = rs.getInt("近战命中率");     // 增加近战命中率
                int add_bow_dmg = rs.getInt("远攻伤害");     // 增加远攻伤害
                int add_bow_hit = rs.getInt("远攻命中率");     // 增加远攻命中率
                int add_dmg_r = rs.getInt("物理伤害减免");      // 增加物理伤害减免
                int add_magic_r = rs.getInt("魔法伤害减免");     // 增加魔法伤害减免
                int add_mr = rs.getInt("魔防");    // 增加抗魔
                int add_sp = rs.getInt("魔攻");    // 增加魔法攻击
                int add_fire = rs.getInt("火属性");    // 增加火属性
                int add_wind = rs.getInt("风属性");    // 增加封属性
                int add_earth = rs.getInt("地属性");    // 增加地属性
                int add_water = rs.getInt("水属性");    // 增加水属性
                int add_stun = rs.getInt("昏迷耐性");    // 增加耐昏迷
                int add_stone = rs.getInt("石化耐性");    // 增加耐石化
                int add_sleep = rs.getInt("睡眠耐性");    // 增加耐睡眠
                int add_freeze = rs.getInt("寒冰耐性"); //增加耐冰
                int add_sustain = rs.getInt("支撑耐性"); // 增加耐支撑
                int add_blind = rs.getInt("暗黑耐性");    // 增加耐暗黑
                int add_exp = rs.getInt("经验%");  // 增加经验率  2 = 2倍

                yinhaisawenyang = new L1yinhaisawen();
                yinhaisawenyang.set_add_str(add_str);
                yinhaisawenyang.set_add_dex(add_dex);
                yinhaisawenyang.set_add_con(add_con);
                yinhaisawenyang.set_add_int(add_int);
                yinhaisawenyang.set_add_wis(add_wis);
                yinhaisawenyang.set_add_cha(add_cha);
                yinhaisawenyang.set_add_ac(add_ac);
                yinhaisawenyang.set_add_hp(add_hp);
                yinhaisawenyang.set_add_mp(add_mp);
                yinhaisawenyang.set_add_hpr(add_hpr);
                yinhaisawenyang.set_add_mpr(add_mpr);
                yinhaisawenyang.set_add_dmg(add_dmg);
                yinhaisawenyang.set_add_hit(add_hit);
                yinhaisawenyang.set_add_bow_dmg(add_bow_dmg);
                yinhaisawenyang.set_add_bow_hit(add_bow_hit);
                yinhaisawenyang.set_add_dmg_r(add_dmg_r);
                yinhaisawenyang.set_add_magic_r(add_magic_r);
                yinhaisawenyang.set_add_mr(add_mr);
                yinhaisawenyang.set_add_sp(add_sp);
                yinhaisawenyang.set_add_fire(add_fire);
                yinhaisawenyang.set_add_wind(add_wind);
                yinhaisawenyang.set_add_earth(add_earth);
                yinhaisawenyang.set_add_water(add_water);
                yinhaisawenyang.set_add_stun(add_stun);
                yinhaisawenyang.set_add_stone(add_stone);
                yinhaisawenyang.set_add_sleep(add_sleep);
                yinhaisawenyang.set_add_freeze(add_freeze);
                yinhaisawenyang.set_add_sustain(add_sustain);
                yinhaisawenyang.set_add_blind(add_blind);
                yinhaisawenyang.set_add_exp(add_exp);

                _List.put(yinhaisa, yinhaisawenyang);

            }

        } catch (SQLException e) {
            _log.error(e.getLocalizedMessage(), e);
        }

        SQLUtil.close(rs);
        SQLUtil.close(pstm);
        SQLUtil.close(con);
        _log.info((new StringBuilder("讀取->殷海薩紋樣屬性數量: ")).append(_List.size()).append("(").append(timer.get()).append("ms)").toString());
        return;
    }

    /**
     * 增加效果
     */
    public void addyinhaisa(L1PcInstance pc, int yinhaisa) {
        if (_List.isEmpty()) {
            return;
        }
        if (!_List.containsKey(Integer.valueOf(yinhaisa))) {
            return;
        }
        L1yinhaisawen vip = (L1yinhaisawen) _List.get(Integer.valueOf(yinhaisa));
        final boolean status = false;
        boolean status2 = false;
        boolean spmr = false;
        boolean attr = false;

        int add_str = vip.get_add_str();
        if (add_str != 0) {
            pc.addStr(add_str);
            status2 = true;
        }
        int add_dex = vip.get_add_dex();
        if (add_dex != 0) {
            pc.addDex(add_dex);
            status2 = true;
        }
        int add_con = vip.get_add_con();
        if (add_con != 0) {
            pc.addCon(add_con);
            status2 = true;
        }
        int add_int = vip.get_add_int();
        if (add_int != 0) {
            pc.addInt(add_int);
            status2 = true;
        }
        int add_wis = vip.get_add_wis();
        if (add_wis != 0) {
            pc.addWis(add_wis);
            status2 = true;
        }
        int add_cha = vip.get_add_cha();
        if (add_cha != 0) {
            pc.addCha(add_cha);
            status2 = true;
        }
        int add_ac = vip.get_add_ac();
        if (add_ac != 0) {
            pc.addAc(-add_ac);
            attr = true;
        }
        int add_hp = vip.get_add_hp();
        if (add_hp != 0) {
            pc.addMaxHp(add_hp);
            pc.sendPackets(new S_HPUpdate(pc.getCurrentHp(), pc.getMaxHp()));
            if (pc.isInParty()) {
                pc.getParty().updateMiniHP(pc);
            }
        }
        int add_mp = vip.get_add_mp();
        if (add_mp != 0) {
            pc.addMaxMp(add_mp);
            pc.sendPackets(new S_MPUpdate(pc.getCurrentMp(), pc.getMaxMp()));
        }
        int add_hpr = vip.get_add_hpr();
        if (add_hpr != 0) {
			pc.addHpr(add_hpr);
		}
        int add_mpr = vip.get_add_mpr();
        if (add_mpr != 0) {
			pc.addMpr(add_mpr);
		}
        int add_dmg = vip.get_add_dmg();
        if (add_dmg != 0) {
			pc.addDmgup(add_dmg);
		}
        int add_hit = vip.get_add_hit();
        if (add_hit != 0) {
			pc.addHitup(add_hit);
		}
        int add_bow_dmg = vip.get_add_bow_dmg();
        if (add_bow_dmg != 0) {
			pc.addBowDmgup(add_bow_dmg);
		}
        int add_bow_hit = vip.get_add_bow_hit();
        if (add_bow_hit != 0) {
			pc.addBowHitup(add_bow_hit);
		}
        int add_mr = vip.get_add_mr();
        if (add_mr != 0) {
            pc.addMr(add_mr);
            spmr = true;
        }
        int add_sp = vip.get_add_sp();
        if (add_sp != 0) {
            pc.addSp(add_sp);
            spmr = true;
        }
        int add_fire = vip.get_add_fire();
        if (add_fire != 0) {
            pc.addFire(add_fire);
            attr = true;
        }
        int add_wind = vip.get_add_wind();
        if (add_wind != 0) {
            pc.addWind(add_wind);
            attr = true;
        }
        int add_earth = vip.get_add_earth();
        if (add_earth != 0) {
            pc.addEarth(add_earth);
            attr = true;
        }
        int add_water = vip.get_add_water();
        if (add_water != 0) {
            pc.addWater(add_water);
            attr = true;
        }
        int add_stun = vip.get_add_stun();
        if (add_stun != 0) {
			pc.addRegistStun(add_stun);
		}
        int add_stone = vip.get_add_stone();
        if (add_stone != 0) {
			pc.addRegistStone(add_stone);
		}
        int add_sleep = vip.get_add_sleep();
        if (add_sleep != 0) {
			pc.addRegistSleep(add_sleep);
		}

        int add_sustain = vip.get_add_sustain();
        if (add_sustain != 0) {
			pc.addRegistSustain(add_sustain);
		}
        int add_blind = vip.get_add_blind();
        if (add_blind != 0) {
			pc.addRegistBlind(add_blind);
		}

        int add_exp = vip.get_add_exp();
        if (add_exp != 0) {
            pc.addExpRateToPc(add_exp);
        }


        if (status) {
            pc.sendPackets(new S_OwnCharStatus(pc));
        } else {
            if (status2) {
				pc.sendPackets(new S_OwnCharStatus2(pc));
			}
            if (attr) {
				pc.sendPackets(new S_OwnCharAttrDef(pc));
			}
        }
        if (spmr) {
			pc.sendPackets(new S_SPMR(pc));
		}
    }

    /**
     * 移除效果
     */
    public void setyinhaisa(L1PcInstance pc, int yinhaisa) {
        if (_List.isEmpty()) {
            return;
        }
        if (!_List.containsKey(Integer.valueOf(yinhaisa))) {
            return;
        }
        L1yinhaisawen vip = (L1yinhaisawen) _List.get(Integer.valueOf(yinhaisa));
        final boolean status = false;
        boolean status2 = false;
        boolean spmr = false;
        boolean attr = false;

        int add_str = vip.get_add_str();
        if (add_str != 0) {
            pc.addStr(-add_str);
            status2 = true;
        }
        int add_dex = vip.get_add_dex();
        if (add_dex != 0) {
            pc.addDex(-add_dex);
            status2 = true;
        }
        int add_con = vip.get_add_con();
        if (add_con != 0) {
            pc.addCon(-add_con);
            status2 = true;
        }
        int add_int = vip.get_add_int();
        if (add_int != 0) {
            pc.addInt(-add_int);
            status2 = true;
        }
        int add_wis = vip.get_add_wis();
        if (add_wis != 0) {
            pc.addWis(-add_wis);
            status2 = true;
        }
        int add_cha = vip.get_add_cha();
        if (add_cha != 0) {
            pc.addCha(-add_cha);
            status2 = true;
        }
        int add_ac = vip.get_add_ac();
        if (add_ac != 0) {
            pc.addAc(add_ac);
            attr = true;
        }
        int add_hp = vip.get_add_hp();
        if (add_hp != 0) {
            pc.addMaxHp(-add_hp);
            pc.sendPackets(new S_HPUpdate(pc.getCurrentHp(), pc.getMaxHp()));
            if (pc.isInParty()) {
                pc.getParty().updateMiniHP(pc);
            }
        }
        int add_mp = vip.get_add_mp();
        if (add_mp != 0) {
            pc.addMaxMp(-add_mp);
            pc.sendPackets(new S_MPUpdate(pc.getCurrentMp(), pc.getMaxMp()));
        }
        int add_hpr = vip.get_add_hpr();
        if (add_hpr != 0) {
			pc.addHpr(-add_hpr);
		}
        int add_mpr = vip.get_add_mpr();
        if (add_mpr != 0) {
			pc.addMpr(-add_mpr);
		}
        int add_dmg = vip.get_add_dmg();
        if (add_dmg != 0) {
			pc.addDmgup(-add_dmg);
		}
        int add_hit = vip.get_add_hit();
        if (add_hit != 0) {
			pc.addHitup(-add_hit);
		}
        int add_bow_dmg = vip.get_add_bow_dmg();
        if (add_bow_dmg != 0) {
			pc.addBowDmgup(-add_bow_dmg);
		}
        int add_bow_hit = vip.get_add_bow_hit();
        if (add_bow_hit != 0) {
			pc.addBowHitup(-add_bow_hit);
		}
        int add_mr = vip.get_add_mr();
        if (add_mr != 0) {
            pc.addMr(-add_mr);
            spmr = true;
        }
        int add_sp = vip.get_add_sp();
        if (add_sp != 0) {
            pc.addSp(-add_sp);
            spmr = true;
        }
        int add_fire = vip.get_add_fire();
        if (add_fire != 0) {
            pc.addFire(-add_fire);
            attr = true;
        }
        int add_wind = vip.get_add_wind();
        if (add_wind != 0) {
            pc.addWind(-add_wind);
            attr = true;
        }
        int add_earth = vip.get_add_earth();
        if (add_earth != 0) {
            pc.addEarth(-add_earth);
            attr = true;
        }
        int add_water = vip.get_add_water();
        if (add_water != 0) {
            pc.addWater(-add_water);
            attr = true;
        }
        int add_stun = vip.get_add_stun();
        if (add_stun != 0) {
			pc.addRegistStun(-add_stun);
		}
        int add_stone = vip.get_add_stone();
        if (add_stone != 0) {
			pc.addRegistStone(-add_stone);
		}
        int add_sleep = vip.get_add_sleep();
        if (add_sleep != 0) {
			pc.addRegistSleep(-add_sleep);
		}

        int add_sustain = vip.get_add_sustain();
        if (add_sustain != 0) {
			pc.addRegistSustain(-add_sustain);
		}
        int add_blind = vip.get_add_blind();
        if (add_blind != 0) {
			pc.addRegistBlind(-add_blind);
		}

        int add_exp = vip.get_add_exp();
        if (add_exp != 0) {
            pc.addExpRateToPc(-add_exp);
        }


        if (status) {
            pc.sendPackets(new S_OwnCharStatus(pc));
        } else {
            if (status2) {
				pc.sendPackets(new S_OwnCharStatus2(pc));
			}
            if (attr) {
				pc.sendPackets(new S_OwnCharAttrDef(pc));
			}
        }
        if (spmr) {
			pc.sendPackets(new S_SPMR(pc));
		}
    }

    public L1yinhaisawen getTemplate(int yinhaisa) {
        return _List.get(yinhaisa);
    }

}
