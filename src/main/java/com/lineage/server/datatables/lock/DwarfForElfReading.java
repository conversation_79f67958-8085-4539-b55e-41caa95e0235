package com.lineage.server.datatables.lock;

import com.lineage.server.model.Instance.L1ItemInstance;
import java.util.concurrent.CopyOnWriteArrayList;
import com.lineage.server.datatables.sql.DwarfForElfTable;
import java.util.concurrent.locks.ReentrantLock;
import com.lineage.server.datatables.storage.DwarfForElfStorage;
import java.util.concurrent.locks.Lock;

public class DwarfForElfReading {
	private final Lock _lock;
	private final DwarfForElfStorage _storage;
	private static DwarfForElfReading _instance;

	private DwarfForElfReading() {
		this._lock = new ReentrantLock(true);
		this._storage = new DwarfForElfTable();
	}

	public static DwarfForElfReading get() {
		if (DwarfForElfReading._instance == null) {
			DwarfForElfReading._instance = new DwarfForElfReading();
		}
		return DwarfForElfReading._instance;
	}

	public void load() {
		this._lock.lock();
		try {
			this._storage.load();
		} finally {
			this._lock.unlock();
		}
	}

	public CopyOnWriteArrayList<L1ItemInstance> loadItems(final String account_name) {
		this._lock.lock();
		CopyOnWriteArrayList tmp = null;
		try {
			tmp = this._storage.loadItems(account_name);
		} finally {
			this._lock.unlock();
		}
		return tmp;
	}

	public void delUserItems(final String account_name) {
		this._lock.lock();
		try {
			this._storage.delUserItems(account_name);
		} finally {
			this._lock.unlock();
		}
	}

	public void insertItem(final String account_name, final L1ItemInstance item) {
		this._lock.lock();
		try {
			this._storage.insertItem(account_name, item);
		} finally {
			this._lock.unlock();
		}
	}

	public void updateItem(final L1ItemInstance item) {
		this._lock.lock();
		try {
			this._storage.updateItem(item);
		} finally {
			this._lock.unlock();
		}
	}

	public void deleteItem(final String account_name, final L1ItemInstance item) {
		this._lock.lock();
		try {
			this._storage.deleteItem(account_name, item);
		} finally {
			this._lock.unlock();
		}
	}

	public boolean getUserItems(final String account_name, final int objid, final int count) {
		this._lock.lock();
		boolean tmp = false;
		try {
			tmp = this._storage.getUserItems(account_name, objid, count);
		} finally {
			this._lock.unlock();
		}
		return tmp;
	}
}
