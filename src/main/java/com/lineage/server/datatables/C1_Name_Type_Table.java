package com.lineage.server.datatables;

import java.lang.reflect.InvocationTargetException;
import com.lineage.server.model.c1.C1Executor;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import com.lineage.DatabaseFactory;
import com.lineage.server.utils.PerformanceTimer;
import org.apache.commons.logging.LogFactory;
import com.lineage.server.templates.L1Name_Power;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.logging.Log;

public class C1_Name_Type_Table {
	private static final Log _log;
	private static final Map<Integer, HashMap<Integer, L1Name_Power>> _types;
	private static final Map<Integer, HashMap<Integer, Integer>> _typesLv;
	private static final Map<Integer, HashMap<Integer, Integer>> _typesLv_down;
	private static C1_Name_Type_Table _instance;

	static {
		_log = LogFactory.getLog(C1_Name_Type_Table.class);
		_types = new HashMap();
		_typesLv = new HashMap();
		_typesLv_down = new HashMap();
	}

	public static C1_Name_Type_Table get() {
		if (C1_Name_Type_Table._instance == null) {
			C1_Name_Type_Table._instance = new C1_Name_Type_Table();
		}
		return C1_Name_Type_Table._instance;
	}

	public void load() {
		final PerformanceTimer timer = new PerformanceTimer();
		Connection con = null;
		PreparedStatement pstm = null;
		ResultSet rs = null;
		int i = 0;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("SELECT * FROM `server_c1_name_type`");
			rs = pstm.executeQuery();
			while (rs.next()) {
				final int c1_id = rs.getInt("c1_id");
				final int c1_type = rs.getInt("c1_type");
				final String c1_name_type = rs.getString("c1_name_type");
				final String c1_classname = rs.getString("c1_classname");
				final int set = rs.getInt("set");
				final int down = rs.getInt("down");
				final int int1 = rs.getInt("int1");
				final int int2 = rs.getInt("int2");
				final int int3 = rs.getInt("int3");
				final int int4 = rs.getInt("int4");
				final String c1_name_title = rs.getString("c1_name_title");
				final int getscore = rs.getInt("getscore");
				final L1Name_Power power = new L1Name_Power();
				power.set_c1_id(c1_id);
				power.set_c1_name_type(c1_name_type);
				power.set_c1_name_title(c1_name_title);
				final C1Executor classname = this.power(c1_classname, int1, int2, int3, int4);
				if (classname != null) {
					power.set_c1_classname(classname);
					power.set_set(set);
					power.set_down(down);
					power.set_getscore(getscore);
					HashMap<Integer, L1Name_Power> types = C1_Name_Type_Table._types.get(Integer.valueOf(c1_type));
					if (types == null) {
						types = new HashMap();
					}
					types.put(Integer.valueOf(c1_id), power);
					HashMap<Integer, Integer> typesLv = C1_Name_Type_Table._typesLv.get(Integer.valueOf(c1_type));
					if (typesLv == null) {
						typesLv = new HashMap();
					}
					typesLv.put(Integer.valueOf(c1_id), Integer.valueOf(set));
					HashMap<Integer, Integer> typesLv_down = C1_Name_Type_Table._typesLv_down
							.get(Integer.valueOf(c1_type));
					if (typesLv_down == null) {
						typesLv_down = new HashMap();
					}
					typesLv_down.put(Integer.valueOf(c1_id), Integer.valueOf(down));
					C1_Name_Type_Table._types.put(Integer.valueOf(c1_type), types);
					C1_Name_Type_Table._typesLv.put(Integer.valueOf(c1_type), typesLv);
					++i;
				}
			}
		} catch (SQLException e) {
			C1_Name_Type_Table._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
		C1_Name_Type_Table._log.info("載入陣營階級能力記錄數量: " + i + "(" + timer.get() + "ms)");
	}

	public L1Name_Power get(final int key1, final int key2) {
		final HashMap<Integer, L1Name_Power> powers = C1_Name_Type_Table._types.get(Integer.valueOf(key1));
		if (powers != null) {
			return powers.get(Integer.valueOf(key2));
		}
		return null;
	}

	public HashMap<Integer, L1Name_Power> get(final int key1) {
		final HashMap<Integer, L1Name_Power> powers = C1_Name_Type_Table._types.get(Integer.valueOf(key1));
		if (powers != null) {
			return powers;
		}
		return null;
	}

	public int getLv(final int key1, final int score) {
		final HashMap<Integer, Integer> powers = C1_Name_Type_Table._typesLv.get(Integer.valueOf(key1));
		if (powers == null) {
			return 0;
		}
		int i = powers.size();
		while (i > 0) {
			final Integer ps = powers.get(Integer.valueOf(i));
			if (score >= ps.intValue()) {
				return i;
			}
			--i;
		}
		return 0;
	}

	private C1Executor power(final String className, final int int1, final int int2, final int int3, final int int4) {
		if (className.equals("0")) {
			return null;
		}
		try {
			final StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append("com.lineage.server.model.c1.");
			stringBuilder.append(className);
			final Class<?> cls = Class.forName(stringBuilder.toString());
			final C1Executor exe = (C1Executor) cls.getMethod("get", new Class[0]).invoke(null, new Object[0]);
			exe.set_power(int1, int2, int3, int4);
			return exe;
		} catch (ClassNotFoundException e6) {
			final String error = "發生[陣營階級能力檔案]錯誤, 檢查檔案是否存在:" + className;
			C1_Name_Type_Table._log.error(error);
		} catch (IllegalArgumentException e) {
			C1_Name_Type_Table._log.error(e.getLocalizedMessage(), e);
		} catch (IllegalAccessException e2) {
			C1_Name_Type_Table._log.error(e2.getLocalizedMessage(), e2);
		} catch (InvocationTargetException e3) {
			C1_Name_Type_Table._log.error(e3.getLocalizedMessage(), e3);
		} catch (SecurityException e4) {
			C1_Name_Type_Table._log.error(e4.getLocalizedMessage(), e4);
		} catch (NoSuchMethodException e5) {
			C1_Name_Type_Table._log.error(e5.getLocalizedMessage(), e5);
		}
		return null;
	}
}
