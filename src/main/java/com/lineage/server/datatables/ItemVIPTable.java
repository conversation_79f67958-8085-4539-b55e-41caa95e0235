package com.lineage.server.datatables;

import com.lineage.server.serverpackets.S_OwnCharPack;
import com.lineage.server.model.Instance.L1SkinInstance;
import com.lineage.server.serverpackets.S_SPMR;
import com.lineage.server.serverpackets.S_OwnCharAttrDef;
import com.lineage.server.serverpackets.S_OwnCharStatus2;
import com.lineage.server.serverpackets.S_OwnCharStatus;
import com.lineage.server.serverpackets.S_PacketBox;
import com.lineage.server.utils.L1SpawnUtil;
import com.lineage.server.serverpackets.S_MPUpdate;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_HPUpdate;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.L1PolyMorph;
import com.lineage.server.model.Instance.L1PcInstance;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import com.lineage.DatabaseFactory;
import com.lineage.server.utils.PerformanceTimer;
import java.util.HashMap;
import org.apache.commons.logging.LogFactory;
import com.lineage.server.templates.L1ItemVIP;
import java.util.Map;
import org.apache.commons.logging.Log;

public class ItemVIPTable {
	private static final Log _log;
	private static final Map<Integer, L1ItemVIP> _VIPList;
	private static ItemVIPTable _instance;

	static {
		_log = LogFactory.getLog(ItemVIPTable.class);
		_VIPList = new HashMap();
	}

	public static ItemVIPTable get() {
		if (ItemVIPTable._instance == null) {
			ItemVIPTable._instance = new ItemVIPTable();
		}
		return ItemVIPTable._instance;
	}

	private ItemVIPTable() {
		this.load();
	}

	private void load() {
		final PerformanceTimer timer = new PerformanceTimer();
		Connection con = null;
		PreparedStatement pstm = null;
		ResultSet rs = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("SELECT * FROM `w_指定道具賦予狀態`");
			rs = pstm.executeQuery();
			while (rs.next()) {
				final int item_id = rs.getInt("item_id");
				final int type = rs.getInt("type");
				final int classid = rs.getInt("classid");
				final int add_wmd = rs.getInt("weapon_dmg");
				final int add_wmc = rs.getInt("weapon_pro");
				final int add_str = rs.getInt("add_str");
				final int add_dex = rs.getInt("add_dex");
				final int add_con = rs.getInt("add_con");
				final int add_int = rs.getInt("add_int");
				final int add_wis = rs.getInt("add_wis");
				final int add_cha = rs.getInt("add_cha");
				final int add_ac = rs.getInt("add_ac");
				final int add_hp = rs.getInt("add_hp");
				final int add_mp = rs.getInt("add_mp");
				final int add_hpr = rs.getInt("add_hpr");
				final int add_mpr = rs.getInt("add_mpr");
				final int add_dmg = rs.getInt("add_dmg");
				final int add_hit = rs.getInt("add_hit");
				final int add_bow_dmg = rs.getInt("add_bow_dmg");
				final int add_bow_hit = rs.getInt("add_bow_hit");
				final int add_dmg_r = rs.getInt("add_dmg_r");
				final int add_magic_r = rs.getInt("add_magic_r");
				final int add_mr = rs.getInt("add_mr");
				final int add_sp = rs.getInt("add_sp");
				final int add_fire = rs.getInt("add_fire");
				final int add_wind = rs.getInt("add_wind");
				final int add_earth = rs.getInt("add_earth");
				final int add_water = rs.getInt("add_water");
				final int add_stun = rs.getInt("add_stun");
				final int add_stone = rs.getInt("add_stone");
				final int add_sleep = rs.getInt("add_sleep");
				final int add_freeze = rs.getInt("add_freeze");
				final int add_sustain = rs.getInt("add_sustain");
				final int add_blind = rs.getInt("add_blind");
				final int add_exp = rs.getInt("add_exp");
				final int add_adena = rs.getInt("add_gf");
				final int effect_icon = rs.getInt("effect_icon");
				final String vipname = rs.getString("顯示稱號");
				final int polyId = rs.getInt("polyId");
				final int polyTime = rs.getInt("polyTime");
				int skin_id = 0;
				skin_id = rs.getInt("skin_id");
				final L1ItemVIP vip = new L1ItemVIP();
				vip.set_type(type);
				vip.set_classid(classid);
				vip.set_add_wmd(add_wmd);
				vip.set_add_wmc(add_wmc);
				vip.set_add_str(add_str);
				vip.set_add_dex(add_dex);
				vip.set_add_con(add_con);
				vip.set_add_int(add_int);
				vip.set_add_wis(add_wis);
				vip.set_add_cha(add_cha);
				vip.set_add_ac(add_ac);
				vip.set_add_hp(add_hp);
				vip.set_add_mp(add_mp);
				vip.set_add_hpr(add_hpr);
				vip.set_add_mpr(add_mpr);
				vip.set_add_dmg(add_dmg);
				vip.set_add_hit(add_hit);
				vip.set_add_bow_dmg(add_bow_dmg);
				vip.set_add_bow_hit(add_bow_hit);
				vip.set_add_dmg_r(add_dmg_r);
				vip.set_add_magic_r(add_magic_r);
				vip.set_add_mr(add_mr);
				vip.set_add_sp(add_sp);
				vip.set_add_fire(add_fire);
				vip.set_add_wind(add_wind);
				vip.set_add_earth(add_earth);
				vip.set_add_water(add_water);
				vip.set_add_stun(add_stun);
				vip.set_add_stone(add_stone);
				vip.set_add_sleep(add_sleep);
				vip.set_add_freeze(add_freeze);
				vip.set_add_sustain(add_sustain);
				vip.set_add_blind(add_blind);
				vip.set_add_exp(add_exp);
				vip.set_effect_icon(effect_icon);
				vip.set_skin_id(skin_id);
				vip.set_add_adena(add_adena);
				vip.set_vipname(vipname);
				vip.set_polyId(polyId);
				vip.set_polyTime(polyTime);
				ItemVIPTable._VIPList.put(Integer.valueOf(item_id), vip);
			}
		} catch (SQLException e) {
			ItemVIPTable._log.error(e.getLocalizedMessage(), e);
		}
		SQLUtil.close(rs);
		SQLUtil.close(pstm);
		SQLUtil.close(con);
		SQLUtil.close(rs);
		SQLUtil.close(pstm);
		SQLUtil.close(con);
		SQLUtil.close(rs);
		SQLUtil.close(pstm);
		SQLUtil.close(con);
		ItemVIPTable._log.info("載入VIP道具加值數量: " + ItemVIPTable._VIPList.size() + "(" + timer.get() + "ms)");
	}

	public L1ItemVIP getVIP(final int item_id) {
		if (ItemVIPTable._VIPList.isEmpty()) {
			return null;
		}
		if (ItemVIPTable._VIPList.containsKey(Integer.valueOf(item_id))) {
			return ItemVIPTable._VIPList.get(Integer.valueOf(item_id));
		}
		return null;
	}

	public boolean checkVIP(final int item_id) {
		return ItemVIPTable._VIPList.containsKey(Integer.valueOf(item_id));
	}

	public static boolean checkVIP1(final int item_id) {
		return ItemVIPTable._VIPList.containsKey(Integer.valueOf(item_id));
	}

	public void addItemVIP(final L1PcInstance pc, final int item_id) {
		if (ItemVIPTable._VIPList.isEmpty()) {
			return;
		}
		if (!ItemVIPTable._VIPList.containsKey(Integer.valueOf(item_id))) {
			return;
		}
		final L1ItemVIP vip = ItemVIPTable._VIPList.get(Integer.valueOf(item_id));
		final boolean status = false;
		boolean status2 = false;
		boolean spmr = false;
		boolean attr = false;
		if (vip.get_polyId() != 0) {
			L1PolyMorph.doPoly(pc, vip.get_polyId(), vip.get_polyTime(), 1);
		}
		if (vip.getvipname() != null) {
			pc.setvipname(vip.getvipname());
			pc.setvipname1();
		}
		final int add_wmd = vip.get_add_wmd();
		if (add_wmd != 0) {
			pc.addweaponMD(add_wmd);
			status2 = true;
		}
		final int add_wmc = vip.get_add_wmc();
		if (add_wmc != 0) {
			pc.addweaponMDC(add_wmc);
			status2 = true;
		}
		final int add_str = vip.get_add_str();
		if (add_str != 0) {
			pc.addStr(add_str);
			status2 = true;
		}
		final int add_dex = vip.get_add_dex();
		if (add_dex != 0) {
			pc.addDex(add_dex);
			status2 = true;
		}
		final int add_con = vip.get_add_con();
		if (add_con != 0) {
			pc.addCon(add_con);
			status2 = true;
		}
		final int add_int = vip.get_add_int();
		if (add_int != 0) {
			pc.addInt(add_int);
			status2 = true;
		}
		final int add_wis = vip.get_add_wis();
		if (add_wis != 0) {
			pc.addWis(add_wis);
			status2 = true;
		}
		final int add_cha = vip.get_add_cha();
		if (add_cha != 0) {
			pc.addCha(add_cha);
			status2 = true;
		}
		final int add_ac = vip.get_add_ac();
		if (add_ac != 0) {
			pc.addAc(-add_ac);
			attr = true;
		}
		final int add_hp = vip.get_add_hp();
		if (add_hp != 0) {
			pc.addMaxHp(add_hp);
			pc.sendPackets(new S_HPUpdate(pc.getCurrentHp(), pc.getMaxHp()));
			if (pc.isInParty()) {
				pc.getParty().updateMiniHP(pc);
			}
		}
		final int add_mp = vip.get_add_mp();
		if (add_mp != 0) {
			pc.addMaxMp(add_mp);
			pc.sendPackets(new S_MPUpdate(pc.getCurrentMp(), pc.getMaxMp()));
		}
		final int add_hpr = vip.get_add_hpr();
		if (add_hpr != 0) {
			pc.addHpr(add_hpr);
		}
		final int add_mpr = vip.get_add_mpr();
		if (add_mpr != 0) {
			pc.addMpr(add_mpr);
		}
		final int add_dmg = vip.get_add_dmg();
		if (add_dmg != 0) {
			pc.addDmgup(add_dmg);
		}
		final int add_hit = vip.get_add_hit();
		if (add_hit != 0) {
			pc.addHitup(add_hit);
		}
		final int add_bow_dmg = vip.get_add_bow_dmg();
		if (add_bow_dmg != 0) {
			pc.addBowDmgup(add_bow_dmg);
		}
		final int add_bow_hit = vip.get_add_bow_hit();
		if (add_bow_hit != 0) {
			pc.addBowHitup(add_bow_hit);
		}
		final int add_dmg_r = vip.get_add_dmg_r();
		if (add_dmg_r != 0) {
			pc.add_reduction_dmg(add_dmg_r);
		}
		final int add_magic_r = vip.get_add_magic_r();
		if (add_magic_r != 0) {
			pc.add_magic_reduction_dmg(add_magic_r);
		}
		final int add_mr = vip.get_add_mr();
		if (add_mr != 0) {
			pc.addMr(add_mr);
			spmr = true;
		}
		final int add_sp = vip.get_add_sp();
		if (add_sp != 0) {
			pc.addSp(add_sp);
			spmr = true;
		}
		final int add_fire = vip.get_add_fire();
		if (add_fire != 0) {
			pc.addFire(add_fire);
			attr = true;
		}
		final int add_wind = vip.get_add_wind();
		if (add_wind != 0) {
			pc.addWind(add_wind);
			attr = true;
		}
		final int add_earth = vip.get_add_earth();
		if (add_earth != 0) {
			pc.addEarth(add_earth);
			attr = true;
		}
		final int add_water = vip.get_add_water();
		if (add_water != 0) {
			pc.addWater(add_water);
			attr = true;
		}
		final int add_stun = vip.get_add_stun();
		if (add_stun != 0) {
			pc.addRegistStun(add_stun);
		}
		final int add_stone = vip.get_add_stone();
		if (add_stone != 0) {
			pc.addRegistStone(add_stone);
		}
		final int add_sleep = vip.get_add_sleep();
		if (add_sleep != 0) {
			pc.addRegistSleep(add_sleep);
		}
		final int add_freeze = vip.get_add_freeze();
		if (add_freeze != 0) {
			pc.add_regist_freeze(add_freeze);
		}
		final int add_sustain = vip.get_add_sustain();
		if (add_sustain != 0) {
			pc.addRegistSustain(add_sustain);
		}
		final int add_blind = vip.get_add_blind();
		if (add_blind != 0) {
			pc.addRegistBlind(add_blind);
		}
		final int add_exp = vip.get_add_exp();
		if (add_exp != 0) {
			pc.add_exp(add_exp);
		}
		if (vip.get_skin_id() != 0) {
			final L1SkinInstance skin = L1SpawnUtil.spawnSkin(pc, vip.get_skin_id());
			if (skin != null) {
				skin.setMoveType(1);
				pc.addSkin(skin, vip.get_skin_id());
			}
		}
		final int add_adena = vip.get_add_adena();
		if (add_adena != 0) {
			pc.addGF(add_adena);
		}
		if (vip.get_effect_icon() != 0) {
			pc.sendPackets(new S_PacketBox(180, 1, vip.get_effect_icon()));
		}
		if (status) {
			pc.sendPackets(new S_OwnCharStatus(pc));
		} else {
			if (status2) {
				pc.sendPackets(new S_OwnCharStatus2(pc));
			}
			if (attr) {
				pc.sendPackets(new S_OwnCharAttrDef(pc));
			}
		}
		if (spmr) {
			pc.sendPackets(new S_SPMR(pc));
		}
	}

	public void deleItemVIP(final L1PcInstance pc, final int item_id) {
		if (!ItemVIPTable._VIPList.containsKey(Integer.valueOf(item_id))) {
			return;
		}
		final L1ItemVIP vip = ItemVIPTable._VIPList.get(Integer.valueOf(item_id));
		final boolean status = false;
		boolean status2 = false;
		boolean spmr = false;
		boolean attr = false;
		if (vip.get_polyId() != 0) {
			L1PolyMorph.undoPoly(pc);
		}
		if (vip.getvipname() != null) {
			pc.setvipname(null);
			pc.sendPackets(new S_OwnCharStatus(pc));
			pc.sendPackets(new S_OwnCharPack(pc));
			pc.removeAllKnownObjects();
			pc.updateObject();
		}
		final int add_wmd = vip.get_add_wmd();
		if (add_wmd != 0) {
			pc.addweaponMD(-add_wmd);
			status2 = true;
		}
		final int add_wmc = vip.get_add_wmc();
		if (add_wmc != 0) {
			pc.addweaponMDC(-add_wmc);
			status2 = true;
		}
		final int add_str = vip.get_add_str();
		if (add_str != 0) {
			pc.addStr(-add_str);
			status2 = true;
		}
		final int add_dex = vip.get_add_dex();
		if (add_dex != 0) {
			pc.addDex(-add_dex);
			status2 = true;
		}
		final int add_con = vip.get_add_con();
		if (add_con != 0) {
			pc.addCon(-add_con);
			status2 = true;
		}
		final int add_int = vip.get_add_int();
		if (add_int != 0) {
			pc.addInt(-add_int);
			status2 = true;
		}
		final int add_wis = vip.get_add_wis();
		if (add_wis != 0) {
			pc.addWis(-add_wis);
			status2 = true;
		}
		final int add_cha = vip.get_add_cha();
		if (add_cha != 0) {
			pc.addCha(-add_cha);
			status2 = true;
		}
		final int add_ac = vip.get_add_ac();
		if (add_ac != 0) {
			pc.addAc(add_ac);
			attr = true;
		}
		final int add_hp = vip.get_add_hp();
		if (add_hp != 0) {
			pc.addMaxHp(-add_hp);
			pc.sendPackets(new S_HPUpdate(pc.getCurrentHp(), pc.getMaxHp()));
			if (pc.isInParty()) {
				pc.getParty().updateMiniHP(pc);
			}
		}
		final int add_mp = vip.get_add_mp();
		if (add_mp != 0) {
			pc.addMaxMp(-add_mp);
			pc.sendPackets(new S_MPUpdate(pc.getCurrentMp(), pc.getMaxMp()));
		}
		final int add_hpr = vip.get_add_hpr();
		if (add_hpr != 0) {
			pc.addHpr(-add_hpr);
		}
		final int add_mpr = vip.get_add_mpr();
		if (add_mpr != 0) {
			pc.addMpr(-add_mpr);
		}
		final int add_dmg = vip.get_add_dmg();
		if (add_dmg != 0) {
			pc.addDmgup(-add_dmg);
		}
		final int add_hit = vip.get_add_hit();
		if (add_hit != 0) {
			pc.addHitup(-add_hit);
		}
		final int add_bow_dmg = vip.get_add_bow_dmg();
		if (add_bow_dmg != 0) {
			pc.addBowDmgup(-add_bow_dmg);
		}
		final int add_bow_hit = vip.get_add_bow_hit();
		if (add_bow_hit != 0) {
			pc.addBowHitup(-add_bow_hit);
		}
		final int add_dmg_r = vip.get_add_dmg_r();
		if (add_dmg_r != 0) {
			pc.add_reduction_dmg(-add_dmg_r);
		}
		final int add_magic_r = vip.get_add_magic_r();
		if (add_magic_r != 0) {
			pc.add_magic_reduction_dmg(-add_magic_r);
		}
		final int add_mr = vip.get_add_mr();
		if (add_mr != 0) {
			pc.addMr(-add_mr);
			spmr = true;
		}
		final int add_sp = vip.get_add_sp();
		if (add_sp != 0) {
			pc.addSp(-add_sp);
			spmr = true;
		}
		final int add_fire = vip.get_add_fire();
		if (add_fire != 0) {
			pc.addFire(-add_fire);
			attr = true;
		}
		final int add_wind = vip.get_add_wind();
		if (add_wind != 0) {
			pc.addWind(-add_wind);
			attr = true;
		}
		final int add_earth = vip.get_add_earth();
		if (add_earth != 0) {
			pc.addEarth(-add_earth);
			attr = true;
		}
		final int add_water = vip.get_add_water();
		if (add_water != 0) {
			pc.addWater(-add_water);
			attr = true;
		}
		final int add_stun = vip.get_add_stun();
		if (add_stun != 0) {
			pc.addRegistStun(-add_stun);
		}
		final int add_stone = vip.get_add_stone();
		if (add_stone != 0) {
			pc.addRegistStone(-add_stone);
		}
		final int add_sleep = vip.get_add_sleep();
		if (add_sleep != 0) {
			pc.addRegistSleep(-add_sleep);
		}
		final int add_freeze = vip.get_add_freeze();
		if (add_freeze != 0) {
			pc.add_regist_freeze(-add_freeze);
		}
		final int add_sustain = vip.get_add_sustain();
		if (add_sustain != 0) {
			pc.addRegistSustain(-add_sustain);
		}
		final int add_blind = vip.get_add_blind();
		if (add_blind != 0) {
			pc.addRegistBlind(-add_blind);
		}
		final int add_exp = vip.get_add_exp();
		if (add_exp != 0) {
			pc.add_exp(-add_exp);
		}
		if (vip.get_skin_id() != 0 && pc.getSkin(vip.get_skin_id()) != null) {
			pc.getSkin(vip.get_skin_id()).deleteMe();
			pc.removeSkin(vip.get_skin_id());
		}
		final int add_adena = vip.get_add_adena();
		if (add_adena != 0) {
			pc.addGF(-add_adena);
		}
		if (vip.get_effect_icon() != 0) {
			pc.sendPackets(new S_PacketBox(180, 0, vip.get_effect_icon()));
		}
		if (status) {
			pc.sendPackets(new S_OwnCharStatus(pc));
		} else {
			if (status2) {
				pc.sendPackets(new S_OwnCharStatus2(pc));
			}
			if (attr) {
				pc.sendPackets(new S_OwnCharAttrDef(pc));
			}
		}
		if (spmr) {
			pc.sendPackets(new S_SPMR(pc));
		}
	}
}
