package com.lineage.server.datatables.sql;

import java.sql.Timestamp;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import com.lineage.DatabaseFactoryLogin;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.server.datatables.storage.EzpayStorage;

public class EzpayTable implements EzpayStorage {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(EzpayTable.class);
	}

	@Override
	public Map<Integer, int[]> ezpayInfo(final String loginName) {
		Connection co = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		final Map<Integer, int[]> list = new HashMap();
		try {
			co = DatabaseFactoryLogin.get().getConnection();
			final String sqlstr = "SELECT * FROM `w_玩家即時領取物品` WHERE `玩家帳號`=? ORDER BY `流水號`";
			ps = co.prepareStatement(sqlstr);
			ps.setString(1, loginName.toLowerCase());
			rs = ps.executeQuery();
			while (rs.next()) {
				final int[] value = new int[3];
				final int out = rs.getInt("領取狀態");
				final int ready = rs.getInt("是否手動發放");
				if (out == 0 && ready == 1) {
					final int key = rs.getInt("流水號");
					final int p_id = rs.getInt("發送物品編號");
					final int count = rs.getInt("發送物品數量");
					value[0] = key;
					value[1] = p_id;
					value[2] = count;
					list.put(Integer.valueOf(key), value);
				}
			}
		} catch (Exception e) {
			EzpayTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(ps);
			SQLUtil.close(co);
			SQLUtil.close(rs);
		}
		return list;
	}

	private boolean is_holding(final String loginName, final int id) {
		Connection co = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			co = DatabaseFactoryLogin.get().getConnection();
			final String sqlstr = "SELECT * FROM `w_玩家即時領取物品` WHERE `玩家帳號`=? AND `流水號`=?";
			ps = co.prepareStatement(sqlstr);
			ps.setString(1, loginName.toLowerCase());
			ps.setInt(2, id);
			rs = ps.executeQuery();
			while (rs.next()) {
				final int out = rs.getInt("領取狀態");
				if (out != 0) {
					return false;
				}
			}
		} catch (Exception e) {
			EzpayTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(ps);
			SQLUtil.close(co);
			SQLUtil.close(rs);
		}
		return true;
	}

	@Override
	public boolean update(final String loginName, final int id, final String pcname, final String ip) {
		if (!this.is_holding(loginName, id)) {
			return false;
		}
		Connection con = null;
		PreparedStatement pstm = null;
		try {
			final Timestamp lastactive = new Timestamp(System.currentTimeMillis());
			con = DatabaseFactoryLogin.get().getConnection();
			final String sqlstr = "UPDATE `w_玩家即時領取物品` SET `領取狀態`=1,`領取玩家角色`=?,`領取時間`=?,`ip`=? WHERE `流水號`=? AND `玩家帳號`=?";
			pstm = con.prepareStatement(sqlstr);
			pstm.setString(1, pcname);
			pstm.setTimestamp(2, lastactive);
			pstm.setString(3, ip);
			pstm.setInt(4, id);
			pstm.setString(5, loginName);
			pstm.execute();
			return true;
		} catch (Exception e) {
			EzpayTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
		return false;
	}
}
