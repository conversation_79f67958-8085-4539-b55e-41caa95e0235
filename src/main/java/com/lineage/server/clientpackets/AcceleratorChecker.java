package com.lineage.server.clientpackets;

import com.lineage.config.ConfigOther;
import com.lineage.server.datatables.RecordTable;
import com.lineage.server.datatables.SprTable;
import com.lineage.server.datatables.lock.IpReading;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1Teleport;
import com.lineage.server.serverpackets.*;
import com.lineage.server.utils.log.PlayerLogUtil;
import com.lineage.server.world.World;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.EnumMap;
import java.util.Iterator;

import static com.lineage.server.model.skill.L1SkillId.STATUS_BRAVE3;

public class AcceleratorChecker {
    public static final int R_OK = 0;
    public static final int R_DETECTED = 1;
    public static final int R_DISPOSED = 2;
    private static final Log _log;
    private static final int INJUSTICE_COUNT_LIMIT = 10;
    private static final int JUSTICE_COUNT_LIMIT = 4;
    private static final double HASTE_RATE = 0.75;
    private static final double WAFFLE_RATE = 0.87;
    private static final double DOUBLE_HASTE_RATE = 0.375;
    private static final double STANDARD_RATE = 1.0;
    public static double CHECK_STRICTNESS;

    static {
        _log = LogFactory.getLog(AcceleratorChecker.class);
        CHECK_STRICTNESS = ConfigOther.SPEED_TIME;
    }

    private final L1PcInstance _pc;
    private final EnumMap<ACT_TYPE, Long> _actTimers;
    private final EnumMap<ACT_TYPE, Long> _checkTimers;
    private int _injusticeCount;
    private int _justiceCount;

    public AcceleratorChecker(L1PcInstance pc) {
        _actTimers = new EnumMap(ACT_TYPE.class);
        _checkTimers = new EnumMap(ACT_TYPE.class);
        _pc = pc;
        _injusticeCount = 0;
        _justiceCount = 0;
        long now = System.currentTimeMillis();
        ACT_TYPE[] values;
        int length = (values = ACT_TYPE.values()).length;
        int i = 0;
        while (i < length) {
            ACT_TYPE each = values[i];
            _actTimers.put(each, Long.valueOf(now));
            _checkTimers.put(each, Long.valueOf(now));
            ++i;
        }
    }


    private void toGmErrMsg(String name, int count) {
        try {
            if (count >= 5) {
                Collection<L1PcInstance> allPc = World.get().getAllPlayers();
                Iterator<L1PcInstance> iterator = allPc.iterator();
                while (iterator.hasNext()) {
                    L1PcInstance tgpc = iterator.next();
                    if (tgpc.isGm()) {
                        tgpc.sendPackets(new S_ToGmMessage("人物:" + name + " 速度異常!(" + count + "次)"));
                    }
                }
            }
        } catch (Exception e) {
            AcceleratorChecker._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void toGmKickMsg(String name) {
        try {
            Collection<L1PcInstance> allPc = World.get().getAllPlayers();
            Iterator<L1PcInstance> iterator = allPc.iterator();
            while (iterator.hasNext()) {
                L1PcInstance tgpc = iterator.next();
                if (tgpc.isGm()) {
                    tgpc.sendPackets(new S_ToGmMessage("人物:" + name + " 速度異常斷線!"));
                }
            }
        } catch (Exception e) {
            AcceleratorChecker._log.error(e.getLocalizedMessage(), e);
        }
    }

    public int checkInterval(ACT_TYPE type) {
        if (!ConfigOther.SPEED) {
            return 0;
        }
        int result = 0;
        long now = System.currentTimeMillis();
        long interval = now - _actTimers.get(type).longValue();
        int rightInterval = getRightInterval(type);
        //TODO 丹丹測試限制速度開始
        LocalDateTime localNow = LocalDateTime.now();
        Duration duration = null;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuilder actionResult = new StringBuilder(dateFormat.format(new Date()) + " ");
        long _moverightInterval = getRightInterval(type);
        boolean errerCheck = false;
        switch (type) {
            case ATTACK:
                if (_pc.getLastTimeAttackTime() == null) {
                    _pc.setLastTimeAttackTime(localNow);
                } else {
                    duration = Duration.between(_pc.getLastTimeAttackTime(), localNow);
                    //actionResult.append(String.format("玩家:%s :: 攻擊時間應該要有%s毫秒，但實際跑了%s毫秒，沒倍率是%s毫秒;\n\r", _pc.getName(), _moverightInterval, (duration.toMillis() * CHECK_STRICTNESS), duration.toMillis()));
//                    _log.warn("攻擊時間應該要有" + _moverightInterval + "毫秒，但實際跑了" + (duration.toMillis() * CHECK_STRICTNESS) + "毫秒" + "沒倍率是" + duration.toMillis() + "毫秒");
                    if (duration.toMillis() * CHECK_STRICTNESS < _moverightInterval) {
                        actionResult.append(String.format("玩家:%s :: 攻擊時間應該要有%s毫秒，但實際跑了%s毫秒;\n\r", _pc.getName(), _moverightInterval, duration.toMillis()));
//                        _log.warn("攻擊時間應該要有" + _moverightInterval + "毫秒，但實際跑了" + duration.toMillis() + "毫秒");
                        errerCheck = true;
                    }
                    _pc.setLastTimeAttackTime(localNow);
                }
                break;
            case MOVE:
                if (_pc.getLastTimeMoveTime() == null) {
                    _pc.setLastTimeMoveTime(localNow);
                } else {
                    duration = Duration.between(_pc.getLastTimeMoveTime(), localNow);
                    if (duration.toMillis() * CHECK_STRICTNESS < _moverightInterval) {
                        actionResult.append(String.format("玩家:%s :: 移動時間應該要有%s毫秒，但實際跑了%s毫秒;\n\r", _pc.getName(), _moverightInterval, duration.toMillis()));
//                        _log.warn("移動時間應該要有" + _moverightInterval + "毫秒，但實際跑了" + duration.toMillis() + "毫秒");
                        errerCheck = true;
                    }
                    _pc.setLastTimeMoveTime(localNow);

                }
                break;
            case SPELL_DIR:
                if (_pc.getLastTimeSpellDirTime() == null) {
                    _pc.setLastTimeSpellDirTime(localNow);
                } else {
                    duration = Duration.between(_pc.getLastTimeSpellDirTime(), localNow);
                    if (duration.toMillis() * CHECK_STRICTNESS < _moverightInterval) {
                        actionResult.append(String.format("玩家:%s :: 有施法動作時間應該要有%s毫秒，但實際跑了%s毫秒;\n\r", _pc.getName(), _moverightInterval, duration.toMillis()));
//                        _log.warn("有施法動作時間應該要有" + _moverightInterval + "毫秒，但實際跑了" + duration.toMillis() + "毫秒");
                        errerCheck = true;
                    }
                    _pc.setLastTimeSpellDirTime(localNow);
                }
                break;
            case SPELL_NODIR:
                if (_pc.getLastTimeSpellNoirTime() == null) {
                    _pc.setLastTimeSpellNoirTime(localNow);
                } else {
                    duration = Duration.between(_pc.getLastTimeSpellNoirTime(), localNow);
                    if (duration.toMillis() * CHECK_STRICTNESS < _moverightInterval) {
                        actionResult.append(String.format("玩家:%s :: 無施法動作時間應該要有%s毫秒，但實際跑了%s毫秒;\n\r", _pc.getName(), _moverightInterval, duration.toMillis()));
//                        _log.warn("無施法動作時間應該要有" + _moverightInterval + "毫秒，但實際跑了" + duration.toMillis() + "毫秒");
                        errerCheck = true;
                    }
                    _pc.setLastTimeSpellNoirTime(localNow);

                }
                break;
            default:
                return 0;
        }
        if (errerCheck) {
            _injusticeCount += 1;
            PlayerLogUtil.writeLog("[加速偵測結果]", actionResult.toString());
            if (_injusticeCount < INJUSTICE_COUNT_LIMIT) {
                result = 1;
            } else {
                doPunishment(ConfigOther.ILLEGAL_SPEEDUP_PUNISHMENT);
                result = 2;
            }
        } else {
            _justiceCount += 1;
            if (_justiceCount >= INJUSTICE_COUNT_LIMIT) { // 必須正常次數,設定如果參雜正常封包在不正常封包中,數值滿足時 InjusticeCount歸 0
                _injusticeCount = 0;
                _justiceCount = 0;
            } else {
                result = 1;
            }
        }
        //TODO 加速測試結束

        /*
        interval = (long) (interval * AcceleratorChecker.CHECK_STRICTNESS);
        double rate = (double) interval / (double) rightInterval;
        if (0.0 < rate && rate < 1.0) {
            ++_injusticeCount;
            toGmErrMsg(_pc.getName(), _injusticeCount);
            _justiceCount = 0;
            if (_injusticeCount >= 10) {
                doPunishment(ConfigOther.ILLEGAL_SPEEDUP_PUNISHMENT);
                return 2;
            }
            result = 1;
        } else if (rate >= 1.0) {
            ++_justiceCount;
            if (_justiceCount >= 4) {
                _injusticeCount = 0;
                _justiceCount = 0;
            }
        }
        _actTimers.put(type, Long.valueOf(now));
         */
        return result;
    }

    private void doPunishment(int punishmaent) {
        if (!_pc.isGm()) {
            int x = _pc.getX();
            int y = _pc.getY();
            int mapid = _pc.getMapId();
            switch (punishmaent) {
                case 0: {
                    _pc.setSpeedError(_pc.getSpeedError() + 1);
                    if (_pc.getSpeedError() >= 10) {
                        final long time = -1702967296L;
                        Timestamp UnbanTime = new Timestamp(System.currentTimeMillis() + time);
                        IpReading.get().setUnbanTime(UnbanTime);
                        IpReading.get().add(_pc.getAccountName(), "加速器檢測 自動封鎖帳號三十日");
                        _pc.saveInventory();
                        RecordTable.get().r_speed(_pc.getName(), "偵測異常(攻速or走速");
                        _pc.sendPackets(new S_Disconnect());
                        World.get().broadcastServerMessage("玩家 : " + _pc.getName() + " 因當日加速器檢測斷線十次，已封鎖其帳號三十日。");
                        AcceleratorChecker._log.info(String.format("玩家 : %s 因當日加速器檢測斷線十次，已封鎖其帳號三十日。", _pc.getName()));
                        break;
                    }
                    _pc.sendPackets(new S_ServerMessage(945));
                    _pc.saveInventory();
                    _pc.sendPackets(new S_Disconnect());
                    StringBuilder name = new StringBuilder();
                    name.append(_pc.getName());
                    toGmKickMsg(name.toString());
                    RecordTable.get().r_speed(_pc.getName(), "偵測異常(攻速or走速");
                    World.get().broadcastServerMessage("玩家 : " + _pc.getName() + " 因為檢測到加速器，已強制切斷其連線。");
                    AcceleratorChecker._log.info(String.format("因為檢測到%s正在使用加速器，強制切斷其連線。", _pc.getName()));
                    break;
                }
                case 1: {
                    _pc.sendPackets(new S_Paralysis(5, true));
                    _pc.sendPackets(new S_ServerMessage("因為檢測到加速器，懲罰暈眩10秒"));
                    RecordTable.get().r_speed(_pc.getName(), "偵測異常(攻速or走速");
                    try {
                        Thread.sleep(10000L);
                    } catch (Exception e) {
                        System.out.println(e.getLocalizedMessage());
                    }
                    _pc.sendPackets(new S_Paralysis(5, false));
                    break;
                }
                case 2: {
                    L1Teleport.teleport(_pc, 32746, 32740, (short) 666, 5, false);
                    _pc.sendPackets(new S_SystemMessage("因為你使用加速器，被傳送到了地域，10秒後傳回。"));
                    RecordTable.get().r_speed(_pc.getName(), "偵測異常(攻速or走速");
                    try {
                        Thread.sleep(10000L);
                    } catch (Exception e) {
                        System.out.println(e.getLocalizedMessage());
                    }
                    L1Teleport.teleport(_pc, x, y, (short) mapid, 5, false);
                    break;
                }
                case 3: {
                    L1Teleport.teleport(_pc, 32737, 32796, (short) 99, 5, false);
                    _pc.sendPackets(new S_SystemMessage("因為你使用加速器，被傳送到了GM房，30秒後傳回。"));
                    try {
                        Thread.sleep(30000L);
                    } catch (Exception e) {
                        System.out.println(e.getLocalizedMessage());
                    }
                    L1Teleport.teleport(_pc, x, y, (short) mapid, 5, false);
                    RecordTable.get().r_speed(_pc.getName(), "偵測異常(攻速or走速");
                    break;
                }
                case 4: {
                    _pc.sendPackets(new S_Paralysis(6, true));
                    _pc.sendPackets(new S_ServerMessage("因為檢測到加速器，限制移動10秒"));
                    RecordTable.get().r_speed(_pc.getName(), "偵測異常(攻速or走速");
                    try {
                        Thread.sleep(10000L);
                    } catch (Exception e) {
                        System.out.println(e.getLocalizedMessage());
                    }
                    _pc.sendPackets(new S_Paralysis(6, false));
                    break;
                }
            }
        } else {
            _pc.sendPackets(new S_SystemMessage("遊戲管理員在遊戲中使用加速器檢測中。"));
            _injusticeCount = 0;
            _justiceCount = 0;
        }
    }

    public int getRightInterval(ACT_TYPE type) {
        int interval = 0;
        switch (type) {
            case ATTACK: {
                interval = SprTable.get().getAttackSpeed(_pc.getTempCharGfx(), _pc.getCurrentWeapon() + 1);
                break;
            }
            case MOVE: {
                interval = SprTable.get().getMoveSpeed(_pc.getTempCharGfx(), _pc.getCurrentWeapon());
                break;
            }
            case SPELL_DIR: {
                interval = SprTable.get().getDirSpellSpeed(_pc.getTempCharGfx());
                break;
            }
            case SPELL_NODIR: {
                interval = SprTable.get().getNodirSpellSpeed(_pc.getTempCharGfx());
                break;
            }
            default: {
                return 0;
            }
        }
        switch (_pc.getMoveSpeed()) {
            case 1: {
                interval = (int) (interval * 0.75);
                break;
            }
            case 2: {
                interval = (int) (interval / 0.75);
                break;
            }
        }
        switch (_pc.getBraveSpeed()) {
            case 1: {
                interval = (int) (interval * 0.75);
                break;
            }
            case 3: {
                interval = (int) (interval * 0.87);
                break;
            }
            case 4: {
                if (type.equals(ACT_TYPE.MOVE) && _pc.isFastMovable()) {
                    interval = (int) (interval * 0.75);
                    break;
                }
                break;
            }
            case 5: {
                interval = (int) (interval * 0.375);
                break;
            }
            case 6: {
                if (type.equals(ACT_TYPE.ATTACK)) {
                    interval = (int) (interval * 0.75);
                    break;
                }
                break;
            }
        }
        if (_pc.isBraveX()) {
            interval = (int) (interval * 0.87);
        }
        if (_pc.isWindShackle() && !type.equals(ACT_TYPE.MOVE)) {
            interval /= 2;
        }
        if (_pc.getMapId() == 5143) {
            interval = (int) (interval * 0.1);
        }
        // Kevin 新增 三段加速 STATUS_BRAVE3 / 巧克力蛋糕
        if (_pc.hasSkillEffect(STATUS_BRAVE3)) { // 攻速、移速 * 1.15倍
            interval *= WAFFLE_RATE;
        }

        return interval;
    }

    public enum ACT_TYPE {
        MOVE("MOVE", 0), ATTACK("ATTACK", 1), SPELL_DIR("SPELL_DIR", 2), SPELL_NODIR("SPELL_NODIR", 3);

        private ACT_TYPE(String s, int n) {
        }
    }
}
