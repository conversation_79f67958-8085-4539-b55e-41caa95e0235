package com.lineage.server.clientpackets;

import com.lineage.server.model.L1ExcludingList;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_PacketBox;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.echo.ClientExecutor;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class C_Exclude extends ClientBasePacket {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(C_Exclude.class);
	}

	@Override
	public void start(final byte[] decrypt, final ClientExecutor client) {
		try {
			this.read(decrypt);
			final String name = this.readS();
			if (name.isEmpty()) {
				return;
			}
			final L1PcInstance pc = client.getActiveChar();
			final L1ExcludingList exList = pc.getExcludingList();
			if (exList.isFull()) {
				pc.sendPackets(new S_ServerMessage(472));
				return;
			}
			if (exList.contains(name)) {
				final String temp = exList.remove(name);
				pc.sendPackets(new S_PacketBox(19, temp));
			} else {
				exList.add(name);
				pc.sendPackets(new S_PacketBox(18, name));
			}
		} catch (Exception ex) {
		} finally {
			this.over();
		}
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
