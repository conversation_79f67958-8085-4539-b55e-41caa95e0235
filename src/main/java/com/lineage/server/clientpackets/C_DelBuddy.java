package com.lineage.server.clientpackets;

import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.datatables.lock.BuddyReading;
import com.lineage.echo.ClientExecutor;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class C_DelBuddy extends ClientBasePacket {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(C_DelBuddy.class);
	}

	@Override
	public void start(final byte[] decrypt, final ClientExecutor client) {
		try {
			this.read(decrypt);
			final L1PcInstance pc = client.getActiveChar();
			final String charName = this.readS().toLowerCase();
			if (charName.isEmpty()) {
				return;
			}
			BuddyReading.get().removeBuddy(pc.getId(), charName);
		} catch (Exception ex) {
		} finally {
			this.over();
		}
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
