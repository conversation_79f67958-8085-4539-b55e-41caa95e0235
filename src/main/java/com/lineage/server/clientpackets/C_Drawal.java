package com.lineage.server.clientpackets;

import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.templates.L1Castle;
import com.lineage.server.model.L1Clan;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.datatables.lock.CastleReading;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.world.WorldClan;
import com.lineage.echo.ClientExecutor;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class C_Drawal extends ClientBasePacket {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(C_Drawal.class);
	}

	@Override
	public void start(final byte[] decrypt, final ClientExecutor client) {
		try {
			this.read(decrypt);
			final L1PcInstance pc = client.getActiveChar();
			if (!pc.isGhost()) {
				if (!pc.isDead()) {
					if (!pc.isTeleport()) {
						final int objid = this.readD();
						long count = this.readD();
						if (count > 2147483647L) {
							count = 2147483647L;
						}
						count = Math.max(0L, count);
						final L1Clan clan = WorldClan.get().getClan(pc.getClanname());
						if (clan == null) {
							return;
						}
						final int castle_id = clan.getCastleId();
						if (castle_id == 0) {
							return;
						}
						if (!pc.isCrown()) {
							pc.sendPackets(new S_ServerMessage(518));
							return;
						}
						if (pc.getId() != clan.getLeaderId()) {
							return;
						}
						final L1Castle l1castle = CastleReading.get().getCastleTable(castle_id);
						final L1ItemInstance item = ItemTable.get().createItem(40308);
						final long money = l1castle.getPublicMoney() - count;
						if (item != null && money >= 0L) {
							l1castle.setPublicMoney(money);
							CastleReading.get().updateCastle(l1castle);
							if (pc.getInventory().checkAddItem(item, count) == 0) {
								pc.getInventory().storeItem(40308, count);
							}
							pc.sendPackets(new S_ServerMessage(143, "$457", "$4 (" + count + ")"));
							return;
						}
						return;
					}
				}
			}
			return;
		} catch (Exception e) {
			C_Drawal._log.error(e.getLocalizedMessage(), e);
		} finally {
			this.over();
		}
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
