package com.lineage.server.clientpackets;

import com.lineage.server.model.L1Clan;
import com.lineage.server.serverpackets.S_Message_YN;
import com.lineage.server.world.WorldClan;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.utils.FaceToFace;
import com.lineage.echo.ClientExecutor;

public class C_Join<PERSON>lan extends ClientBasePacket {
	private static final String C_JOIN_CLAN = "[C] C_JoinClan";

	@Override
	public void start(final byte[] abyte0, final ClientExecutor client) throws Exception {
		final L1PcInstance pc = client.getActiveChar();
		if (pc == null || pc.isGhost()) {
			return;
		}
		final L1PcInstance target = FaceToFace.faceToFace(pc);
		if (target != null) {
			this.JoinClan(pc, target);
		}
	}

	private void JoinClan(final L1PcInstance player, final L1PcInstance target) {
		if (!target.isCrown() && target.getClanRank() != 9 && target.getClanRank() != 6) {
			player.sendPackets(new S_ServerMessage(92, target.getName()));
			return;
		}
		if (player.isCrown() && (target.getClanRank() == 9 || target.getClanRank() == 6)) {
			return;
		}
		if (player.isCrown() && target.getClanRank() == 3) {
			player.sendPackets(new S_ServerMessage(2504));
			return;
		}
		if (player.getClanid() == target.getClanid()) {
			player.sendPackets(new S_ServerMessage("已經是加入血盟狀態。"));
			return;
		}
		final int clan_id = target.getClanid();
		final String clan_name = target.getClanname();
		if (clan_id == 0) {
			player.sendPackets(new S_ServerMessage(90, target.getName()));
			return;
		}
		final L1Clan clan = WorldClan.get().getClan(clan_name);
		if (clan == null) {
			return;
		}
		if (target.getClanRank() != 10 && target.getClanRank() != 9 && target.getClanRank() != 6
				&& target.getClanRank() != 4 && target.getClanRank() != 3) {
			player.sendPackets(new S_ServerMessage(92, target.getName()));
			return;
		}
		if (player.getClanid() != 0) {
			if (!player.isCrown()) {
				player.sendPackets(new S_ServerMessage(89));
				return;
			}
			final String player_clan_name = player.getClanname();
			final L1Clan player_clan = WorldClan.get().getClan(player_clan_name);
			if (player_clan == null) {
				return;
			}
			if (player.getId() != player_clan.getLeaderId()) {
				player.sendPackets(new S_ServerMessage(89));
				return;
			}
			if (player_clan.getCastleId() != 0 || player_clan.getHouseId() != 0) {
				player.sendPackets(new S_ServerMessage(665));
				return;
			}
		}
		if (player.getRejoinClanTime() != null && player.getRejoinClanTime().getTime() > System.currentTimeMillis()) {
			final int time = (int) (player.getRejoinClanTime().getTime() - System.currentTimeMillis()) / 3600000;
			player.sendPackets(new S_ServerMessage(1925, new StringBuilder(String.valueOf(time)).toString()));
			return;
		}
		target.setTempID(player.getId());
		target.sendPackets(new S_Message_YN(97, player.getName()));
	}

	@Override
	public String getType() {
		return "[C] C_JoinClan";
	}
}
