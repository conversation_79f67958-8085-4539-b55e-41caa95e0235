package com.lineage.server.clientpackets;

import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.datatables.lock.CharBookReading;
import com.lineage.echo.ClientExecutor;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class C_DeleteBookmark extends ClientBasePacket {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(C_DeleteBookmark.class);
	}

	@Override
	public void start(final byte[] decrypt, final ClientExecutor client) {
		try {
			this.read(decrypt);
			final String bookmarkname = this.readS();
			if (!bookmarkname.isEmpty()) {
				final L1PcInstance pc = client.getActiveChar();
				CharBookReading.get().deleteBookmark(pc, bookmarkname);
			}
		} catch (Exception ex) {
		} finally {
			this.over();
		}
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
