package com.lineage.server.clientpackets;

import java.util.logging.Level;
import com.lineage.server.serverpackets.S_CharTitle;
import com.lineage.server.model.L1Clan;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.config.ConfigOther;
import com.lineage.server.world.WorldClan;
import com.lineage.list.BadNamesList;
import com.lineage.server.world.World;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.echo.ClientExecutor;
import java.util.logging.Logger;

public class C_Title extends ClientBasePacket {
	private static final String C_TITLE = "[C] C_Title";
	private static Logger _log;

	static {
		_log = Logger.getLogger(C_Title.class.getName());
	}

	@Override
	public void start(final byte[] decrypt, final ClientExecutor client) {
		this.read(decrypt);
		final L1PcInstance pc = client.getActiveChar();
		if (pc == null) {
			return;
		}
		final String charName = this.readS();
		final String title = this.readS();
		if (charName.isEmpty() || title.isEmpty()) {
			pc.sendPackets(new S_ServerMessage(196));
			return;
		}
		final L1PcInstance target = World.get().getPlayer(charName);
		if (target == null) {
			return;
		}
		if (pc.isGm()) {
			this.changeTitle(target, title);
			return;
		}
		if (this.isClanLeader(pc)) {
			if (pc.getId() == target.getId()) {
				if (pc.getLevel() < 10) {
					pc.sendPackets(new S_ServerMessage(197));
					return;
				}
				this.changeTitle(pc, title);
			} else {
				if (pc.getClanid() != target.getClanid()) {
					pc.sendPackets(new S_ServerMessage(201));
					return;
				}
				if (target.getLevel() < 10) {
					pc.sendPackets(new S_ServerMessage(202, charName));
					return;
				}
				if (BadNamesList.get().isBadName(title)) {
					pc.sendPackets(new S_ServerMessage("此稱號無效名稱"));
					return;
				}
				this.changeTitle(target, title);
				final L1Clan clan = WorldClan.get().getClan(pc.getClanname());
				if (clan != null) {
					final L1PcInstance[] array;
					final int n = (array = clan.getOnlineClanMember()).length;
					int i = 0;
					while (i < n) {
						final L1PcInstance clanPc = array[i];
						clanPc.sendPackets(new S_ServerMessage(203, pc.getName(), charName, title));
						++i;
					}
				}
			}
		} else if (pc.getId() == target.getId()) {
			if (pc.getClanid() != 0 && !ConfigOther.CLANTITLE) {
				pc.sendPackets(new S_ServerMessage(198));
				return;
			}
			if (target.getLevel() < 40) {
				pc.sendPackets(new S_ServerMessage(200));
				return;
			}
			if (BadNamesList.get().isBadName(title)) {
				pc.sendPackets(new S_ServerMessage("此稱號無效名稱"));
				return;
			}
			this.changeTitle(pc, title);
		} else if (pc.isCrown()) {
			if (pc.getClanid() == target.getClanid()) {
				if (target.getClanRank() == 10 || target.getClanRank() == 4 || target.getClanRank() == 3) {
					pc.sendPackets(new S_ServerMessage(2065));
					return;
				}
				this.changeTitle(target, title);
				final L1Clan clan = WorldClan.get().getClan(pc.getClanname());
				if (clan != null) {
					final L1PcInstance[] array;
					final int n = (array = clan.getOnlineClanMember()).length;
					int i = 0;
					while (i < n) {
						final L1PcInstance clanPc = array[i];
						clanPc.sendPackets(new S_ServerMessage(203, pc.getName(), charName, title));
						++i;
					}
				}
			}
		} else if (pc.getClanid() == target.getClanid()) {
			if (pc.getClanRank() == 9 || pc.getClanRank() == 6) {
				pc.sendPackets(new S_ServerMessage(2474));
			} else {
				pc.sendPackets(new S_ServerMessage(2143));
			}
			return;
		}
	}

	private void changeTitle(final L1PcInstance pc, final String title) {
		final int objectId = pc.getId();
		pc.setTitle(title);
		pc.sendPackets(new S_CharTitle(objectId, title));
		pc.broadcastPacketAll(new S_CharTitle(objectId, title));
		try {
			pc.save();
		} catch (Exception e) {
			C_Title._log.log(Level.SEVERE, e.getLocalizedMessage(), e);
		}
	}

	private boolean isClanLeader(final L1PcInstance pc) {
		boolean isClanLeader = false;
		if (pc.getClanid() != 0) {
			final L1Clan clan = WorldClan.get().getClan(pc.getClanname());
			if (clan != null && pc.isCrown() && pc.getId() == clan.getLeaderId()) {
				isClanLeader = true;
			}
		}
		return isClanLeader;
	}

	@Override
	public String getType() {
		return "[C] C_Title";
	}
}
