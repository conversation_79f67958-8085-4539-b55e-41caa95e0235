package com.lineage.server.clientpackets;

import com.lineage.commons.system.LanSecurityManager;
import com.lineage.config.Config;
import com.lineage.config.ConfigIpCheck;
import com.lineage.data.event.NetBarSet;
import com.lineage.echo.ClientExecutor;
import com.lineage.list.OnlineUser;
import com.lineage.server.datatables.lock.AccountReading;
import com.lineage.server.serverpackets.S_CommonNews;
import com.lineage.server.serverpackets.S_Disconnect;
import com.lineage.server.serverpackets.S_LoginResult;
import com.lineage.server.templates.L1Account;
import com.lineage.server.thread.GeneralThreadPool;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Iterator;
import java.util.Random;

public class C_AuthLogin extends ClientBasePacket {
    private static final Log _log;
    private static final String _check_accname = "abcdefghijklmnopqrstuvwxyz0123456789";
    private static final String _check_pwd = "abcdefghijklmnopqrstuvwxyz0123456789!_=+-?.#";

    static {
        _log = LogFactory.getLog(C_AuthLogin.class);
    }

    @Override
    public void start(byte[] decrypt, ClientExecutor client) {
        try {
            read(decrypt);
            boolean iserror = false;
            String loginName = readS().toLowerCase();
            if (loginName.length() > 12) {
                C_AuthLogin._log.warn("不合法的帳號長度:" + client.getIp().toString());
                client.set_error(client.get_error() + 1);
                return;
            }
            int i = 0;
            while (i < loginName.length()) {
                String ch = loginName.substring(i, i + 1);
                if (!"abcdefghijklmnopqrstuvwxyz0123456789".contains(ch)) {
                    C_AuthLogin._log.warn("不被允許的帳號字元!");
                    iserror = true;
                    break;
                }
                ++i;
            }
            String password = readS();
            if (password.length() > 13) {
                C_AuthLogin._log.warn("不合法的密碼長度:" + client.getIp().toString());
                client.set_error(client.get_error() + 1);
                return;
            }
            int j = 0;
            while (j < password.length()) {
                String ch2 = password.substring(j, j + 1);
                if (!"abcdefghijklmnopqrstuvwxyz0123456789!_=+-?.#".contains(ch2.toLowerCase())) {
                    C_AuthLogin._log.warn("不被允許的密碼字元!");
                    iserror = true;
                    break;
                }
                ++j;
            }
            if (!iserror) {
                checkLogin(client, loginName, password, false);
            } else {
                client.out().encrypt(new S_LoginResult(10));
            }
        } catch (Exception ex) {
            _log.error("login error ::", ex);
        } finally {
            over();
        }
    }

    public void checkLogin(ClientExecutor client, String loginName, String password,
                           boolean auto) {
        try {
            if (Config.TestServer == true) {
                if (!password.equals(Config.TestServerPassWords)) {
                    C_AuthLogin._log.info("測試密碼輸入錯誤!");
                    client.out().encrypt(new S_LoginResult(8));
                    return;
                } else {
                    C_AuthLogin._log.info("測試密碼登入成功!");
                }
            }
            if (loginName == null) {
                return;
            }
            if (loginName.equals("")) {
                return;
            }
            if (password == null) {
                return;
            }
            if (password.equals("")) {
                return;
            }
            StringBuilder ip = client.getIp();
            StringBuilder mac = client.getMac();
            if (LanSecurityManager.BANNAMEMAP.containsKey(loginName)) {
                C_AuthLogin._log.warn("禁止登入帳號位置: account=" + loginName + " host=" + client.getIp());
                client.out().encrypt(new S_LoginResult(26));
                KickTimeController kickTime = new KickTimeController(client, null);
                kickTime.schedule();
                return;
            }
            boolean isError = false;
            L1Account account = AccountReading.get().getAccount(loginName);
            int tongip = ConfigIpCheck.ipcount;
            if (NetBarSet.EXIPLIST.containsKey(ip.toString())) {
                tongip = NetBarSet.EXIPLIST.get(ip.toString()).intValue();
            }
            if (tongip != 0) {
                int k = 0;
                Iterator<ClientExecutor> iterator = OnlineUser.get().all().iterator();
                while (iterator.hasNext()) {
                    ClientExecutor tempClient = iterator.next();
                    //基本上不會進入這行
                    if (tempClient == null) {
                        continue;
                    }
                    //跳過這些無法取得資料的
                    if (tempClient.getAccount() == null) {
                        _log.info("跳過取得空值：tempClient.getAccount() , 線上資料沒有getAccount");
                        continue;
                    }
                    //如果這個線上玩家沒IP理論上要跳警告
                    if (tempClient.getAccount().get_ip() == null) {
                        _log.warn("玩家帳號:" + tempClient.getAccount().get_login() + " , 線上資料沒有IP");
                        continue;
                    }

                    if (ip.toString().equalsIgnoreCase(tempClient.getAccount().get_ip()) && ++k > tongip - 1) {
                        client.out().encrypt(new S_LoginResult(39));
                        return;
                    }

                }
            }
            if (account == null) {
                if (Config.AUTO_CREATE_ACCOUNTS) {
                    if (mac == null) {
                        mac = ip;
                    }
                    account = AccountReading.get().create(loginName, password, ip.toString(), mac.toString(),
                            "未建立超級密碼");
                } else {
                    if (auto) {
                        client.out().encrypt(new S_LoginResult(155));
                        return;
                    }
                    client.out().encrypt(new S_LoginResult(8));
                    isError = true;
                }
            }
            if (!account.get_password().equals(password) && !isError) {
                if (auto) {
                    client.out().encrypt(new S_LoginResult(149));
                } else {
                    client.out().encrypt(new S_LoginResult(8));
                }
                isError = true;
            }
            if (OnlineUser.get().isMax() && !isError) {
                C_AuthLogin._log.info("人數已達上限");
                client.out().encrypt(new S_LoginResult(39));
                isError = true;
            }
            if (isError) {
                int error = client.get_error();
                client.set_error(error + 1);
                return;
            }
            ClientExecutor inGame = OnlineUser.get().get(loginName);
            if (inGame != null) {
                C_AuthLogin._log.info("相同帳號重複登 入: account=" + loginName + " host=" + ip);
                client.out().encrypt(new S_LoginResult(22));
                KickTimeController kickTime2 = new KickTimeController(client, inGame);
                kickTime2.schedule();
            } else {
                if (account.get_server_no() != 0 && account.get_server_no() != Config.SERVERNO) {
                    C_AuthLogin._log.info("帳號登入其他服務器: account=" + loginName + " host=" + ip + " 已經登入:"
                            + account.get_server_no() + "伺服器");
                    client.out().encrypt(new S_LoginResult(22));
                    KickTimeController kickTime2 = new KickTimeController(client, null);
                    kickTime2.schedule();
                    return;
                }
                if (account.is_isLoad()) {
                    C_AuthLogin._log.info("相同帳號重複登入: account=" + loginName + " host=" + ip);
                    client.out().encrypt(new S_LoginResult(22));
                    KickTimeController kickTime2 = new KickTimeController(client, null);
                    kickTime2.schedule();
                    return;
                }
                if (OnlineUser.get().addClient(account, client)) {
                    account.set_ip(ip.toString());
                    if (mac != null) {
                        account.set_mac(mac.toString());
                    }
                    AccountReading.get().updateLastActive(account);
                    client.setAccount(account);

                    //登入資料更新
                    OnlineUser.get().updateClient(account, client);

                    client.out().encrypt(new S_LoginResult(0));
                    if (Config.NEWS) {
                        delay(100, 100);
                        client.out().encrypt(new S_CommonNews());
                    } else {
                        delay(100, 100);
                        C_CommonClick common = new C_CommonClick();
                        common.start(null, client);
                    }
                }
            }
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }

    public void delay(int i, int j) {
        try {
            int rnd = new Random().nextInt(i) + j;
            Thread.sleep(rnd);
        } catch (Exception e) {
            C_AuthLogin._log.info("角色延遲登入系統出錯");
        }
    }

    private class KickTimeController implements Runnable {
        private ClientExecutor _kick1;
        private ClientExecutor _kick2;

        private KickTimeController(ClientExecutor kick1, ClientExecutor kick2) {
            _kick1 = kick1;
            _kick2 = kick2;
        }

        private void schedule() {
            GeneralThreadPool.get().execute(this);
        }

        @Override
        public void run() {
            try {
                Thread.sleep(1000L);
                _kick1.out().encrypt(new S_Disconnect());
                Thread.sleep(1000L);
                _kick1.set_error(10);
                if (_kick2 != null) {
                    _kick2.set_error(10);
                }
            } catch (InterruptedException e) {
                C_AuthLogin._log.error(e.getLocalizedMessage(), e);
            }
        }
    }
}
