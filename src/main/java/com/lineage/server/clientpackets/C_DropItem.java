package com.lineage.server.clientpackets;

import com.lineage.config.ConfigAlt;
import com.lineage.data.event.DropItem;
import com.lineage.echo.ClientExecutor;
import com.lineage.server.Instance.character.CharacterPunishInstance;
import com.lineage.server.Shutdown;
import com.lineage.server.datatables.ItemRestrictionsTable;
import com.lineage.server.datatables.RecordTable;
import com.lineage.server.datatables.ServerItemDropTable;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.Instance.L1PetInstance;
import com.lineage.server.serverpackets.S_CloseList;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.world.World;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class C_DropItem extends ClientBasePacket {
    private static final Log _log;

    static {
        _log = LogFactory.getLog(C_DropItem.class);
    }

    @Override
    public void start(byte[] decrypt, ClientExecutor client) {
        try {
            read(decrypt);
            int x = readH();
            int y = readH();
            int objectId = readD();
            int count = readD();
            if (count > Integer.MAX_VALUE) {
                count = Integer.MAX_VALUE;
            }
            count = Math.max(0, count);
            L1PcInstance pc = client.getActiveChar();

            if (CharacterPunishInstance.get().checkCharacter(pc.getId())) {
                return;
            }

            if (!pc.isGhost()) {
                if (!ConfigAlt.DORP_ITEM && !pc.isGm()) {
                    pc.sendPackets(new S_ServerMessage(125));
                } else {
                    L1ItemInstance item = pc.getInventory().getItem(objectId);
                    if (item == null) {
                        return;
                    }
                    if (item.getCount() <= 0L) {
                        return;
                    }
                    if (!pc.isGm()) {
                        if (!item.getItem().isTradable()) {
                            pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                            return;
                        }
                        if (item.getItemId() == 44070 && !pc.isGm()) {
                            pc.sendPackets(new S_ServerMessage("該物品貴重.無法捨棄"));
                            return;
                        }
                        if (DropItem.START) {
                            int maxCount = ServerItemDropTable.get().getMaxCount(item.getItemId());
                            if (maxCount < 0) {
                                pc.sendPackets(new S_ServerMessage(125));
                                return;
                            }
                            if (count > maxCount) {
                                pc.sendPackets(new S_ServerMessage(166,
                                        String.valueOf(item.getName()) + "最多只能丟棄" + maxCount + "個"));
                                return;
                            }
                        }
                        if (item.get_time() != null) {
                            pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                            return;
                        }

                        if (ItemRestrictionsTable.get().checkItemRestrictions(item.getItemId(), pc)) {
                            pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                            return; //丹丹 Kevin新增限制親友
                        }
                        if (pc.get_other().get_item() != null) {
                            pc.sendPackets(new S_SystemMessage("\\fT物品正在進行託售中禁止丟棄"));
                            pc.sendPackets(new S_CloseList(pc.getId()));
                            pc.get_other().set_item(null);
                            return;
                        }
                        if (Shutdown.isSHUTDOWN) {
                            pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                            return;
                        }
                    }
                    RecordTable.get().recorddropitem(pc.getName(), item.getAllName(), count, item.getId(), pc.getIp());
                    Object[] petlist = pc.getPetList().values().toArray();
                    Object[] array;
                    int length = (array = petlist).length;
                    int i = 0;
                    while (i < length) {
                        Object petObject = array[i];
                        if (petObject instanceof L1PetInstance) {
                            L1PetInstance pet = (L1PetInstance) petObject;
                            if (item.getId() == pet.getItemObjId()) {
                                pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                                return;
                            }
                        }
                        ++i;
                    }
                    if (item.getId() == pc.getQuest().get_step(74)) {
                        pc.sendPackets(new S_ServerMessage(210, item.getItem().getName()));
                        return;
                    }
                    if (pc.getDoll(item.getId()) != null) {
                        pc.sendPackets(new S_ServerMessage(1181));
                        return;
                    }
                    if (item.isEquipped()) {
                        pc.sendPackets(new S_ServerMessage(125));
                        return;
                    }
                    if (item.getBless() >= 128) {
                        pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                        return;
                    }
                    pc.getInventory().tradeItem(item, count, pc.get_showId(),
                            World.get().getInventory(x, y, pc.getMapId()));
                    pc.turnOnOffLight();
                    return;
                }
            }
            return;
        } catch (Exception ex) {
        } finally {
            over();
        }
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }

}
