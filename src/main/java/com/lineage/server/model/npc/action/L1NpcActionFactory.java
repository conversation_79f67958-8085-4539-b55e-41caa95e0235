package com.lineage.server.model.npc.action;

import org.w3c.dom.Element;
import java.util.HashMap;
import org.apache.commons.logging.LogFactory;
import java.lang.reflect.Constructor;
import java.util.Map;
import org.apache.commons.logging.Log;

public class L1NpcActionFactory {
	private static final Log _log;
	private static Map<String, Constructor<?>> _actions;

	static {
		_log = LogFactory.getLog(L1NpcActionFactory.class);
		_actions = new HashMap();
		try {
			_actions.put("Action", loadConstructor(L1NpcListedAction.class));
			_actions.put("MakeItem", loadConstructor(L1NpcMakeItemAction.class));
			_actions.put("ShowHtml", loadConstructor(L1NpcShowHtmlAction.class));
			_actions.put("SetQuest", loadConstructor(L1NpcSetQuestAction.class));
			_actions.put("Teleport", loadConstructor(L1NpcTeleportAction.class));
		} catch (NoSuchMethodException e) {
			_log.error("NpcAction加載失敗", e);
		}
	}

	private static Constructor<?> loadConstructor(final Class<?> c) throws NoSuchMethodException {
		return c.getConstructor(Element.class);
	}

	public static L1NpcAction newAction(final Element element) {
		try {
			final Constructor<?> con = L1NpcActionFactory._actions.get(element.getNodeName());
			final L1NpcAction action = (L1NpcAction) con.newInstance(element);
			return action;
		} catch (NullPointerException e) {
			L1NpcActionFactory._log.error("未定義的NPC對話設置", e);
		} catch (Exception e2) {
			L1NpcActionFactory._log.error("NpcAction加載失敗", e2);
		}
		return null;
	}
}
