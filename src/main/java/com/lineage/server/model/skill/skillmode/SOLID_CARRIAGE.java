package com.lineage.server.model.skill.skillmode;

import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_PacketBox;
import com.lineage.server.model.L1Magic;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;

public class SOLID_CARRIAGE extends SkillMode {
	@Override
	public int start(final L1PcInstance srcpc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		final L1PcInstance pc = (L1PcInstance) cha;
		if (pc.getInventory().getTypeEquipped(2, 7) >= 1 || pc.getInventory().getTypeEquipped(2, 13) >= 1) {
			pc.setSkillEffect(90, integer * 1000);
			pc.sendPackets(new S_PacketBox(132, pc.getEr()));
		} else {
			pc.sendPackets(new S_ServerMessage("你並未裝備盾牌或臂甲。"));
		}
		return dmg;
	}

	@Override
	public int start(final L1NpcInstance npc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		return dmg;
	}

	@Override
	public void start(final L1PcInstance srcpc, final Object obj) throws Exception {
	}

	@Override
	public void stop(final L1Character cha) throws Exception {
		if (cha instanceof L1PcInstance) {
			final L1PcInstance pc = (L1PcInstance) cha;
			pc.sendPackets(new S_PacketBox(132, pc.getEr()));
		}
	}
}
