package com.lineage.server.model.skill.skillmode;

import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.config.ConfigElfSkill;
import com.lineage.server.model.L1Magic;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;

public class BLOODY_SOUL extends SkillMode {
	@Override
	public int start(final L1PcInstance srcpc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		int rr = 0;
		if (srcpc.getlogpcpower_SkillFor2() != 0 && srcpc.getlogpcpower_SkillFor2() > 0) {
			rr += srcpc.getlogpcpower_SkillFor2() * 2;
		}
		srcpc.setCurrentMp(srcpc.getCurrentMp() + ConfigElfSkill.BLOODY_SOULADDMP + rr);
		return dmg;
	}

	@Override
	public int start(final L1NpcInstance npc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		return dmg;
	}

	@Override
	public void start(final L1PcInstance srcpc, final Object obj) throws Exception {
	}

	@Override
	public void stop(final L1Character cha) throws Exception {
	}
}
