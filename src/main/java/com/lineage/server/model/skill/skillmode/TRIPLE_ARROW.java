package com.lineage.server.model.skill.skillmode;

import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SkillSound;
import com.lineage.config.ConfigElfSkill;
import com.lineage.server.datatables.SprTable;
import com.lineage.server.model.L1Magic;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;

public class TRIPLE_ARROW extends SkillMode {
	@Override
	public int start(final L1PcInstance srcpc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		final int playerGFX = srcpc.getTempCharGfx();
		final int attack21 = SprTable.get().getAttackSpeed(playerGFX, 21);
		if (attack21 == 0) {
			return 0;
		}
		if (ConfigElfSkill.TRIPLE_ARROW_DMG > 1.0) {
			srcpc.setIsTRIPLE_ARROW(true);
		}
		srcpc.setTripleArrow(true);
		int i = 0;
		while (i < 3) {
			cha.onAction(srcpc);
			++i;
		}
		if (ConfigElfSkill.TRIPLE_ARROW_DMG > 1.0) {
			srcpc.setIsTRIPLE_ARROW(false);
		}
		srcpc.sendPacketsAll(new S_SkillSound(srcpc.getId(), 11764));
		return dmg;
	}

	@Override
	public int start(final L1NpcInstance npc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		cha.setTripleArrow(true);
		int i = 3;
		while (i > 0) {
			npc.attackTarget(cha);
			--i;
		}
		npc.broadcastPacketAll(new S_SkillSound(npc.getId(), 11764));
		return dmg;
	}

	@Override
	public void start(final L1PcInstance srcpc, final Object obj) throws Exception {
	}

	@Override
	public void stop(final L1Character cha) throws Exception {
	}
}
