package com.lineage.server.model.skill.skillmode;

import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.serverpackets.S_Paralysis;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.L1Teleport;
import com.lineage.server.model.L1CastleLocation;
import com.lineage.server.world.World;
import com.lineage.server.model.L1Magic;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;

public class RUN_CLAN extends SkillMode {
	@Override
	public int start(final L1PcInstance srcpc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		final L1PcInstance pc = (L1PcInstance) cha;
		final L1PcInstance clanPc = (L1PcInstance) World.get().findObject(integer);
		if (clanPc != null) {
			if (pc.getMap().isEscapable() || pc.isGm()) {
				final boolean castle_area = L1CastleLocation.checkInAllWarArea(clanPc.getX(), clanPc.getY(),
						clanPc.getMapId());
				if (!castle_area && (clanPc.getMapId() == 0 || clanPc.getMapId() == 4 || clanPc.getMapId() == 304)) {
					L1Teleport.teleport(pc, clanPc.getX(), clanPc.getY(), clanPc.getMapId(), 5, true);
					return 0;
				}
				pc.sendPackets(new S_ServerMessage(1192));
				pc.sendPackets(new S_Paralysis(7, false));
			} else {
				pc.sendPackets(new S_ServerMessage(647));
				pc.sendPackets(new S_Paralysis(7, false));
			}
		}
		return 0;
	}

	@Override
	public int start(final L1NpcInstance npc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		return 0;
	}

	@Override
	public void start(final L1PcInstance srcpc, final Object obj) throws Exception {
	}

	@Override
	public void stop(final L1Character cha) throws Exception {
	}
}
