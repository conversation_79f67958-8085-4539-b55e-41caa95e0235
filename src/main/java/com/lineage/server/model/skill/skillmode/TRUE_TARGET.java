package com.lineage.server.model.skill.skillmode;

import com.lineage.server.model.Instance.L1EffectInstance;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.L1Magic;
import com.lineage.server.serverpackets.S_TrueTarget;
import com.lineage.server.world.WorldClan;
import com.lineage.server.world.WorldEffect;

import java.util.ArrayList;
import java.util.Iterator;

public class TRUE_TARGET extends SkillMode {
    @Override
    /**
     * 精準目標技能使用
     */
    public int start(L1PcInstance srcpc, L1Character cha, L1Magic magic, int integer)
            throws Exception {
        final int dmg = 0;
        Iterator<L1EffectInstance> iterator = WorldEffect.get().all().iterator();
        while (iterator.hasNext()) {
            L1EffectInstance eff = iterator.next();
            if (eff.getMaster() == srcpc && eff.getNpcId() == 86131) {
                eff.deleteMe();
                break;
            }
        }
        ArrayList<L1EffectInstance> effectlist = cha.get_TrueTargetEffectList();
        L1EffectInstance tgeffect = null;
        if (effectlist != null) {
            Iterator<L1EffectInstance> iterator2 = effectlist.iterator();
            while (iterator2.hasNext()) {
                L1EffectInstance effect = iterator2.next();
                if (effect.getMaster() == srcpc) {
                    tgeffect = effect;
                    break;
                }
            }
        }
//        if (tgeffect == null || tgeffect.destroyed()) {
        //Kevin 精準目標(利用NPC做明顯化)
        //tgeffect = L1SpawnUtil.spawnTrueTargetEffect(86131, 3, cha, srcpc, 0, 12299);
//            cha.add_TrueTargetEffect(tgeffect);
//        }
        if (!cha.hasSkillEffect(113)) {
            cha.setSkillEffect(113, integer * 1000);
        }
        if (srcpc.getClan() != null) {
            L1PcInstance[] onlinemembers = WorldClan.get().getClan(srcpc.getClanname()).getOnlineClanMember();
            L1PcInstance[] array;
            int length = (array = onlinemembers).length;
            int i = 0;
            while (i < length) {
                L1PcInstance clanmember = array[i];
                clanmember.sendPackets(new S_TrueTarget(cha.getId(), clanmember.getId(), srcpc.getText()));
                ++i;
            }
        } else {
            srcpc.sendPackets(new S_TrueTarget(cha.getId(), srcpc.getId(), srcpc.getText()));
        }
        srcpc.setText("");
        return 0;
    }

    @Override
    public int start(L1NpcInstance npc, L1Character cha, L1Magic magic, int integer)
            throws Exception {
        final int dmg = 0;
        return 0;
    }

    @Override
    public void start(L1PcInstance srcpc, Object obj) throws Exception {
    }

    @Override
    public void stop(L1Character cha) throws Exception {
    }
}
