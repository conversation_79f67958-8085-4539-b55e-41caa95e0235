package com.lineage.server.model;

import com.lineage.config.ConfigRate;

public class L1ItemUpdata {
	public static double enchant_wepon_up9(double enchant_level_tmp) {
		if (enchant_level_tmp <= 0.0) {
			enchant_level_tmp = 1.0;
		}
		return (100.0 + enchant_level_tmp * ConfigRate.ENCHANT_CHANCE_WEAPON) / (enchant_level_tmp * 2.0);
	}

	public static double enchant_wepon_dn9(double enchant_level_tmp) {
		if (enchant_level_tmp <= 0.0) {
			enchant_level_tmp = 1.0;
		}
		return (100.0 + enchant_level_tmp * ConfigRate.ENCHANT_CHANCE_WEAPON) / enchant_level_tmp;
	}

	public static double enchant_armor_up9(double enchant_level_tmp) {
		if (enchant_level_tmp <= 0.0) {
			enchant_level_tmp = 1.0;
		}
		return (100.0 + enchant_level_tmp * ConfigRate.ENCHANT_CHANCE_ARMOR) / (enchant_level_tmp * 2.0);
	}

	public static double enchant_armor_dn9(double enchant_level_tmp) {
		if (enchant_level_tmp <= 0.0) {
			enchant_level_tmp = 1.0;
		}
		return (100.0 + enchant_level_tmp * ConfigRate.ENCHANT_CHANCE_ARMOR) / enchant_level_tmp;
	}
}
