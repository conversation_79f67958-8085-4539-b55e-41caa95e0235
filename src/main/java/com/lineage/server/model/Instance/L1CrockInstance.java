package com.lineage.server.model.Instance;

import com.lineage.server.model.L1AttackMode;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.L1AttackPc;
import com.lineage.server.templates.L1Npc;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class L1CrockInstance extends L1NpcInstance {
	private static final long serialVersionUID = 1L;
	private static final Log _log;

	static {
		_log = LogFactory.getLog(L1CrockInstance.class);
	}

	public L1CrockInstance(final L1Npc template) {
		super(template);
	}

	@Override
	public void onAction(final L1PcInstance pc) {
		try {
			final L1AttackMode attack = new L1AttackPc(pc, this);
			attack.action();
		} catch (Exception e) {
			L1CrockInstance._log.error(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public void onNpcAI() {
	}

	@Override
	public void onTalkAction(final L1PcInstance player) {
	}

	@Override
	public void onFinalAction(final L1PcInstance player, final String action) {
	}
}
