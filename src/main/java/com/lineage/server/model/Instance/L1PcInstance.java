package com.lineage.server.model.Instance;

import com.add.L1PcUnlock;
import com.add.NewAutoPractice;
import com.eric.gui.J_Main;
import com.lineage.config.*;
import com.lineage.data.event.*;
import com.lineage.data.quest.Chapter01R;
import com.lineage.echo.ClientExecutor;
import com.lineage.echo.EncryptExecutor;
import com.lineage.server.clientpackets.AcceleratorChecker;
import com.lineage.server.datatables.*;
import com.lineage.server.datatables.lock.*;
import com.lineage.server.datatables.sql.CharacterTable;
import com.lineage.server.model.*;
import com.lineage.server.model.SoulTower.SoulTowerThread;
import com.lineage.server.model.classes.L1ClassFeature;
import com.lineage.server.model.monitor.L1PcInvisDelay;
import com.lineage.server.model.monitor.PcAttackThread;
import com.lineage.server.model.skill.L1SkillUse;
import com.lineage.server.serverpackets.*;
import com.lineage.server.templates.*;
import com.lineage.server.thread.GeneralThreadPool;
import com.lineage.server.timecontroller.pc.MapTimerThread;
import com.lineage.server.timecontroller.server.ServerUseMapTimer;
import com.lineage.server.timecontroller.server.ServerWarExecutor;
import com.lineage.server.utils.*;
import com.lineage.server.world.World;
import com.lineage.server.world.WorldClan;
import com.lineage.server.world.WorldQuest;
import com.lineage.server.world.WorldWar;
import com.lineage.william.*;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Random;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

import static com.lineage.server.model.skill.L1SkillId.STATUS_BRAVE3;

public class L1PcInstance extends L1Character {
    public static final int CLASSID_KNIGHT_FEMALE = 48;
    public static final int CLASSID_ELF_MALE = 138;
    public static final int CLASSID_ELF_FEMALE = 37;
    public static final int CLASSID_WIZARD_MALE = 734;
    public static final int CLASSID_WIZARD_FEMALE = 1186;
    public static final int CLASSID_DARK_ELF_MALE = 2786;
    public static final int CLASSID_DARK_ELF_FEMALE = 2796;
    public static final int CLASSID_PRINCE = 0;
    public static final int CLASSID_PRINCESS = 1;
    public static final int CLASSID_DRAGON_KNIGHT_MALE = 6658;
    public static final int CLASSID_DRAGON_KNIGHT_FEMALE = 6661;
    public static final int CLASSID_ILLUSIONIST_MALE = 6671;
    public static final int CLASSID_ILLUSIONIST_FEMALE = 6650;
    public static final int REGENSTATE_NONE = 4;
    public static final int REGENSTATE_MOVE = 2;
    public static final int REGENSTATE_ATTACK = 1;
    public static final int INTERVAL_BY_AWAKE = 4;
    public static final int CLASSID_KNIGHT_MALE = 61;
    private static final Log _log;
    private static final long serialVersionUID = 1L;
    private static final Map<Long, Double> _magicDamagerList;
    private static final long DELAY_INVIS = 3000L;
    private static final Random _random;
    private static final boolean _debug;

    static {
        _log = LogFactory.getLog(L1PcInstance.class);
        _random = new Random();
        _debug = Config.DEBUG;
        _magicDamagerList = new HashMap<>();
    }

    protected final L1HateList _hateList;
    private final Map<Integer, L1SkinInstance> _skins;
    private final L1Karma _karma;
    private final L1PcInventory _inventory;
    private final L1DwarfInventory _dwarf;
    private final L1DwarfForChaInventory _dwarfForCha;
    private final L1DwarfForElfInventory _dwarfForElf;
    private final L1ExcludingList _excludingList;
    private final Map<Integer, L1ItemPower_text> _allpowers;
    private final AcceleratorChecker _acceleratorChecker;
    private final L1Inventory _tradewindow;
    private final ArrayList<String> _attackenemy;
    private final ArrayList<String> _Badattackenemy;
    private final ArrayList<Integer> _skillList;
    private final ArrayList<L1PrivateShopSellList> _sellList;
    private final ArrayList<L1PrivateShopBuyList> _buyList;
    private final L1PcQuest _quest;
    private final L1ActionPc _action;
    private final L1ActionPet _actionPet;
    private final L1ActionSummon _actionSummon;
    private final L1EquipmentSlot _equipSlot;
    private final Object _invisTimerMonitor;
    private final ArrayList<L1TradeItem> _trade_items;
    private final int[] _doll_get;
    private final int _state;
    private final ArrayList<String> _InviteList;
    private final ArrayList<String> _cmalist;
    private final ArrayList<Integer> soulHp;
    private final ArrayList<Integer> _autobuff;
    private final ArrayList<Integer> _autoattack;
    public short _originalHpr;
    public short _originalMpr;
    public long _oldTime;
    public short _temp;
    public short _baseMaxMpc;
    public double _PartyExp;
    public int[] _reward_Weapon;
    public int ValakasStatus;
    public boolean _TRIPLEARROW;
    protected NpcMoveExecutor _pcMove;
    int _venom_resist;
    PcAttackThread attackThread;
    Thread _tempThread;
    private L1PcRewardPrestigeGfxTimer _gfxTimer4;
    private boolean _isKill;
    private short _hpr;
    private short _trueHpr;
    private short _mpr;
    private short _trueMpr;
    private boolean _mpRegenActive;
    private boolean _mpReductionActiveByAwake;
    private boolean _hpRegenActive;
    private int _hpRegenType;
    private int _hpRegenState;
    private int _mpRegenType;
    private int _mpRegenState;
    private int _awakeMprTime;
    private int _awakeSkillId;
    private int _old_lawful;
    private int _old_karma;
    private boolean _jl1;
    private boolean _jl2;
    private boolean _jl3;
    private boolean _el1;
    private boolean _el2;
    private boolean _el3;
    private long _old_exp;
    private boolean _isCHAOTIC;
    private L1ClassFeature _classFeature;
    private int _PKcount;
    private int _PkCountForElf;
    private int _clanid;
    private String clanname;
    private int _clanRank;
    private byte _sex;
    private byte[] _shopChat;
    private boolean _isPrivateShop;
    private boolean _isTradingInPrivateShop;
    private int _partnersPrivateShopItemCount;
    private EncryptExecutor _out;
    private int _originalEr;
    private ClientExecutor _netConnection;
    private int _classId;
    private int _type;
    private long _exp;
    private boolean _gm;
    private boolean _monitor;
    private boolean _gmInvis;
    private short _accessLevel;
    private int _currentWeapon;
    private L1ItemInstance _weapon;
    private L1Party _party;
    private L1ChatParty _chatParty;
    private int _partyID;
    private int _tradeID;
    private boolean _tradeOk;
    private int _tempID;
    private boolean _isTeleport;
    private boolean _isDrink;
    private boolean _isGres;
    private String _accountName;
    private short _baseMaxHp;
    private short _baseMaxMp;
    private int _baseAc;
    private int _originalAc;
    private int _baseStr;
    private int _baseCon;
    private int _baseDex;
    private int _baseCha;
    private int _baseInt;
    private int _baseWis;
    private int _originalStr;
    private int _originalCon;
    private int _originalDex;
    private int _originalCha;
    private int _originalInt;
    private int _originalWis;
    private int _originalDmgup;
    private int _originalBowDmgup;
    private int _originalHitup;
    private int _originalBowHitup;
    private int _originalMr;
    private int _originalMagicHit;
    private int _originalMagicCritical;
    private int _originalMagicConsumeReduction;
    private int _originalMagicDamage;
    private int _originalHpup;
    private int _originalMpup;
    private int _baseDmgup;
    private int _baseBowDmgup;
    private int _baseHitup;
    private int _baseBowHitup;
    private int _baseMr;
    private int _advenHp;
    private int _advenMp;
    private int _highLevel;
    private int _bonusStats;
    private int _elixirStats;
    private int _elfAttr;
    private int _expRes;
    private int _partnerId;
    private int _onlineStatus;
    private int _homeTownId;
    private int _contribution;
    private int _hellTime;
    private boolean _banned;
    private int _food;
    private int invisDelayCounter;
    private boolean _ghost;
    private int _ghostTime;
    private boolean _ghostCanTalk;
    private boolean _isReserveGhost;
    private int _ghostSaveLocX;
    private int _ghostSaveLocY;
    private short _ghostSaveMapId;
    private int _ghostSaveHeading;
    private Timestamp _lastPk;
    private Timestamp _lastPkForElf;
    private Timestamp _deleteTime;
    private double _weightUP;
    private int _weightReduction;
    private int _originalStrWeightReduction;
    private int _originalConWeightReduction;
    private int _hasteItemEquipped;
    private int _damageReductionByArmor;
    private int _hitModifierByArmor;
    private int _dmgModifierByArmor;
    private int _bowHitModifierByArmor;
    private int _bowDmgModifierByArmor;
    private boolean _gresValid;
    private boolean _isFishing;
    private int _fishX;
    private int _fishY;
    private int _cookingId;
    private int _dessertId;
    private int _teleportX;
    private int _teleportY;
    private short _teleportMapId;
    private int _teleportHeading;
    private int _tempCharGfxAtDead;
    private boolean _isCanWhisper;
    private boolean _isShowTradeChat;
    private boolean _isShowWorldChat;
    private int _fightId;
    private byte _chatCount;
    private long _oldChatTimeInMillis;
    private int _callClanId;
    private int _callClanHeading;
    private boolean _isInCharReset;
    private int _tempLevel;
    private int _tempMaxLevel;
    private boolean _isSummonMonster;
    private boolean _isShapeChange;
    private String _text;
    private byte[] _textByte;
    private L1PcOther _other;
    private L1PcOther1 _other1;
    private L1PcOther2 _other2;
    private L1PcOther3 _other3;
    private L1PcOtherList _otherList;
    private int _oleLocX;
    private int _oleLocY;
    private L1DeInstance _outChat;
    private long _h_time;
    private boolean _mazu;
    private long _mazu_time;
    private int _int1;
    private int _int2;
    private int _evasion;
    private double _expadd;
    private int _dd1;
    private int _dd2;
    private boolean _isFoeSlayer;
    private long _weaknss_t;
    private int _actionId;
    private Chapter01R _hardin;
    private int _unfreezingTime;
    private int _misslocTime;
    private L1User_Power _c_power;
    private int _dice_hp;
    private int _sucking_hp;
    private int _dice_mp;
    private int _sucking_mp;
    private int _double_dmg;
    private int _lift;
    private int _magic_modifier_dmg;
    private int _magic_reduction_dmg;
    private boolean _rname;
    private boolean _retitle;
    private int _repass;
    private int _mode_id;
    private boolean _check_item;
    private boolean _vip_1;
    private boolean _vip_2;
    private boolean _vip_3;
    private boolean _vip_4;
    private long _global_time;
    private int _doll_hpr;
    private int _doll_hpr_time;
    private int _doll_hpr_time_src;
    private int _doll_mpr;
    private int _doll_mpr_time;
    private int _doll_mpr_time_src;
    private int _doll_get_time;
    private int _doll_get_time_src;
    private String _board_title;
    private String _board_content;
    private long _spr_move_time;
    private long _spr_attack_time;
    private long _spr_skill_time;
    private int _delete_time;
    private int _up_hp_potion;
    private int _uhp_number;
    private AcceleratorChecker _speed;
    private int _arena;
    private int _temp_adena;
    private long _ss_time;
    private int _ss;
    private int killCount;
    private int _meteLevel;
    private L1MeteAbility _meteAbility;
    private boolean _isProtector;
    private L1Apprentice _apprentice;
    private int _tempType;
    private Timestamp _punishTime;
    private int _magicDmgModifier;
    private int _magicDmgReduction;
    private int _elitePlateMail_Fafurion;
    private int _fafurion_hpmin;
    private int _fafurion_hpmax;
    private int _elitePlateMail_Lindvior;
    private int _lindvior_mpmin;
    private int _lindvior_mpmax;
    private int _hades_cloak;
    private int _hades_cloak_dmgmin;
    private int _hades_cloak_dmgmax;
    private int _Hexagram_Magic_Rune;
    private int _hexagram_hpmin;
    private int _hexagram_hpmax;
    private int _hexagram_gfx;
    private int _dimiter_mpr_rnd;
    private int _dimiter_mpmin;
    private int _dimiter_mpmax;
    private int _dimiter_bless;
    private int _dimiter_time;
    private int _expPoint;
    private int _pay;
    private int _SummonId;
    private int _lap;
    private int _lapCheck;
    private boolean _order_list;
    private int followstep;
    private L1PcInstance followmaster;
    private int _fishingpoleid;
    private L1Character _target;
    private int _weaknss;
    private boolean _EffectDADIS;
    private boolean _EffectGS;
    private int _elitePlateMail_Valakas;
    private int _valakas_dmgmin;
    private int _valakas_dmgmax;
    private int _isBigHot;
    private String _bighot1;
    private String _bighot2;
    private String _bighot3;
    private String _bighot4;
    private String _bighot5;
    private String _bighot6;
    private boolean _isATeam;
    private boolean _isBTeam;
    private Timestamp _rejoinClanTime;
    private Timestamp _CreateTime;
    private int _partyType;
    private int _ubscore;
    private int _inputerror;
    private int _speederror;
    private int _banerror;
    private int _inputbanerror;
    private int _Slot;
    private boolean _itempoly;
    private boolean _itempoly1;
    private L1ItemInstance _polyscroll;
    private L1ItemInstance _polyscrol2;
    private L1ItemInstance _itembox;
    private L1EffectInstance _tomb;
    private boolean _isMagicCritical;
    private boolean _isPhantomTeleport;
    private int _rocksPrisonTime;
    private int _lastabardTime;
    private int _ivorytowerTime;
    private int _dragonvalleyTime;
    private boolean isTimeMap;
    private ConcurrentHashMap<Integer, Integer> mapTime;
    private int _clanMemberId;
    private String _clanMemberNotes;
    private int _stunlevel;
    private int _other_ReductionDmg;
    private int _Clan_ReductionDmg;
    private int _Clanmagic_reduction_dmg;
    private double _addExpByArmor;
    private int _PcContribution;
    private int _clanContribution;
    private int _clanadena;
    private String clanNameContribution;
    private boolean _checkgm;
    private boolean check_lv;
    private int _logpcpower_SkillCount;
    private int _logpcpower_SkillFor1;
    private int _logpcpower_SkillFor2;
    private int _logpcpower_SkillFor3;
    private int _logpcpower_SkillFor4;
    private int _logpcpower_SkillFor5;
    private int _EsotericSkill;
    private int _EsotericCount;
    private boolean _isEsoteric;
    private boolean _TripleArrow;
    private boolean _checklogpc;
    private int _savepclog;
    private int _ReductionDmg;
    private int _pcdmg;
    private int _paycount;
    private int _ArmorCount1;
    private int _logintime;
    private int _logintime1;
    private boolean ATK_ai;
    private long _shopAdenaRecord;
    private int _dolldamageReductionByArmor;
    private int _weaponMD;
    private int _weaponMDC;
    private int _reduction_dmg;
    private double _GF;
    private boolean _isTeleportToOk;
    private boolean _MOVE_STOP;
    private int _amount;
    private long _consume_point;
    private Map<Integer, Integer> _mapsList;
    private int _tempStr;
    private int _tempDex;
    private int _tempCon;
    private int _tempWis;
    private int _tempCha;
    private int _tempInt;
    private int _tempInitPoint;
    private int _tempElixirstats;
    private int weapondmg;
    private int Dmgdouble;
    private int elfweapon;
    private int _PVPdmg;
    private int _PVPdmgReduction;
    private int _attr_potion_heal;
    private int _penetrate;
    private int _attr_物理格檔;
    private int _attr_魔法格檔;
    private int _NoweaponRedmg;
    private int _addStunLevel;
    private int _loginpoly;
    private int backX;
    private int backY;
    private int backHeading;
    private boolean _isAI;
    private int _ai_number;
    private int _ai_count;
    private int _ai_error;
    private int _ai_correct;
    private int _Imperius_Tshirt_rnd;
    private int _drainingHP_min;
    private int _drainingHP_max;
    private int _MoonAmulet_rnd;
    private int _MoonAmulet_dmg_min;
    private int _MoonAmulet_dmg_max;
    private int _MoonAmulet_gfxid;
    private int _AttrAmulet_rnd;
    private int _AttrAmulet_dmg;
    private int _AttrAmulet_gfxid;
    private int _range;
    private int _day;
    private int _prestige;
    private int _prestigeLv;
    private boolean _go_guajitele;
    private long _oldexp;
    private boolean _isItemName;
    private boolean _isItemopen;
    private boolean _isfollow;
    private boolean _isfollowcheck;
    private int _poisonStatus2;
    private int _poisonStatus7;
    private boolean _isCraftsmanHeirloom;
    private boolean _isMarsSoul;
    private int _super;
    private int guaji_poly;
    private int _iceTime;
    private boolean _firstAttack;
    private int move;
    private boolean _aiRunning;
    private boolean _actived;
    private boolean _Pathfinding;
    private int _randomMoveDirection;
    private int _tguajiX;
    private int _tguajiY;
    private int _guajiMapId;
    private int _armorbreaklevel;
    private int _FoeSlayerBonusDmg;
    private int _soulHp_r;
    private int _soulHp_hpmin;
    private int _soulHp_hpmax;
    private int isSoulHp;
    private String oldtitle;
    private String vipname;
    private int _PVPdmgg;
    private int _potion_healling;
    private int _potion_heal;
    private int _weaponSkillChance;
    private double _addWeaponSkillDmg;
    private String newaititle;
    private int _newaicount;
    private int _proctctran;
    private boolean _newcharpra;
    private int _fwgj;
    private int _lslocx;
    private int _lslocy;
    private int _guaji_count;
    private int _aibig;
    private int _aismall;
    private int _newaicount_2;
    private boolean _opengfxid;
    private int _AiGxfxid;
    private int _Aierror;
    private int _add_er;
    private int moveErrorCount;
    private boolean moveStatus;
    private int _followskilltype;
    private int _followskillhp;
    private boolean _followmebuff;
    private int _ItemBlendcheckitem;
    private String _ItemBlendcheckitemname;
    private int _ItemBlendcheckitemcount;
    private int _hppotion;
    private int _pvp;
    private int _bowpvp;
    private int _followxy1;
    private int _polyarrow;
    private int callclanal;
    private int _changtype1;
    private int _changtype2;
    private int _changtype3;
    private int _changtype4;
    private int _changtype5;
    private String changtypename1;
    private String changtypename2;
    private String changtypename3;
    private String changtypename4;
    private int _pag;
    private boolean _keyenemy;
    private boolean _outenemy;
    private boolean _enemyteleport;
    private boolean _attackteleport;
    private boolean _buffskill;
    private boolean _attackskill;
    private int _itempotion;
    private int _itempotion1;
    private int _itempotion2;
    private int _itemitemid;
    private int _itemitemid1;
    private int _itemitemid2;
    private int _itemadena;
    private int _itemadena1;
    private int _itemadena2;
    private int _potioncount;
    private int _potioncount1;
    private int _potioncount2;
    private boolean _go_guajired;
    private int _ma1;
    private double _npcdmg;
    private int _newai1;
    private int _newai2;
    private int _newai3;
    private int _newai4;
    private int _newai5;
    private int _newai6;
    private int _newaiq1;
    private int _newaiq2;
    private int _newaiq3;
    private int _newaiq4;
    private int _newaiq5;
    private int _newaiq6;
    private int _newaiq7;
    private int _newaiq8;
    private int _newaiq9;
    private int _newaiq0;
    private int _npciddmg;
    private boolean _followatk;
    private boolean _followatkmagic;
    private boolean _isfollowskill26;
    private boolean _isfollowskill42;
    private boolean _isfollowskill55;
    private boolean _isfollowskill68;
    private boolean _isfollowskill160;
    private boolean _isfollowskill79;
    private boolean _isfollowskill148;
    private boolean _isfollowskill151;
    private boolean _isfollowskill149;
    private boolean _isfollowskill158;
    private boolean _isnomoveguaji;
    private boolean _Badkeyenemy;
    private boolean _Badoutenemy;
    private short _oldMapId;
    private boolean _ischeckpoly;
    private String _itemactionhtml;
    private boolean _isOutbur;
    private boolean _ischeckOutbur;
    private int _Quburcount;
    private int _WeaponTotalDmg;
    private int _weaknss1;
    private int _WeaponSkillPro;
    private int _PcMagicPro;
    private int _Save_Quest_Map1;
    private int _Save_Quest_Map2;
    private int _Save_Quest_Map3;
    private int _Save_Quest_Map4;
    private int _Save_Quest_Map5;
    private int _CardId;
    private int soulTower;
    private boolean _isarmor_setgive;
    private String _Summon_npcid;
    private int _summon_skillid;
    private int _summon_skillidmp;
    private boolean _checksummid;
    private boolean _checksummidhp;
    private boolean _mobatk;
    private int _towerIsWhat;
    private int _avatar;
    /**
     * Kevin 穿雲箭開關 0=登入預設關閉 1=登入預設開啟
     */
    private int _AllCall_clan = 1;

    //加速器檢驗用封包確認
    private LocalDateTime lastTimeAttackTime;
    private LocalDateTime lastTimeMoveTime;
    private LocalDateTime lastTimeSpellDirTime;
    private LocalDateTime lastTimeSpellNoirTime;

    //限制穿雲箭使用確認時間
    private LocalDateTime lastTimeAllCallTime;
    /**
     * 祝福点数
     */
    private int _zhufudianshu = 0;
    /**
     * 伊娃纹样
     *
     * @return
     */
    private int _yiwa = 0;
    /**
     * 伊娃纹样可强化次数
     */
    private int _yiwacishu = 0;
    /**
     * 伊娃纹样升1级的机率
     */
    private double _yiwajilv1 = 51.5;
    /**
     * 伊娃纹样升2级的机率
     */
    private double _yiwajilv2 = 20.2;
    /**
     * 伊娃纹样升3级的机率
     */
    private double _yiwajilv3 = 12.9;
    /**
     * 伊娃纹样机率等级
     */
    private int _yiwajilvdengji = 0;
    /**
     * 沙哈纹样
     *
     * @return
     */
    private int _shaha = 0;
    /**
     * 沙哈纹样可强化次数
     */
    private int _shahacishu = 0;
    /**
     * 沙哈纹样升1级的机率
     */
    private double _shahajilv1 = 51.5;
    /**
     * 沙哈纹样升2级的机率
     */
    private double _shahajilv2 = 20.2;
    /**
     * 沙哈纹样升3级的机率
     */
    private double _shahajilv3 = 12.9;
    /**
     * 沙哈纹样机率等级
     */
    private int _shahajilvdengji = 0;
    /**
     * 馬普勒纹样
     *
     * @return
     */
    private int _mapule = 0;
    /**
     * 馬普勒纹样可强化次数
     */
    private int _mapulecishu = 0;
    /**
     * 馬普勒纹样升1级的机率
     */
    private double _mapulejilv1 = 51.5;
    /**
     * 馬普勒纹样升2级的机率
     */
    private double _mapulejilv2 = 20.2;
    /**
     * 馬普勒纹样升3级的机率
     */
    private double _mapulejilv3 = 12.9;
    /**
     * 馬普勒纹样机率等级
     */
    private int _mapulejilvdengji = 0;
    /**
     * 帕格里奧纹样
     *
     * @return
     */
    private int _pageliao = 0;
    /**
     * 帕格里奧纹样可强化次数
     */
    private int _pageliaocishu = 0;
    /**
     * 帕格里奧纹样升1级的机率
     */
    private double _pageliaojilv1 = 51.5;
    /**
     * 帕格里奧纹样升2级的机率
     */
    private double _pageliaojilv2 = 20.2;
    /**
     * 帕格里奧纹样升3级的机率
     */
    private double _pageliaojilv3 = 12.9;
    /**
     * 帕格里奧纹样机率等级
     */
    private int _pageliaojilvdengji = 0;
    /**
     * 殷海薩纹样
     *
     * @return
     */
    private int _yinhaisa = 0;
    /**
     * 殷海薩纹样可强化次数
     */
    private int _yinhaisacishu = 0;
    /**
     * 殷海薩纹样升1级的机率
     */
    private double _yinhaisajilv1 = 51.5;
    /**
     * 殷海薩纹样升2级的机率
     */
    private double _yinhaisajilv2 = 20.2;
    /**
     * 殷海薩纹样升3级的机率
     */
    private double _yinhaisajilv3 = 12.9;
    /**
     * 殷海薩纹样机率等级
     */
    private int _yinhaisajilvdengji = 0;
    /**
     * 经验值增加
     */

    private double _expRateToPc = 0.0;
    //竊聽一般
    private Boolean chatListenNormal = false;
    //竊聽隊伍
    private Boolean chatListenParty = false;
    //竊聽血盟
    private Boolean chatListenClan = false;
    //竊聽密語
    private Boolean chatListenWhisper = false;
    //竊聽大喊
    private Boolean chatListenShout = false;
    //竊聽聯盟
    private Boolean chatListenAlliance = false;

    public L1PcInstance() {
        _skins = new HashMap<>();
        _isKill = false;
        _attackenemy = new ArrayList<>();
        _Badattackenemy = new ArrayList<>();
        _hpr = 0;
        _trueHpr = 0;
        _mpr = 0;
        _trueMpr = 0;
        _originalHpr = 0;
        _originalMpr = 0;
        _hpRegenType = 0;
        _hpRegenState = 4;
        _mpRegenType = 0;
        _mpRegenState = 4;
        _awakeMprTime = 0;
        _awakeSkillId = 0;
        _jl1 = false;
        _jl2 = false;
        _jl3 = false;
        _el1 = false;
        _el2 = false;
        _el3 = false;
        _isCHAOTIC = false;
        _skillList = new ArrayList<>();
        _classFeature = null;
        _sellList = new ArrayList<>();
        _buyList = new ArrayList<>();
        _isPrivateShop = false;
        _isTradingInPrivateShop = false;
        _partnersPrivateShopItemCount = 0;
        _oldTime = 0L;
        _originalEr = 0;
        _netConnection = null;
        _karma = new L1Karma();
        _isTeleport = false;
        _isDrink = false;
        _isGres = false;
        _baseMaxHp = 0;
        _baseMaxMp = 0;
        _baseAc = 0;
        _originalAc = 0;
        _baseStr = 0;
        _baseCon = 0;
        _baseDex = 0;
        _baseCha = 0;
        _baseInt = 0;
        _baseWis = 0;
        _originalStr = 0;
        _originalCon = 0;
        _originalDex = 0;
        _originalCha = 0;
        _originalInt = 0;
        _originalWis = 0;
        _originalDmgup = 0;
        _originalBowDmgup = 0;
        _originalHitup = 0;
        _originalBowHitup = 0;
        _originalMr = 0;
        _originalMagicHit = 0;
        _originalMagicCritical = 0;
        _originalMagicConsumeReduction = 0;
        _originalMagicDamage = 0;
        _originalHpup = 0;
        _originalMpup = 0;
        _baseDmgup = 0;
        _baseBowDmgup = 0;
        _baseHitup = 0;
        _baseBowHitup = 0;
        _baseMr = 0;
        invisDelayCounter = 0;
        _invisTimerMonitor = new Object();
        _ghost = false;
        _ghostTime = -1;
        _ghostCanTalk = true;
        _isReserveGhost = false;
        _ghostSaveLocX = 0;
        _ghostSaveLocY = 0;
        _ghostSaveMapId = 0;
        _ghostSaveHeading = 0;
        _weightUP = 1.0;
        _weightReduction = 0;
        _originalStrWeightReduction = 0;
        _originalConWeightReduction = 0;
        _hasteItemEquipped = 0;
        _damageReductionByArmor = 0;
        _hitModifierByArmor = 0;
        _dmgModifierByArmor = 0;
        _bowHitModifierByArmor = 0;
        _bowDmgModifierByArmor = 0;
        _isFishing = false;
        _fishX = -1;
        _fishY = -1;
        _cookingId = 0;
        _dessertId = 0;
        _excludingList = new L1ExcludingList();
        _teleportX = 0;
        _teleportY = 0;
        _teleportMapId = 0;
        _teleportHeading = 0;
        _isCanWhisper = true;
        _isShowTradeChat = true;
        _isShowWorldChat = true;
        _chatCount = 0;
        _oldChatTimeInMillis = 0L;
        _isInCharReset = false;
        _tempLevel = 1;
        _tempMaxLevel = 1;
        _isSummonMonster = false;
        _isShapeChange = false;
        _textByte = null;
        _outChat = null;
        _mazu = false;
        _mazu_time = 0L;
        _expadd = 0.0;
        _isFoeSlayer = false;
        _actionId = -1;
        _allpowers = new ConcurrentHashMap<>();
        _magic_modifier_dmg = 0;
        _magic_reduction_dmg = 0;
        _rname = false;
        _retitle = false;
        _repass = 0;
        _trade_items = new ArrayList<>();
        _mode_id = 0;
        _check_item = false;
        _vip_1 = false;
        _vip_2 = false;
        _vip_3 = false;
        _vip_4 = false;
        _global_time = 0L;
        _doll_hpr = 0;
        _doll_hpr_time = 0;
        _doll_hpr_time_src = 0;
        _doll_mpr = 0;
        _doll_mpr_time = 0;
        _doll_mpr_time_src = 0;
        _doll_get = new int[2];
        _doll_get_time = 0;
        _doll_get_time_src = 0;
        _spr_move_time = 0L;
        _spr_attack_time = 0L;
        _spr_skill_time = 0L;
        _delete_time = 0;
        _up_hp_potion = 0;
        _venom_resist = 0;
        _speed = null;
        _arena = 0;
        _temp_adena = 0;
        _ss_time = 0L;
        _ss = 0;
        _elitePlateMail_Fafurion = 0;
        _fafurion_hpmin = 0;
        _fafurion_hpmax = 0;
        _SummonId = 0;
        _lap = 1;
        _lapCheck = 0;
        _order_list = false;
        _state = 0;
        _target = null;
        _InviteList = new ArrayList<>();
        _cmalist = new ArrayList<>();
        _isATeam = false;
        _isBTeam = false;
        _acceleratorChecker = new AcceleratorChecker(this);
        _Slot = 0;
        _itempoly = false;
        _itempoly1 = false;
        _stunlevel = 0;
        _other_ReductionDmg = 0;
        _Clan_ReductionDmg = 0;
        _Clanmagic_reduction_dmg = 0;
        _addExpByArmor = 0.0;
        _PcContribution = 0;
        _clanContribution = 0;
        _clanadena = 0;
        _checkgm = false;
        check_lv = false;
        _EsotericSkill = 0;
        _EsotericCount = 0;
        _isEsoteric = false;
        _TripleArrow = false;
        _checklogpc = false;
        _savepclog = 0;
        _ReductionDmg = 0;
        _pcdmg = 0;
        _paycount = 0;
        _ArmorCount1 = 0;
        _logintime = 0;
        _logintime1 = 0;
        _PartyExp = 0.0;
        ATK_ai = false;
        _dolldamageReductionByArmor = 0;
        _reduction_dmg = 0;
        _isTeleportToOk = false;
        _MOVE_STOP = false;
        _amount = 0;
        _consume_point = 0L;
        _tempStr = 0;
        _tempDex = 0;
        _tempCon = 0;
        _tempWis = 0;
        _tempCha = 0;
        _tempInt = 0;
        _tempInitPoint = 0;
        _tempElixirstats = 0;
        weapondmg = 0;
        _reward_Weapon = new int[9];
        elfweapon = 0;
        _PVPdmg = 0;
        _PVPdmgReduction = 0;
        _attr_potion_heal = 0;
        _penetrate = 0;
        _attr_物理格檔 = 0;
        _attr_魔法格檔 = 0;
        _addStunLevel = 0;
        _loginpoly = 0;
        _isAI = false;
        _ai_count = 0;
        _ai_error = 0;
        _ai_correct = 0;
        _range = 0;
        _day = 0;
        _prestige = 0;
        _prestigeLv = 0;
        _go_guajitele = false;
        _oldexp = 0L;
        _isItemName = false;
        _isItemopen = false;
        _isfollow = false;
        _isfollowcheck = false;
        guaji_poly = 0;
        _hateList = new L1HateList();
        _firstAttack = false;
        _pcMove = null;
        move = 0;
        _aiRunning = false;
        _actived = false;
        _Pathfinding = false;
        _randomMoveDirection = 0;
        _tguajiX = 0;
        _tguajiY = 0;
        _guajiMapId = 0;
        _armorbreaklevel = 0;
        _soulHp_r = 0;
        _soulHp_hpmin = 0;
        _soulHp_hpmax = 0;
        isSoulHp = 0;
        soulHp = new ArrayList<>();
        _PVPdmgg = 0;
        _weaponSkillChance = 0;
        _addWeaponSkillDmg = 0.0;
        _newcharpra = false;
        _lslocx = 0;
        _lslocy = 0;
        _opengfxid = true;
        ValakasStatus = 0;
        _followmebuff = false;
        _ItemBlendcheckitem = 0;
        _ItemBlendcheckitemcount = 0;
        _hppotion = 0;
        _pvp = 0;
        _bowpvp = 0;
        _followxy1 = 1;
        _polyarrow = 66;
        _changtype1 = 0;
        _changtype2 = 0;
        _changtype3 = 0;
        _changtype4 = 0;
        _changtype5 = 0;
        _pag = 0;
        _keyenemy = false;
        _outenemy = false;
        _enemyteleport = false;
        _attackteleport = false;
        _autobuff = new ArrayList<>();
        _autoattack = new ArrayList<>();
        _buffskill = false;
        _attackskill = false;
        _go_guajired = false;
        _ma1 = 50;
        _npcdmg = 0.0;
        _newai1 = 0;
        _newai2 = 0;
        _newai3 = 0;
        _newai4 = 0;
        _newai5 = 0;
        _newai6 = 0;
        _newaiq1 = 0;
        _newaiq2 = 0;
        _newaiq3 = 0;
        _newaiq4 = 0;
        _newaiq5 = 0;
        _newaiq6 = 0;
        _newaiq7 = 0;
        _newaiq8 = 0;
        _newaiq9 = 0;
        _newaiq0 = 0;
        _npciddmg = 0;
        _followatk = false;
        _followatkmagic = false;
        _isfollowskill26 = false;
        _isfollowskill42 = false;
        _isfollowskill55 = false;
        _isfollowskill68 = false;
        _isfollowskill160 = false;
        _isfollowskill79 = false;
        _isfollowskill148 = false;
        _isfollowskill151 = false;
        _isfollowskill149 = false;
        _isfollowskill158 = false;
        _isnomoveguaji = false;
        _Badkeyenemy = false;
        _Badoutenemy = false;
        _oldMapId = 0;
        _ischeckpoly = false;
        _isOutbur = false;
        _ischeckOutbur = false;
        _WeaponTotalDmg = 0;
        _WeaponSkillPro = 0;
        _PcMagicPro = 0;
        _Save_Quest_Map1 = 0;
        _Save_Quest_Map2 = 0;
        _Save_Quest_Map3 = 0;
        _Save_Quest_Map4 = 0;
        _Save_Quest_Map5 = 0;
        _CardId = 0;
        soulTower = 0;
        _isarmor_setgive = false;
        _summon_skillid = 0;
        _summon_skillidmp = 0;
        _checksummid = false;
        _checksummidhp = false;
        _mobatk = false;
        _TRIPLEARROW = false;
        _accessLevel = 0;
        _currentWeapon = 0;
        _inventory = new L1PcInventory(this);
        _dwarf = new L1DwarfInventory(this);
        _dwarfForCha = new L1DwarfForChaInventory(this);
        _dwarfForElf = new L1DwarfForElfInventory(this);
        _tradewindow = new L1Inventory();
        _quest = new L1PcQuest(this);
        _action = new L1ActionPc(this);
        _actionPet = new L1ActionPet(this);
        _actionSummon = new L1ActionSummon(this);
        _equipSlot = new L1EquipmentSlot(this);
        LocalDateTime localNow = LocalDateTime.now();
        lastTimeAttackTime = localNow;
        lastTimeMoveTime = localNow;
        lastTimeSpellDirTime = localNow;
        lastTimeSpellNoirTime = localNow;
    }

    public static void load() {
        double newdmg = 100.0;
        long i = 2000L;
        while (i > 0L) {
            if (i % 100L == 0L) {
                newdmg -= 3.33;
            }
            L1PcInstance._magicDamagerList.put(Long.valueOf(i), Double.valueOf(newdmg));
            --i;
        }
    }

    public static L1PcInstance load(String charName) {
        L1PcInstance result = null;
        try {
            result = CharacterTable.get().loadCharacter(charName);
        } catch (Exception e) {
            L1PcInstance._log.error(e.getLocalizedMessage(), e);
        }
        return result;
    }

    public static void bowisbuy(String info) {
        try {
            BufferedWriter out = new BufferedWriter(new FileWriter("玩家紀錄/物品噴掉紀錄.txt", true));
            out.write(String.valueOf(info) + "\r\n");
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public LocalDateTime getLastTimeAllCallTime() {
        return lastTimeAllCallTime;
    }

    public void setLastTimeAllCallTime(LocalDateTime lastTimeAllCallTime) {
        this.lastTimeAllCallTime = lastTimeAllCallTime;
    }

    public LocalDateTime getLastTimeAttackTime() {
        return lastTimeAttackTime;
    }

    public void setLastTimeAttackTime(LocalDateTime lastTimeAttackTime) {
        this.lastTimeAttackTime = lastTimeAttackTime;
    }

    public LocalDateTime getLastTimeMoveTime() {
        return lastTimeMoveTime;
    }

    public void setLastTimeMoveTime(LocalDateTime lastTimeMoveTime) {
        this.lastTimeMoveTime = lastTimeMoveTime;
    }

    public LocalDateTime getLastTimeSpellDirTime() {
        return lastTimeSpellDirTime;
    }

    public void setLastTimeSpellDirTime(LocalDateTime lastTimeSpellDirTime) {
        this.lastTimeSpellDirTime = lastTimeSpellDirTime;
    }

    public LocalDateTime getLastTimeSpellNoirTime() {
        return lastTimeSpellNoirTime;
    }

    public void setLastTimeSpellNoirTime(LocalDateTime lastTimeSpellNoirTime) {
        this.lastTimeSpellNoirTime = lastTimeSpellNoirTime;
    }

    public void load_src() {
        _old_exp = getExp();
        _old_lawful = getLawful();
        _old_karma = getKarma();
    }

    public boolean is_isKill() {
        return _isKill;
    }

    public void set_isKill(boolean _isKill) {
        this._isKill = _isKill;
    }

    public short getHpr() {
        return _hpr;
    }

    public void addHpr(int i) {
        _trueHpr += (short) i;
        _hpr = (short) Math.max(0, _trueHpr);
    }

    public short getMpr() {
        return _mpr;
    }

    public void addMpr(int i) {
        _trueMpr += (short) i;
        _mpr = (short) Math.max(0, _trueMpr);
    }

    public short getOriginalHpr() {
        return _originalHpr;
    }

    public short getOriginalMpr() {
        return _originalMpr;
    }

    public int getHpRegenState() {
        return _hpRegenState;
    }

    public void set_hpRegenType(int hpRegenType) {
        _hpRegenType = hpRegenType;
    }

    public int hpRegenType() {
        return _hpRegenType;
    }

    private int regenMax() {
        int[] lvlTable = {30, 25, 20, 16, 14, 12, 11, 10, 9, 3, 2};
        int regenLvl = Math.min(10, getLevel());
        if (30 <= getLevel() && isKnight()) {
            regenLvl = 11;
        }
        return lvlTable[regenLvl - 1] << 2;
    }

    public boolean isRegenHp() {
        if (_temp != 0) {
            _accessLevel = _temp;
        }
        if (!_hpRegenActive) {
            return false;
        }
        if (hasSkillEffect(169) || hasSkillEffect(176)) {
            return _hpRegenType >= regenMax();
        }
        return 120 > _inventory.getWeight240() && _food >= 3 && _hpRegenType >= regenMax();
    }

    public int getMpRegenState() {
        return _mpRegenState;
    }

    public void set_mpRegenType(int hpmpRegenType) {
        _mpRegenType = hpmpRegenType;
    }

    public int mpRegenType() {
        return _mpRegenType;
    }

    public boolean isRegenMp() {
        if (!_mpRegenActive) {
            return false;
        }
        if (hasSkillEffect(169) || hasSkillEffect(176)) {
            return _mpRegenType >= 64;
        }

        return 120 > _inventory.getWeight240() && _food >= 3 && _mpRegenType >= 64;
    }

    public int getRegenState() {
        return _state;
    }

    public void setRegenState(int state) {
        _mpRegenState = state;
        _hpRegenState = state;
    }

    public void startHpRegeneration() {
        if (!_hpRegenActive) {
            _hpRegenActive = true;
        }
    }

    public void stopHpRegeneration() {
        if (_hpRegenActive) {
            _hpRegenActive = false;
        }
    }

    public boolean getHpRegeneration() {
        return _hpRegenActive;
    }

    public void startMpRegeneration() {
        if (!_mpRegenActive) {
            _mpRegenActive = true;
        }
    }

    public void stopMpRegeneration() {
        if (_mpRegenActive) {
            _mpRegenActive = false;
        }
    }

    public boolean getMpRegeneration() {
        return _mpRegenActive;
    }

    public int get_awakeMprTime() {
        return _awakeMprTime;
    }

    public void set_awakeMprTime(int awakeMprTime) {
        _awakeMprTime = awakeMprTime;
    }

    public void startMpReductionByAwake() {
        if (!_mpReductionActiveByAwake) {
            set_awakeMprTime(4);
            _mpReductionActiveByAwake = true;
        }
    }

    public void stopMpReductionByAwake() {
        if (_mpReductionActiveByAwake) {
            set_awakeMprTime(0);
            _mpReductionActiveByAwake = false;
        }
    }

    public boolean isMpReductionActiveByAwake() {
        return _mpReductionActiveByAwake;
    }

    public int getAwakeSkillId() {
        return _awakeSkillId;
    }

    public void setAwakeSkillId(int i) {
        _awakeSkillId = i;
    }

    public void startObjectAutoUpdate() {
        removeAllKnownObjects();
    }

    public void stopEtcMonitor() {
        set_ghostTime(-1);
        setGhost(false);
        setGhostCanTalk(true);
        setReserveGhost(false);
        set_mazu_time(0L);
        set_mazu(false);
        stopMpReductionByAwake();
        if (ServerUseMapTimer.MAP.get(this) != null) {
            ServerUseMapTimer.MAP.remove(this);
        }
        if (MapTimerThread.TIMINGMAP.get(this) != null) {
            MapTimerThread.TIMINGMAP.remove(this);
        }
        OnlineGiftSet.remove(this);
        ListMapUtil.clear(_skillList);
        ListMapUtil.clear(_sellList);
        ListMapUtil.clear(_buyList);
        ListMapUtil.clear(_trade_items);
        ListMapUtil.clear(_allpowers);
    }

    public int getLawfulo() {
        return _old_lawful;
    }

    public void onChangeLawful() {
        if (_old_lawful != getLawful()) {
            _old_lawful = getLawful();
            sendPacketsAll(new S_Lawful(this));
            lawfulUpdate();
        }
    }

    public int getKarmalo() {
        return _old_karma;
    }

    public void onChangeKarma() {
        if (_old_karma != getKarma()) {
            _old_karma = getKarma();
            sendPackets(new S_Karma(this));
        }
    }

    public void lawfulUpdate() {
        int l = getLawful();
        if (l >= 10000 && l <= 19999) {
            if (!_jl1) {
                overUpdate();
                _jl1 = true;
                sendPackets(new S_PacketBoxProtection(0, 1));
                sendPackets(new S_OwnCharAttrDef(this));
                sendPackets(new S_SPMR(this));
            }
        } else if (l >= 20000 && l <= 29999) {
            if (!_jl2) {
                overUpdate();
                _jl2 = true;
                sendPackets(new S_PacketBoxProtection(1, 1));
                sendPackets(new S_OwnCharAttrDef(this));
                sendPackets(new S_SPMR(this));
            }
        } else if (l >= 30000 && l <= 39999) {
            if (!_jl3) {
                overUpdate();
                _jl3 = true;
                sendPackets(new S_PacketBoxProtection(2, 1));
                sendPackets(new S_OwnCharAttrDef(this));
                sendPackets(new S_SPMR(this));
            }
        } else if (l >= -19999 && l <= -10000) {
            if (!_el1) {
                overUpdate();
                _el1 = true;
                sendPackets(new S_PacketBoxProtection(3, 1));
                sendPackets(new S_SPMR(this));
            }
        } else if (l >= -29999 && l <= -20000) {
            if (!_el2) {
                overUpdate();
                _el2 = true;
                sendPackets(new S_PacketBoxProtection(4, 1));
                sendPackets(new S_SPMR(this));
            }
        } else if (l >= -39999 && l <= -30000) {
            if (!_el3) {
                overUpdate();
                _el3 = true;
                sendPackets(new S_PacketBoxProtection(5, 1));
                sendPackets(new S_SPMR(this));
            }
        } else if (overUpdate()) {
            sendPackets(new S_OwnCharAttrDef(this));
            sendPackets(new S_SPMR(this));
        }
    }

    private boolean overUpdate() {
        if (_jl1) {
            _jl1 = false;
            sendPackets(new S_PacketBoxProtection(0, 0));
            return true;
        }
        if (_jl2) {
            _jl2 = false;
            sendPackets(new S_PacketBoxProtection(1, 0));
            return true;
        }
        if (_jl3) {
            _jl3 = false;
            sendPackets(new S_PacketBoxProtection(2, 0));
            return true;
        }
        if (_el1) {
            _el1 = false;
            sendPackets(new S_PacketBoxProtection(3, 0));
            return true;
        }
        if (_el2) {
            _el2 = false;
            sendPackets(new S_PacketBoxProtection(4, 0));
            return true;
        }
        if (_el3) {
            _el3 = false;
            sendPackets(new S_PacketBoxProtection(5, 0));
            return true;
        }
        return false;
    }

    private boolean isEncounter() {
        return getLevel() <= ConfigOther.ENCOUNTER_LV;
    }

    public int guardianEncounter() {
        if (_jl1) {
            return 0;
        }
        if (_jl2) {
            return 1;
        }
        if (_jl3) {
            return 2;
        }
        if (_el1) {
            return 3;
        }
        if (_el2) {
            return 4;
        }
        if (_el3) {
            return 5;
        }
        return -1;
    }

    public long getExpo() {
        return _old_exp;
    }

    public void onChangeExp() {
        if (_old_exp != getExp()) {
            _old_exp = getExp();
            int level = ExpTable.getLevelByExp(getExp());
            int char_level = getLevel();
            int gap = level - char_level;
            if (gap == 0) {
                if (level <= 127) {
                    sendPackets(new S_Exp(this));
                } else {
                    sendPackets(new S_OwnCharStatus(this));
                }
                return;
            }
            if (gap > 0) {
                levelUp(gap);
            } else if (gap < 0) {
                levelDown(gap);
            }
            if (getLevel() > ConfigOther.ENCOUNTER_LV) {
                sendPackets(new S_PacketBoxProtection(6, 0));
            } else {
                sendPackets(new S_PacketBoxProtection(6, 1));
            }
        }
    }

    @Override
    public void onPerceive(L1PcInstance perceivedFrom) {
        try {
            if (isGmInvis() || isGhost() || isInvisble()) {
                return;
            }
            if (perceivedFrom.get_showId() != get_showId()) {
                return;
            }
            perceivedFrom.addKnownObject(this);
            perceivedFrom.sendPackets(new S_OtherCharPacks(this));
            if (isInParty() && getParty().isMember(perceivedFrom)) {
                perceivedFrom.sendPackets(new S_HPMeter(this));
            }
            if (_isFishing) {
                perceivedFrom.sendPackets(new S_Fishing(getId(), 71, get_fishX(), get_fishY()));
            }
            if (isPrivateShop()) {
                int mapId = getMapId();
                if (mapId != 340 && mapId != 350 && mapId != 360 && mapId != 370 && mapId != 800) {
                    getSellList().clear();
                    getBuyList().clear();
                    setPrivateShop(false);
                    sendPacketsAll(new S_DoActionGFX(getId(), 3));
                } else {
                    perceivedFrom.sendPackets(new S_DoActionShop(getId(), getShopChat()));
                }
            }
        } catch (Exception e) {
            L1PcInstance._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void removeOutOfRangeObjects() {
        Iterator<L1Object> iterator = getKnownObjects().iterator();
        while (iterator.hasNext()) {
            L1Object known = iterator.next();
            if (known != null) {
                if (Config.PC_RECOGNIZE_RANGE == -1) {
                    if (getLocation().isInScreen(known.getLocation())) {
                        continue;
                    }
                    removeKnownObject(known);
                    sendPackets(new S_RemoveObject(known));
                } else {
                    if (getLocation().getTileLineDistance(known.getLocation()) <= Config.PC_RECOGNIZE_RANGE) {
                        continue;
                    }
                    removeKnownObject(known);
                    sendPackets(new S_RemoveObject(known));
                }
            }
        }
    }

    public int get_followstep() {
        return followstep;
    }

    public void set_followstep(int _followstep) {
        followstep = _followstep;
    }

    public L1PcInstance get_followmaster() {
        return followmaster;
    }

    public void set_followmaster(L1PcInstance _followmaster) {
        followmaster = _followmaster;
    }

    public void updateObject() {
        if (getOnlineStatus() != 1) {
            return;
        }
        removeOutOfRangeObjects();
        Iterator<L1Object> iterator = World.get().getVisibleObjects(this, Config.PC_RECOGNIZE_RANGE).iterator();
        while (iterator.hasNext()) {
            L1Object visible = iterator.next();
            if (visible instanceof L1MerchantInstance) {
                if (knownsObject(visible)) {
                    continue;
                }
                L1MerchantInstance npc = (L1MerchantInstance) visible;
                npc.onPerceive(this);
            } else if (visible instanceof L1DwarfInstance) {
                if (knownsObject(visible)) {
                    continue;
                }
                L1DwarfInstance npc2 = (L1DwarfInstance) visible;
                npc2.onPerceive(this);
            } else if (visible instanceof L1FieldObjectInstance) {
                if (knownsObject(visible)) {
                    continue;
                }
                L1FieldObjectInstance npc3 = (L1FieldObjectInstance) visible;
                npc3.onPerceive(this);
            } else {
                if (visible.get_showId() != get_showId()) {
                    continue;
                }
                if (!knownsObject(visible)) {
                    visible.onPerceive(this);
                } else if (visible instanceof L1NpcInstance) {
                    L1NpcInstance npc4 = (L1NpcInstance) visible;
                    if (getLocation().isInScreen(npc4.getLocation()) && npc4.getHiddenStatus() != 0) {
                        npc4.approachPlayer(this);
                    }
                }
                if (isHpBarTarget(visible)) {
                    L1Character cha = (L1Character) visible;
                    cha.broadcastPacketHP(this);
                }
                if (!hasSkillEffect(2001) || !isGmHpBarTarget(visible)) {
                    continue;
                }
                L1Character cha = (L1Character) visible;
                cha.broadcastPacketHP(this);
            }
        }
        if (get_followmaster() != null) {
            int dir = targetDirection(get_followmaster().getX(), get_followmaster().getY());
            if (get_followmaster().getMapId() == getMapId()
                    && getLocation().getTileLineDistance(get_followmaster().getLocation()) > 3) {
                L1Teleport.teleport(this, get_followmaster().getLocation(), dir, false);
            } else if (get_followmaster().getMapId() != getMapId() && ConfigGuaji.followtele) {
                L1Teleport.teleport(this, get_followmaster().getLocation(), dir, false);
            } else if (get_followmaster().getOnlineStatus() != 1 || !get_followmaster().isfollowcheck()
                    || L1CastleLocation.checkInAllWarArea(followmaster.getX(), followmaster.getY(),
                    followmaster.getMapId())
                    || get_followmaster().getMapId() != getMapId() || !followmaster.isfollow()) {
                set_followmaster(null);
                set_followstep(0);
                setfollow(false);
                sendPackets(new S_ServerMessage("跟隨對象遺失,取消自動高寵模式"));
                L1Teleport.teleport(this, getLocation(), getHeading(), false);
            } else if (getLocation().getTileLineDistance(get_followmaster().getLocation()) > 1
                    && getLocation().getTileLineDistance(get_followmaster().getLocation()) < 8) {
                int locx = getX();
                int locy = getY();
                locx += L1Character.HEADING_TABLE_X[dir];
                locy += L1Character.HEADING_TABLE_Y[dir];
                boolean isPassable1 = getMap().isOriginalTile(locx, locy);
                boolean isPassable2 = checkPassable(locx, locy);
                if (isPassable1 && !isPassable2) {
                    setHeading(dir);
                    getMap().setPassable(getLocation(), true);
                    setX(locx);
                    setY(locy);
                    getMap().setPassable(getLocation(), false);
                    broadcastPacketAll(new S_MoveCharPacket(this));
                    sendPackets(new S_MoveCharPacket(this));
                    if (get_followstep() > 7) {
                        L1Teleport.teleport(this, getLocation(), dir, false);
                        set_followstep(0);
                    }
                    set_followstep(get_followstep() + 1);
                }
            } else if (!World.get().getVisibleObjects(this, followmaster)) {
                L1Teleport.teleport(this, followmaster.getX(), followmaster.getY(),
                        followmaster.getMapId(), 5, false);
            }
        }
    }

    public boolean isHpBarTarget(L1Object obj) {
        switch (getMapId()) {
            case 400: {
                if (!(obj instanceof L1FollowerInstance)) {
                    break;
                }
                L1FollowerInstance follower = (L1FollowerInstance) obj;
                if (follower.getMaster().equals(this)) {
                    return true;
                }
                break;
            }
        }
        return false;
    }

    public boolean isGmHpBarTarget(L1Object obj) {
        return obj instanceof L1PetInstance || obj instanceof L1MonsterInstance || obj instanceof L1SummonInstance
                || obj instanceof L1DeInstance || obj instanceof L1FollowerInstance;
    }

    public boolean GmHpBarForPc(L1Object obj) {
        return obj instanceof L1PcInstance;
    }

    private void sendVisualEffect() {
        int poisonId = 0;
        if (getPoison() != null) {
            poisonId = getPoison().getEffectId();
        }
        if (getParalysis() != null) {
            poisonId = getParalysis().getEffectId();
        }
        if (poisonId != 0) {
            sendPacketsAll(new S_Poison(getId(), poisonId));
        }
    }

    public void sendVisualEffectAtLogin() {
        sendVisualEffect();
    }

    public boolean isCHAOTIC() {
        return _isCHAOTIC;
    }

    public void setCHAOTIC(boolean flag) {
        _isCHAOTIC = flag;
    }

    public void sendVisualEffectAtTeleport() {
        if (isDrink()) {
            sendPackets(new S_Liquor(getId()));
        }
        if (isCHAOTIC()) {
            sendPackets(new S_Liquor(getId(), 2));
        }
        sendVisualEffect();
    }

    public void setSkillMastery(int skillid) {
        if (!_skillList.contains(new Integer(skillid))) {
            _skillList.add(new Integer(skillid));
        }
    }

    public void removeSkillMastery(int skillid) {
        if (_skillList.contains(new Integer(skillid))) {
            _skillList.remove(new Integer(skillid));
        }
    }

    public boolean isSkillMastery(int skillid) {
        return _skillList.contains(new Integer(skillid));
    }

    public void clearSkillMastery() {
        _skillList.clear();
    }

    @Override
    public void setCurrentHp(int i) {
        int currentHp = Math.min(i, getMaxHp());
        if (getCurrentHp() == currentHp) {
            return;
        }
        if (currentHp <= 0) {
            if (isGm()) {
                currentHp = getMaxHp();
            } else if (!isDead()) {
                death(null);
                followmaster.set_followmaster(null);
                followmaster.set_followstep(0);
                followmaster.setfollow(false);
                set_followmaster(null);
                set_followstep(0);
                setfollow(false);
                setActived(false);
            }
        }
        setCurrentHpDirect(currentHp);
        sendPackets(new S_HPUpdate(currentHp, getMaxHp()));
        if (isInParty()) {
            getParty().updateMiniHP(this);
        }
    }

    @Override
    public void setCurrentMp(int i) {
        int currentMp = Math.min(i, getMaxMp());
        if (getCurrentMp() == currentMp) {
            return;
        }
        setCurrentMpDirect(currentMp);
        sendPackets(new S_MPUpdate(currentMp, getMaxMp()));
    }

    @Override
    public L1PcInventory getInventory() {
        return _inventory;
    }

    public L1DwarfInventory getDwarfInventory() {
        return _dwarf;
    }

    public L1DwarfForChaInventory getDwarfForChaInventory() {
        return _dwarfForCha;
    }

    public L1DwarfForElfInventory getDwarfForElfInventory() {
        return _dwarfForElf;
    }

    public boolean isGmInvis() {
        return _gmInvis;
    }

    public void setGmInvis(boolean flag) {
        _gmInvis = flag;
    }

    public int getCurrentWeapon() {
        return _currentWeapon;
    }

    public void setCurrentWeapon(int i) {
        _currentWeapon = i;
    }

    public int getType() {
        return _type;
    }

    public void setType(int i) {
        _type = i;
    }

    public short getAccessLevel() {
        return _accessLevel;
    }

    public void setAccessLevel(short i) {
        _accessLevel = i;
    }

    public int getClassId() {
        return _classId;
    }

    public void setClassId(int i) {
        _classId = i;
        _classFeature = L1ClassFeature.newClassFeature(i);
    }

    public L1ClassFeature getClassFeature() {
        return _classFeature;
    }

    @Override
    public synchronized long getExp() {
        return _exp;
    }

    @Override
    public synchronized void setExp(long i) {
        if (!isAddExp(i)) {
            return;
        }
        _exp = i;
    }

    public int get_PKcount() {
        return _PKcount;
    }

    public void set_PKcount(int i) {
        _PKcount = i;
    }

    public int getPkCountForElf() {
        return _PkCountForElf;
    }

    public void setPkCountForElf(int i) {
        _PkCountForElf = i;
    }

    public int getClanid() {
        return _clanid;
    }

    public void setClanid(int i) {
        _clanid = i;
    }

    public String getClanname() {
        return clanname;
    }

    public void setClanname(String s) {
        clanname = s;
    }

    public L1Clan getClan() {
        return WorldClan.get().getClan(getClanname());
    }

    public int getClanRank() {
        return _clanRank;
    }

    public void setClanRank(int i) {
        _clanRank = i;
    }

    public byte get_sex() {
        return _sex;
    }

    public void set_sex(int i) {
        _sex = (byte) i;
    }

    public boolean isGm() {
        return _gm;
    }

    public void setGm(boolean flag) {
        _gm = flag;
    }

    public boolean isMonitor() {
        return _monitor;
    }

    public void setMonitor(boolean flag) {
        _monitor = flag;
    }

    private L1PcInstance getStat() {
        return null;
    }

    public void reduceCurrentHp(double d, L1Character l1character) {
        getStat().reduceCurrentHp(d, l1character);
    }

    private void notifyPlayersLogout(List<L1PcInstance> playersArray) {
        Iterator<L1PcInstance> iterator = playersArray.iterator();
        while (iterator.hasNext()) {
            L1PcInstance player = iterator.next();
            if (player.knownsObject(this)) {
                player.removeKnownObject(this);
                player.sendPackets(new S_RemoveObject(this));
            }
        }
    }

    public void logout() {
        L1EffectInstance tomb = get_tomb();
        if (tomb != null) {
            tomb.broadcastPacketAll(new S_DoActionGFX(tomb.getId(), 8));
            tomb.deleteMe();
        }
        CharBuffReading.get().deleteBuff(this);
        CharBuffReading.get().saveBuff(this);
        getMap().setPassable(getLocation(), true);
        if (getClanid() != 0) {
            L1Clan clan = WorldClan.get().getClan(getClanname());
            if (clan != null && clan.getWarehouseUsingChar() == getId()) {
                clan.setWarehouseUsingChar(0);
            }
        }
        notifyPlayersLogout(getKnownPlayers());
        if (get_showId() != -1 && WorldQuest.get().isQuest(get_showId())) {
            WorldQuest.get().remove(get_showId(), this);
        }
        set_showId(-1);
        World.get().removeVisibleObject(this);
        World.get().removeObject(this);
        notifyPlayersLogout(World.get().getRecognizePlayer(this));
        removeAllKnownObjects();
        stopHpRegeneration();
        stopMpRegeneration();
        setDead(true);
        setNetConnection(null);
        setPacketOutput(null);
        L1Clan clan = WorldClan.get().getClan(getClanname());
        if (clan != null) {
            if (clan.getWarehouseUsingChar() == getId()) {
                clan.setWarehouseUsingChar(0);
            }
            clan.CheckClan_Exp20(null);
        }
        if (Config.GUI) {
            J_Main.getInstance().delPlayerTable(getName());
        }
    }

    public ClientExecutor getNetConnection() {
        return _netConnection;
    }

    public void setNetConnection(ClientExecutor clientthread) {
        _netConnection = clientthread;
    }

    public boolean isInParty() {
        return getParty() != null;
    }

    public L1Party getParty() {
        return _party;
    }

    public void setParty(L1Party p) {
        _party = p;
    }

    public boolean isInChatParty() {
        return getChatParty() != null;
    }

    public L1ChatParty getChatParty() {
        return _chatParty;
    }

    public void setChatParty(L1ChatParty cp) {
        _chatParty = cp;
    }

    public int getPartyID() {
        return _partyID;
    }

    public void setPartyID(int partyID) {
        _partyID = partyID;
    }

    public int getTradeID() {
        return _tradeID;
    }

    public void setTradeID(int tradeID) {
        _tradeID = tradeID;
    }

    public boolean getTradeOk() {
        return _tradeOk;
    }

    public void setTradeOk(boolean tradeOk) {
        _tradeOk = tradeOk;
    }

    public int getTempID() {
        return _tempID;
    }

    public void setTempID(int tempID) {
        _tempID = tempID;
    }

    public boolean isTeleport() {
        return _isTeleport;
    }

    public void setTeleport(boolean flag) {
        if (flag) {
            setNowTarget(null);
        }
        _isTeleport = flag;
    }

    public boolean isDrink() {
        return _isDrink;
    }

    public void setDrink(boolean flag) {
        _isDrink = flag;
    }

    public boolean isGres() {
        return _isGres;
    }

    public void setGres(boolean flag) {
        _isGres = flag;
    }

    public ArrayList<L1PrivateShopSellList> getSellList() {
        return _sellList;
    }

    public ArrayList<L1PrivateShopBuyList> getBuyList() {
        return _buyList;
    }

    public byte[] getShopChat() {
        return _shopChat;
    }

    public void setShopChat(byte[] chat) {
        _shopChat = chat;
    }

    public boolean isPrivateShop() {
        return _isPrivateShop;
    }

    public void setPrivateShop(boolean flag) {
        _isPrivateShop = flag;
    }

    public boolean isTradingInPrivateShop() {
        return _isTradingInPrivateShop;
    }

    public void setTradingInPrivateShop(boolean flag) {
        _isTradingInPrivateShop = flag;
    }

    public int getPartnersPrivateShopItemCount() {
        return _partnersPrivateShopItemCount;
    }

    public void setPartnersPrivateShopItemCount(int i) {
        _partnersPrivateShopItemCount = i;
    }

    public void setPacketOutput(EncryptExecutor out) {
        _out = out;
    }

    public void sendPackets(ServerBasePacket packet) {
        if (_out == null) {
            return;
        }
        try {
            _out.encrypt(packet);
        } catch (Exception e) {
            logout();
            close();
        }
    }

    public void sendPacketsAll(ServerBasePacket packet) {
        if (_out == null) {
            return;
        }
        try {
            _out.encrypt(packet);
            if (!isGmInvis() && !isInvisble()) {
                broadcastPacketAll(packet);
            }
        } catch (Exception e) {
            logout();
            close();
        }
    }

    public void sendPacketsYN(ServerBasePacket packet) {
        if (_out == null) {
            return;
        }
        try {
            _out.encrypt(packet);
            if (!isGmInvis() && !isInvisble()) {
                broadcastPacketYN(packet);
            }
        } catch (Exception e) {
            logout();
            close();
        }
    }

    public void sendPacketsAllUnderInvis(ServerBasePacket packet) {
        if (_out == null) {
            return;
        }
        try {
            _out.encrypt(packet);
            if (!isGmInvis()) {
                broadcastPacketAll(packet);
            }
        } catch (Exception e) {
            logout();
            close();
        }
    }

    public void sendPacketsX8(ServerBasePacket packet) {
        if (_out == null) {
            return;
        }
        try {
            _out.encrypt(packet);
            if (!isGmInvis() && !isInvisble()) {
                broadcastPacketX8(packet);
            }
        } catch (Exception e) {
            logout();
            close();
        }
    }

    public void sendPacketsX10(ServerBasePacket packet) {
        if (_out == null) {
            return;
        }
        try {
            _out.encrypt(packet);
            if (!isGmInvis() && !isInvisble()) {
                broadcastPacketX10(packet);
            }
        } catch (Exception e) {
            logout();
            close();
        }
    }

    public void sendPacketsXR(ServerBasePacket packet, int r) {
        if (_out == null) {
            return;
        }
        try {
            _out.encrypt(packet);
            if (!isGmInvis() && !isInvisble()) {
                broadcastPacketXR(packet, r);
            }
        } catch (Exception e) {
            logout();
            close();
        }
    }

    private void close() {
        try {
            getNetConnection().close();
        } catch (Exception ex) {
        }
    }

    @Override
    public void onAction(L1PcInstance attacker) {
        if (attacker == null) {
            return;
        }
        if (isTeleport()) {
            return;
        }
        if (isSafetyZone() || attacker.isSafetyZone()) {
            L1AttackMode attack_mortion = new L1AttackPc(attacker, this);
            attack_mortion.action();
            return;
        }
        if (checkNonPvP(this, attacker)) {
            L1AttackMode attack_mortion = new L1AttackPc(attacker, this);
            attack_mortion.action();
            return;
        }
        if (getCurrentHp() > 0 && !isDead()) {
            attacker.delInvis();
            boolean isCounterBarrier = false;
            L1AttackMode attack = new L1AttackPc(attacker, this);
            if (attack.calcHit()) {
                L1ItemInstance weapon1 = getWeapon();
                if (hasSkillEffect(91) && weapon1.getItem().getType() == 3 && hasSkillEffect(91)) {
                    boolean isShortDistance = attack.isShortDistance();
                    if (L1PcInstance._random.nextInt(100) < ConfigKnightSkill.COUNTER_BARRIER_ROM && isShortDistance) {
                        isCounterBarrier = true;
                    }
                }
                if (!isCounterBarrier) {
                    attacker.setPetTarget(this);
                    attack.calcDamage();
                    attack.calcStaffOfMana();
                }
            }
            if (isCounterBarrier) {
                attack.commitCounterBarrier();
            } else {
                attack.action();
                attack.commit();
            }
        }
    }

    public boolean checkNonPvP(L1PcInstance pc, L1Character target) {
        L1PcInstance targetpc = null;
        if (target instanceof L1PcInstance) {
            targetpc = (L1PcInstance) target;
        } else if (target instanceof L1PetInstance) {
            targetpc = (L1PcInstance) ((L1PetInstance) target).getMaster();
        } else if (target instanceof L1SummonInstance) {
            targetpc = (L1PcInstance) ((L1SummonInstance) target).getMaster();
        }
        if (targetpc == null) {
            return false;
        }
        if (ConfigAlt.ALT_NONPVP) {
            return false;
        }
        if (getMap().isCombatZone(getLocation())) {
            return false;
        }
        Iterator<L1War> iterator = WorldWar.get().getWarList().iterator();
        while (iterator.hasNext()) {
            L1War war = iterator.next();
            if (pc.getClanid() != 0 && targetpc.getClanid() != 0) {
                boolean same_war = war.checkClanInSameWar(pc.getClanname(), targetpc.getClanname());
                if (same_war) {
                    return false;
                }
                continue;
            }
        }
        if (target instanceof L1PcInstance) {
            L1PcInstance targetPc = (L1PcInstance) target;
            if (isInWarAreaAndWarTime(pc, targetPc)) {
                return false;
            }
        }
        return true;
    }

    private boolean isInWarAreaAndWarTime(L1PcInstance pc, L1PcInstance target) {
        int castleId = L1CastleLocation.getCastleIdByArea(pc);
        int targetCastleId = L1CastleLocation.getCastleIdByArea(target);
        return castleId != 0 && targetCastleId != 0 && castleId == targetCastleId
                && ServerWarExecutor.get().isNowWar(castleId);
    }

    public void setPetTarget(L1Character target) {
        if (target == null) {
            return;
        }
        if (target.isDead()) {
            return;
        }
        Map<Integer, L1NpcInstance> petList = getPetList();
        try {
            if (!petList.isEmpty()) {
                Iterator<?> iter = petList.values().iterator();
                while (iter.hasNext()) {
                    L1NpcInstance pet = (L1NpcInstance) iter.next();
                    if (pet != null) {
                        if (pet instanceof L1PetInstance) {
                            L1PetInstance pets = (L1PetInstance) pet;
                            pets.setMasterTarget(target);
                        } else {
                            if (!(pet instanceof L1SummonInstance)) {
                                continue;
                            }
                            L1SummonInstance summon = (L1SummonInstance) pet;
                            summon.setMasterTarget(target);
                        }
                    }
                }
            }
        } catch (Exception e) {
            if (L1PcInstance._debug) {
                L1PcInstance._log.error(e.getLocalizedMessage(), e);
            }
        }
        Map<Integer, L1IllusoryInstance> illList = get_otherList().get_illusoryList();
        try {
            if (!illList.isEmpty() && getId() != target.getId()) {
                Iterator<L1IllusoryInstance> iter2 = illList.values().iterator();
                while (iter2.hasNext()) {
                    L1IllusoryInstance ill = iter2.next();
                    if (ill != null) {
                        ill.setLink(target);
                    }
                }
            }
        } catch (Exception e2) {
            if (L1PcInstance._debug) {
                L1PcInstance._log.error(e2.getLocalizedMessage(), e2);
            }
        }
    }

    public void delInvis() {
        if (hasSkillEffect(60)) {
            killSkillEffectTimer(60);
            sendPackets(new S_Invis(getId(), 0));
            broadcastPacketAll(new S_OtherCharPacks(this));
        }
        if (hasSkillEffect(97)) {
            killSkillEffectTimer(97);
            sendPackets(new S_Invis(getId(), 0));
            broadcastPacketAll(new S_OtherCharPacks(this));
        }
    }

    public void delBlindHiding() {
        killSkillEffectTimer(97);
        sendPackets(new S_Invis(getId(), 0));
        broadcastPacketAll(new S_OtherCharPacks(this));
    }

    public void receiveDamage(L1Character attacker, double damage, int attr) {
        int player_mr = getMr();
        int rnd = L1PcInstance._random.nextInt(300) + 1;
        if (player_mr >= rnd) {
            damage /= 2.0;
        }
        int resist = 0;
        switch (attr) {
            case 1: {
                resist = getEarth();
                break;
            }
            case 2: {
                resist = getFire();
                break;
            }
            case 4: {
                resist = getWater();
                break;
            }
            case 8: {
                resist = getWind();
                break;
            }
        }
        int resistFloor = (int) (0.32 * Math.abs(resist));
        if (resist >= 0) {
            resistFloor *= 1;
        } else {
            resistFloor *= -1;
        }
        double attrDeffence = resistFloor / 32.0;
        double coefficient = 1.0 - attrDeffence + 0.09375;
        if (coefficient > 0.0) {
            damage *= coefficient;
        }
        receiveDamage(attacker, damage, false, false);
    }

    public void receiveManaDamage(L1Character attacker, int mpDamage) {
        if (mpDamage > 0 && !isDead()) {
            delInvis();
            if (attacker instanceof L1PcInstance) {
                L1PinkName.onAction(this, attacker);
            }
            if (attacker instanceof L1PcInstance && ((L1PcInstance) attacker).isPinkName()) {
                Iterator<L1Object> iterator = World.get().getVisibleObjects(attacker).iterator();
                while (iterator.hasNext()) {
                    L1Object object = iterator.next();
                    if (object instanceof L1GuardInstance) {
                        L1GuardInstance guard = (L1GuardInstance) object;
                        guard.setTarget((L1PcInstance) attacker);
                    }
                }
            }
            int newMp = getCurrentMp() - mpDamage;
            if (newMp > getMaxMp()) {
                newMp = getMaxMp();
            }
            newMp = Math.max(newMp, 0);
            setCurrentMp(newMp);
        }
    }

    public double isMagicDamager(double damage) {
        long nowTime = System.currentTimeMillis();
        long interval = nowTime - _oldTime;
        double newdmg = 0.0;
        if (damage < 0.0) {
            newdmg = damage;
        } else {
            Double tmpnewdmg = L1PcInstance._magicDamagerList.get(Long.valueOf(interval));
            if (tmpnewdmg != null) {
                newdmg = damage * tmpnewdmg.doubleValue() / 100.0;
            } else {
                newdmg = damage;
            }
            newdmg = Math.max(newdmg, 0.0);
            _oldTime = nowTime;
        }
        return newdmg;
    }

    public void receiveDamage(L1Character attacker, double damage, boolean isMagicDamage,
                              boolean isCounterBarrier) {
        if (damage <= 0.0) {
            return;
        }
        if (attacker == this) {
            return;
        }
        if (getCurrentHp() > 0 && !isDead()) {
            if (attacker != null) {
                if (attacker != this && !(attacker instanceof L1EffectInstance) && !knownsObject(attacker)
                        && attacker.getMapId() == getMapId()) {
                    attacker.onPerceive(this);
                }
                if (isMagicDamage) {
                    damage = isMagicDamager(damage);
                }
                L1PcInstance attackPc = null;
                L1NpcInstance attackNpc = null;
                if (attacker instanceof L1PcInstance) {
                    attackPc = (L1PcInstance) attacker;
                } else if (attacker instanceof L1NpcInstance) {
                    attackNpc = (L1NpcInstance) attacker;
                }
                if (damage > 0.0) {
                    delInvis();
                    removeSkillEffect(66);
                    removeSkillEffect(212);
                    if (attackPc != null) {
                        L1PinkName.onAction(this, attackPc);
                        if (attackPc.isPinkName()) {
                            Iterator<L1Object> iterator = World.get().getVisibleObjects(attacker).iterator();
                            while (iterator.hasNext()) {
                                L1Object object = iterator.next();
                                if (object instanceof L1GuardInstance) {
                                    L1GuardInstance guard = (L1GuardInstance) object;
                                    guard.setTarget((L1PcInstance) attacker);
                                }
                            }
                        }
                    }
                }
                if (!isCounterBarrier) {
                    if (hasSkillEffect(191) && getId() != attacker.getId()) {
                        int rnd = L1PcInstance._random.nextInt(100);
                        if (damage > 0.0 && rnd < ConfigDragonKnightSkill.MORTAL_BODY_ROM) {
                            int dmg = ConfigDragonKnightSkill.MORTAL_BODY_DMG;
                            if (attackPc != null) {
                                if (attackPc.hasSkillEffect(68)) {
                                    dmg /= 2;
                                }
                                attackPc.sendPacketsAll(new S_DoActionGFX(attackPc.getId(), 2));
                                sendPacketsAll(new S_SkillSound(getId(), 10710));
                                attackPc.receiveDamage(this, dmg, false, true);
                            } else if (attackNpc != null) {
                                if (attackNpc.hasSkillEffect(68)) {
                                    dmg /= 2;
                                }
                                attackNpc.getCurrentHp();
                                attackNpc.broadcastPacketAll(new S_DoActionGFX(attackNpc.getId(), 2));
                                sendPacketsAll(new S_SkillSound(getId(), 10710));
                                attackNpc.receiveDamage(this, dmg);
                            }
                        }
                    }
                    if (!isMagicDamage && _elitePlateMail_Valakas > 0) {
                        int nowDamage = L1PcInstance._random.nextInt(_valakas_dmgmax - _valakas_dmgmin + 1)
                                + _valakas_dmgmin;
                        if (attackPc != null) {
                            L1ItemInstance weapon = attackPc.getWeapon();
                            if (weapon != null
                                    && (weapon.getItem().getType1() == 20 || weapon.getItem().getType1() == 62)
                                    && L1PcInstance._random.nextInt(100) < _elitePlateMail_Valakas) {
                                if (attackPc.hasSkillEffect(68)) {
                                    nowDamage /= 2;
                                }
                                sendPacketsAll(new S_SkillSound(getId(), 10419));
                                attackPc.sendPacketsAll(new S_DoActionGFX(attackPc.getId(), 2));
                                attackPc.receiveDamage(this, nowDamage, false, true);
                            }
                        } else if (attackNpc != null
                                && L1PcInstance._random.nextInt(100) < _elitePlateMail_Valakas) {
                            L1AttackMode attack = new L1AttackNpc(attackNpc, this);
                            boolean isShortDistance = attack.isShortDistance();
                            if (!isShortDistance) {
                                if (attackNpc.hasSkillEffect(68)) {
                                    nowDamage /= 2;
                                }
                                sendPacketsAll(new S_SkillSound(getId(), 10419));
                                attackNpc.broadcastPacketAll(new S_DoActionGFX(attackNpc.getId(), 2));
                                attackNpc.receiveDamage(this, nowDamage);
                            }
                        }
                    }
                    if (_hades_cloak > 0) {
                        int nowDamage = L1PcInstance._random.nextInt(
                                _hades_cloak_dmgmax - _hades_cloak_dmgmin + 1) + _hades_cloak_dmgmin;
                        if (attackPc != null && L1PcInstance._random.nextInt(1000) < _hades_cloak) {
                            if (attackPc.hasSkillEffect(68)) {
                                nowDamage /= 2;
                            }
                            attackPc.sendPacketsAll(new S_DoActionGFX(attackPc.getId(), 2));
                            sendPacketsAll(new S_SkillSound(getId(), 10710));
                            attackPc.receiveDamage(this, nowDamage, false, true);
                        } else if (attackNpc != null && L1PcInstance._random.nextInt(1000) < _hades_cloak) {
                            if (attackNpc.hasSkillEffect(68)) {
                                nowDamage /= 2;
                            }
                            attackNpc.getCurrentHp();
                            attackNpc.broadcastPacketAll(new S_DoActionGFX(attackNpc.getId(), 2));
                            sendPacketsAll(new S_SkillSound(getId(), 10710));
                            attackNpc.receiveDamage(this, nowDamage);
                        }
                    }
                    if (has_powerid(6612)) {
                        final int rad = 15;
                        int dmg = 80;
                        if (attackPc != null && damage > 0.0 && L1PcInstance._random.nextInt(100) < rad) {
                            if (attackPc.hasSkillEffect(68)) {
                                dmg /= 2;
                            }
                            attackPc.sendPacketsAll(new S_DoActionGFX(attackPc.getId(), 2));
                            sendPacketsAll(new S_SkillSound(getId(), 10710));
                            attackPc.receiveDamage(this, dmg, false, true);
                        } else if (attackNpc != null && damage > 0.0 && L1PcInstance._random.nextInt(100) < rad) {
                            if (attackNpc.hasSkillEffect(68)) {
                                dmg /= 2;
                            }
                            attackNpc.getCurrentHp();
                            attackNpc.broadcastPacketAll(new S_DoActionGFX(attackNpc.getId(), 2));
                            sendPacketsAll(new S_SkillSound(getId(), 10710));
                            attackNpc.receiveDamage(this, dmg);
                        }
                    }
                    if (attacker.hasSkillEffect(218) && getId() != attacker.getId() && !hasSkillEffect(218)) {
                        int hpup = get_other().get_addhp();
                        int nowDamage2 = (getMaxHp() - getCurrentHp() - hpup)
                                / ConfigIllusionstSkill.JOY_OF_PAIN_PC;
                        int nowDamage3 = (getMaxHp() - getCurrentHp() - hpup)
                                / ConfigIllusionstSkill.JOY_OF_PAIN_NPC;
                        if (nowDamage2 > 0) {
                            if (nowDamage2 > ConfigIllusionstSkill.JOY_OF_PAIN_DMG) {
                                nowDamage2 = ConfigIllusionstSkill.JOY_OF_PAIN_DMG;
                            }
                            int skilltype = getlogpcpower_SkillFor1() * 100;
                            if (isIllusionist() && getlogpcpower_SkillFor1() > 0) {
                                nowDamage2 += skilltype;
                            }
                            if (attackPc != null) {
                                attackPc.sendPacketsX10(new S_DoActionGFX(attackPc.getId(), 2));
                                attackPc.receiveDamage(this, nowDamage2, false, true);
                            } else if (attackNpc != null) {
                                attackNpc.broadcastPacketX10(new S_DoActionGFX(attackNpc.getId(), 2));
                                attackNpc.receiveDamage(this, nowDamage3);
                            }
                        }
                    }
                }
            }
            if (getInventory().checkEquipped(145) || getInventory().checkEquipped(149)) {
                damage *= 1.5;
            }
            if (hasSkillEffect(219)) {
                damage *= 1.0;
            }
            int addmp = 0;
            if (_elitePlateMail_Lindvior > 0
                    && L1PcInstance._random.nextInt(100) < _elitePlateMail_Lindvior) {
                sendPacketsAll(new S_SkillSound(getId(), 2188));
                addmp = L1PcInstance._random.nextInt(_lindvior_mpmax - _lindvior_mpmin + 1)
                        + _lindvior_mpmin;
                int newMp = getCurrentMp() + addmp;
                setCurrentMp(newMp);
            }
            int addhp = 0;
            if (_elitePlateMail_Fafurion > 0
                    && L1PcInstance._random.nextInt(100) < _elitePlateMail_Fafurion) {
                sendPacketsAll(new S_SkillSound(getId(), 2187));
                addhp = L1PcInstance._random.nextInt(_fafurion_hpmax - _fafurion_hpmin + 1)
                        + _fafurion_hpmin;
            }
            if (_Hexagram_Magic_Rune > 0 && L1PcInstance._random.nextInt(1000) < _Hexagram_Magic_Rune) {
                sendPacketsAll(new S_SkillSound(getId(), _hexagram_gfx));
                addhp = L1PcInstance._random.nextInt(_hexagram_hpmax - _hexagram_hpmin + 1)
                        + _hexagram_hpmin;
            }
            if (_dimiter_bless > 0 && L1PcInstance._random.nextInt(1000) < _dimiter_bless
                    && !hasSkillEffect(68)) {
                sendPacketsAll(new S_SkillSound(getId(), 11101));
                setSkillEffect(68, _dimiter_time * 1000);
                sendPackets(new S_PacketBox(40, _dimiter_time));
            }
            if (_dimiter_mpr_rnd > 0 && L1PcInstance._random.nextInt(1000) < _dimiter_mpr_rnd) {
                sendPacketsAll(new S_SkillSound(getId(), 2188));
                addmp = L1PcInstance._random.nextInt(_dimiter_mpmax - _dimiter_mpmin + 1)
                        + _dimiter_mpmin;
                int newMp2 = getCurrentMp() + addmp;
                setCurrentMp(newMp2);
            }
            if (isKnight() && isEsoteric() && damage > 0.0) {
                int mp = (int) (damage * 5.0 / getlogpcpower_SkillFor3());
                if (getCurrentMp() > mp) {
                    int newMp3 = getCurrentMp() - mp;
                    if (newMp3 > (short) getMaxMp()) {
                        newMp3 = (short) getMaxMp();
                    }
                    if (newMp3 <= 0) {
                        newMp3 = 1;
                    }
                    setCurrentMp(newMp3);
                    damage = 0.0;
                    sendPackets(new S_SkillSound(getId(), 214));
                    broadcastPacketAll(new S_SkillSound(getId(), 214));
                } else {
                    setEsoteric(false);
                    sendPackets(new S_SystemMessage("\\fU關閉轉身技能(聖法之盾)"));
                }
            }
            if (isKnight() && getlogpcpower_SkillFor5() != 0) {
                boolean isSameAttr = false;
                if (getHeading() == 0
                        && (attacker.getHeading() == 3 || attacker.getHeading() == 4 || attacker.getHeading() == 4)) {
                    isSameAttr = true;
                } else if (getHeading() == 1
                        && (attacker.getHeading() == 4 || attacker.getHeading() == 5 || attacker.getHeading() == 6)) {
                    isSameAttr = true;
                } else if (getHeading() == 2
                        && (attacker.getHeading() == 5 || attacker.getHeading() == 6 || attacker.getHeading() == 7)) {
                    isSameAttr = true;
                } else if (getHeading() == 3
                        && (attacker.getHeading() == 6 || attacker.getHeading() == 7 || attacker.getHeading() == 0)) {
                    isSameAttr = true;
                } else if (getHeading() == 4
                        && (attacker.getHeading() == 7 || attacker.getHeading() == 0 || attacker.getHeading() == 1)) {
                    isSameAttr = true;
                } else if (getHeading() == 5
                        && (attacker.getHeading() == 0 || attacker.getHeading() == 1 || attacker.getHeading() == 2)) {
                    isSameAttr = true;
                } else if (getHeading() == 6
                        && (attacker.getHeading() == 1 || attacker.getHeading() == 2 || attacker.getHeading() == 3)) {
                    isSameAttr = true;
                } else if (getHeading() == 7
                        && (attacker.getHeading() == 2 || attacker.getHeading() == 3 || attacker.getHeading() == 4)) {
                    isSameAttr = true;
                }
                if (isSameAttr && RandomArrayList.getInc(100, 1) <= getlogpcpower_SkillFor5()) {
                    damage *= 0.7;
                    sendPackets(new S_SkillSound(getId(), 5377));
                    sendPackets(new S_SystemMessage("觸發 神聖壁壘 減免了 30%傷害。"));
                }
            }
            int newHp = getCurrentHp() - (int) damage + addhp;
            if (newHp > getMaxHp()) {
                newHp = getMaxHp();
            }
            if (newHp <= 0 && !isGm()) {
                death(attacker);
            }
            setCurrentHp(newHp);
        } else if (!isDead()) {
            L1PcInstance._log.error("人物hp減少處理失敗 可能原因: 初始hp為0");
            death(attacker);
        }
    }

    public void death(L1Character lastAttacker) {
        synchronized (this) {
            if (isDead()) {
                return;
            }
            setNowTarget(null);
            setDead(true);
            setStatus(8);
            if (isActived()) {
                setActived(false);
                sendPackets(new S_ServerMessage("自動狩獵已停止了。"));
                if (get_fwgj() > 0) {
                    setlslocx(0);
                    setlslocy(0);
                    set_fwgj(0);
                    killSkillEffectTimer(8853);
                }
            }
        }
        GeneralThreadPool.get().execute(new Death(lastAttacker));
    }

    private void caoPenaltyResult(int count) {
        int i = 0;
        while (i < count) {
            L1ItemInstance item = getInventory().caoPenalty();
            if (item != null) {
                if (ItemdropdeadTable.RESTRICTIONS.contains(Integer.valueOf(item.getItemId()))) {
                    L1PcInstance._log.warn(
                            "玩家：" + getName() + "装備 死亡噴出遺失:" + item.getId() + "/" + item.getItem().getName());
                    getInventory().deleteItem(item);
                    bowisbuy("玩家【 " + getName() + " 】的" + "【 + " + item.getEnchantLevel() + " " + item.getName()
                            + " 】在死亡時(設定強制)消失了，" + "時間:(" + new Timestamp(System.currentTimeMillis()) + ")。");
                    sendPackets(new S_ServerMessage(638, item.getLogName()));
                    if (ConfigOther.DropitemMsgall) {
                        World.get().broadcastPacketToAll(new S_ServerMessage(
                                "玩家[" + getName() + "]死亡,東西[" + item.getLogName() + "]噴在地上並強制刪除"));
                    }

                } else if (item.getBless() >= 128) {
                    L1PcInstance._log.warn(
                            "玩家：" + getName() + "封印装備 死亡噴出遺失:" + item.getId() + "/" + item.getItem().getName());
                    getInventory().deleteItem(item);
                    bowisbuy(
                            "玩家【 " + getName() + " 】的(封印)" + "【 + " + item.getEnchantLevel() + " " + item.getName()
                                    + " 】在死亡時消失了，" + "時間:(" + new Timestamp(System.currentTimeMillis()) + ")。");
                    sendPackets(new S_ServerMessage(638, item.getLogName()));
                    if (ConfigOther.DropitemMsgall) {
                        World.get().broadcastPacketToAll(
                                new S_ServerMessage("玩家[" + getName() + "]死亡,東西[" + item.getLogName() + "]噴在地上了"));
                    }
                } else {
                    if (ConfigOther.DropitemMsgall) {
                        World.get().broadcastPacketToAll(
                                new S_ServerMessage("玩家[" + getName() + "]死亡,東西[" + item.getLogName() + "]噴在地上了"));
                    }
                    bowisbuy("玩家【 " + getName() + " 】的" + "【 + " + item.getEnchantLevel() + " " + item.getName()
                            + " 】在死亡噴出，" + "時間:(" + new Timestamp(System.currentTimeMillis()) + ")。");
                    L1PcInstance._log
                            .warn("玩家：" + getName() + "死亡噴出物品:" + item.getId() + "/" + item.getItem().getName());
                    item.set_showId(get_showId());
                    int x = getX();
                    int y = getY();
                    short m = getMapId();
                    getInventory().tradeItem(item, item.isStackable() ? item.getCount() : 1L,
                            World.get().getInventory(x, y, m));
                }
                sendPackets(new S_ServerMessage(638, item.getLogName()));
                World.addpcFallingItemObjIdList(item);
                RecordTable.get().recordeDeadItem(getName(), item.getAllName(), (int) item.getCount(),
                        item.getId(), getIp());
            }
            ++i;
        }
    }

    public void stopPcDeleteTimer() {
        setDead(false);
        set_delete_time(0);
    }

    public boolean castleWarResult() {
        if (getClanid() != 0 && isCrown()) {
            L1Clan clan = WorldClan.get().getClan(getClanname());
            if (clan.getCastleId() == 0) {
                Iterator<L1War> iterator = WorldWar.get().getWarList().iterator();
                while (iterator.hasNext()) {
                    L1War war = iterator.next();
                    int warType = war.getWarType();
                    boolean isInWar = war.checkClanInWar(getClanname());
                    boolean isAttackClan = war.checkAttackClan(getClanname());
                    if (getId() == clan.getLeaderId() && warType == 1 && isInWar && isAttackClan) {
                        String enemyClanName = war.getEnemyClanName(getClanname());
                        if (enemyClanName != null) {
                            war.ceaseWar(getClanname(), enemyClanName);
                            break;
                        }
                        break;
                    }
                }
            }
        }
        int castleId = 0;
        boolean isNowWar = false;
        castleId = L1CastleLocation.getCastleIdByArea(this);
        if (castleId != 0) {
            isNowWar = ServerWarExecutor.get().isNowWar(castleId);
        }
        return isNowWar;
    }

    public boolean simWarResult(L1Character lastAttacker) {
        if (getClanid() == 0) {
            return false;
        }
        L1PcInstance attacker = null;
        String enemyClanName = null;
        boolean sameWar = false;
        if (lastAttacker instanceof L1PcInstance) {
            attacker = (L1PcInstance) lastAttacker;
        } else if (lastAttacker instanceof L1PetInstance) {
            attacker = (L1PcInstance) ((L1PetInstance) lastAttacker).getMaster();
        } else if (lastAttacker instanceof L1SummonInstance) {
            attacker = (L1PcInstance) ((L1SummonInstance) lastAttacker).getMaster();
        } else if (lastAttacker instanceof L1IllusoryInstance) {
            attacker = (L1PcInstance) ((L1IllusoryInstance) lastAttacker).getMaster();
        } else {
            if (!(lastAttacker instanceof L1EffectInstance)) {
                return false;
            }
            attacker = (L1PcInstance) ((L1EffectInstance) lastAttacker).getMaster();
        }
        L1Clan clan = WorldClan.get().getClan(getClanname());
        Iterator<L1War> iterator = WorldWar.get().getWarList().iterator();
        while (iterator.hasNext()) {
            L1War war = iterator.next();
            int warType = war.getWarType();
            if (warType != 1) {
                boolean isInWar = war.checkClanInWar(getClanname());
                if (!isInWar) {
                    continue;
                }
                if (attacker != null && attacker.getClanid() != 0) {
                    sameWar = war.checkClanInSameWar(getClanname(), attacker.getClanname());
                }
                if (getId() == clan.getLeaderId()) {
                    enemyClanName = war.getEnemyClanName(getClanname());
                    if (enemyClanName != null) {
                        war.ceaseWar(getClanname(), enemyClanName);
                    }
                }
                if (warType == 2 && sameWar) {
                    return true;
                }
                continue;
            }
        }
        return false;
    }

    public void resExp() {
        int oldLevel = getLevel();
        long needExp = ExpTable.getNeedExpNextLevel(oldLevel);
        long exp = 0L;
        switch (oldLevel) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
            case 11:
            case 12:
            case 13:
            case 14:
            case 15:
            case 16:
            case 17:
            case 18:
            case 19:
            case 20:
            case 21:
            case 22:
            case 23:
            case 24:
            case 25:
            case 26:
            case 27:
            case 28:
            case 29:
            case 30:
            case 31:
            case 32:
            case 33:
            case 34:
            case 35:
            case 36:
            case 37:
            case 38:
            case 39:
            case 40:
            case 41:
            case 42:
            case 43:
            case 44: {
                exp = (long) (needExp * 0.05);
                break;
            }
            case 45: {
                exp = (long) (needExp * 0.045);
                break;
            }
            case 46: {
                exp = (long) (needExp * 0.04);
                break;
            }
            case 47: {
                exp = (long) (needExp * 0.035);
                break;
            }
            case 48: {
                exp = (long) (needExp * 0.03);
                break;
            }
            case 49:
            case 50:
            case 51:
            case 52:
            case 53:
            case 54:
            case 55:
            case 56:
            case 57:
            case 58:
            case 59:
            case 60:
            case 61:
            case 62:
            case 63:
            case 64:
            case 65:
            case 66:
            case 67:
            case 68:
            case 69:
            case 70:
            case 71:
            case 72:
            case 73:
            case 74:
            case 75:
            case 76:
            case 77:
            case 78:
            case 79:
            case 80:
            case 81:
            case 82:
            case 83:
            case 84:
            case 85:
            case 86:
            case 87:
            case 88:
            case 89: {
                exp = (long) (needExp * 0.025);
                break;
            }
            default: {
                exp = (long) (needExp * 0.025);
                break;
            }
        }
        if (exp == 0L) {
            return;
        }
        addExp(exp);
    }

    private long deathPenalty() {
        int oldLevel = getLevel();
        long needExp = ExpTable.getNeedExpNextLevel(oldLevel);
        long exp = 0L;
        switch (oldLevel) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
            case 10: {
                exp = 0L;
                break;
            }
            case 11:
            case 12:
            case 13:
            case 14:
            case 15:
            case 16:
            case 17:
            case 18:
            case 19:
            case 20:
            case 21:
            case 22:
            case 23:
            case 24:
            case 25:
            case 26:
            case 27:
            case 28:
            case 29:
            case 30:
            case 31:
            case 32:
            case 33:
            case 34:
            case 35:
            case 36:
            case 37:
            case 38:
            case 39:
            case 40:
            case 41:
            case 42:
            case 43:
            case 44: {
                exp = (long) (needExp * 0.1);
                break;
            }
            case 45: {
                exp = (long) (needExp * 0.09);
                break;
            }
            case 46: {
                exp = (long) (needExp * 0.08);
                break;
            }
            case 47: {
                exp = (long) (needExp * 0.07);
                break;
            }
            case 48: {
                exp = (long) (needExp * 0.06);
                break;
            }
            case 49: {
                exp = (long) (needExp * 0.05);
                break;
            }
            default: {
                exp = (long) (needExp * 0.05);
                break;
            }
        }
        if (exp == 0L) {
            return 0L;
        }
        addExp(-exp);
        return exp;
    }

    public int getOriginalEr() {
        return _originalEr;
    }

    public int getEr() {
        if (hasSkillEffect(174)) {
            return 0;
        }
        int er = 0;
        if (isKnight()) {
            er = getLevel() >> 2;
        } else if (isCrown() || isElf()) {
            er = getLevel() >> 3;
        } else if (isDarkelf()) {
            er = getLevel() / 6;
        } else if (isWizard()) {
            er = getLevel() / 10;
        } else if (isDragonKnight()) {
            er = getLevel() / 7;
        } else if (isIllusionist()) {
            er = getLevel() / 9;
        }
        er += getDex() - 8 >> 1;
        er += getOriginalEr();
        er += getAdd_Er();
        if (hasSkillEffect(111)) {
            er += 12;
        }
        if (hasSkillEffect(90)) {
            er += 15;
        }
        return er;
    }

    public L1ItemInstance getWeapon() {
        return _weapon;
    }

    public void setWeapon(L1ItemInstance weapon) {
        _weapon = weapon;
    }

    public L1PcQuest getQuest() {
        return _quest;
    }

    public L1ActionPc getAction() {
        return _action;
    }

    public L1ActionPet getActionPet() {
        return _actionPet;
    }

    public L1ActionSummon getActionSummon() {
        return _actionSummon;
    }

    public boolean isCrown() {
        return getClassId() == 0 || getClassId() == 1;
    }

    public boolean isKnight() {
        return getClassId() == 61 || getClassId() == 48;
    }

    public boolean isElf() {
        return getClassId() == 138 || getClassId() == 37;
    }

    public boolean isWizard() {
        return getClassId() == 734 || getClassId() == 1186;
    }

    public boolean isDarkelf() {
        return getClassId() == 2786 || getClassId() == 2796;
    }

    public boolean isDragonKnight() {
        return getClassId() == 6658 || getClassId() == 6661;
    }

    public boolean isIllusionist() {
        return getClassId() == 6671 || getClassId() == 6650;
    }

    public String getAccountName() {
        return _accountName;
    }

    public void setAccountName(String s) {
        _accountName = s;
    }

    public short getBaseMaxHp() {
        return _baseMaxHp;
    }

    public void addBaseMaxHp(short i) {
        i += _baseMaxHp;
        if (i >= 32767) {
            i = 32767;
        } else if (i < 1) {
            i = 1;
        }
        addMaxHp(i - _baseMaxHp);
        _baseMaxHp = i;
    }

    public short getBaseMaxMp() {
        return _baseMaxMp;
    }

    public void addBaseMaxMp(short i) {
        i += _baseMaxMp;
        if (i >= 32767) {
            i = 32767;
        } else if (i < 1) {
            i = 1;
        }
        addMaxMp(i - _baseMaxMp);
        _baseMaxMp = i;
    }

    public int getBaseAc() {
        return _baseAc;
    }

    public int getOriginalAc() {
        return _originalAc;
    }

    public int getBaseStr() {
        return _baseStr;
    }

    public void addBaseStr(int i) {
        i += _baseStr;
        if (i >= 254) {
            i = 254;
        } else if (i < 1) {
            i = 1;
        }
        addStr(i - _baseStr);
        _baseStr = i;
    }

    public int getBaseCon() {
        return _baseCon;
    }

    public void addBaseCon(int i) {
        i += _baseCon;
        if (i >= 254) {
            i = 254;
        } else if (i < 1) {
            i = 1;
        }
        addCon(i - _baseCon);
        _baseCon = i;
    }

    public int getBaseDex() {
        return _baseDex;
    }

    public void addBaseDex(int i) {
        i += _baseDex;
        if (i >= 254) {
            i = 254;
        } else if (i < 1) {
            i = 1;
        }
        addDex(i - _baseDex);
        _baseDex = i;
    }

    public int getBaseCha() {
        return _baseCha;
    }

    public void addBaseCha(int i) {
        i += _baseCha;
        if (i >= 254) {
            i = 254;
        } else if (i < 1) {
            i = 1;
        }
        addCha(i - _baseCha);
        _baseCha = i;
    }

    public int getBaseInt() {
        return _baseInt;
    }

    public void addBaseInt(int i) {
        i += _baseInt;
        if (i >= 254) {
            i = 254;
        } else if (i < 1) {
            i = 1;
        }
        addInt(i - _baseInt);
        _baseInt = i;
    }

    public int getBaseWis() {
        return _baseWis;
    }

    public void addBaseWis(int i) {
        i += _baseWis;
        if (i >= 254) {
            i = 254;
        } else if (i < 1) {
            i = 1;
        }
        addWis(i - _baseWis);
        _baseWis = i;
    }

    public int getOriginalStr() {
        return _originalStr;
    }

    public void setOriginalStr(int i) {
        _originalStr = i;
    }

    public int getOriginalCon() {
        return _originalCon;
    }

    public void setOriginalCon(int i) {
        _originalCon = i;
    }

    public int getOriginalDex() {
        return _originalDex;
    }

    public void setOriginalDex(int i) {
        _originalDex = i;
    }

    public int getOriginalCha() {
        return _originalCha;
    }

    public void setOriginalCha(int i) {
        _originalCha = i;
    }

    public int getOriginalInt() {
        return _originalInt;
    }

    public void setOriginalInt(int i) {
        _originalInt = i;
    }

    public int getOriginalWis() {
        return _originalWis;
    }

    public void setOriginalWis(int i) {
        _originalWis = i;
    }

    public int getOriginalDmgup() {
        return _originalDmgup;
    }

    public int getOriginalBowDmgup() {
        return _originalBowDmgup;
    }

    public int getOriginalHitup() {
        return _originalHitup;
    }

    public int getOriginalBowHitup() {
        return _originalHitup + _originalBowHitup;
    }

    public int getOriginalMr() {
        return _originalMr;
    }

    public void addOriginalMagicHit(int i) {
        _originalMagicHit += i;
    }

    public int getOriginalMagicHit() {
        return _originalMagicHit;
    }

    public void addOriginalMagicCritical(int i) {
        _originalMagicCritical += i;
    }

    public int getOriginalMagicCritical() {
        return _originalMagicCritical;
    }

    public int getOriginalMagicConsumeReduction() {
        return _originalMagicConsumeReduction;
    }

    public int getOriginalMagicDamage() {
        return _originalMagicDamage;
    }

    public int getOriginalHpup() {
        return _originalHpup;
    }

    public int getOriginalMpup() {
        return _originalMpup;
    }

    public int getBaseDmgup() {
        return _baseDmgup;
    }

    public int getBaseBowDmgup() {
        return _baseBowDmgup;
    }

    public int getBaseHitup() {
        return _baseHitup;
    }

    public int getBaseBowHitup() {
        return _baseBowHitup;
    }

    public int getBaseMr() {
        return _baseMr;
    }

    public int getAdvenHp() {
        return _advenHp;
    }

    public void setAdvenHp(int i) {
        _advenHp = i;
    }

    public int getAdvenMp() {
        return _advenMp;
    }

    public void setAdvenMp(int i) {
        _advenMp = i;
    }

    public int getHighLevel() {
        return _highLevel;
    }

    public void setHighLevel(int i) {
        _highLevel = i;
    }

    public int getBonusStats() {
        return _bonusStats;
    }

    public void setBonusStats(int i) {
        _bonusStats = i;
    }

    public int getElixirStats() {
        return _elixirStats;
    }

    public void setElixirStats(int i) {
        _elixirStats = i;
    }

    public int getElfAttr() {
        return _elfAttr;
    }

    public void setElfAttr(int i) {
        _elfAttr = i;
    }

    public int getExpRes() {
        return _expRes;
    }

    public void setExpRes(int i) {
        _expRes = i;
    }

    public int getPartnerId() {
        return _partnerId;
    }

    public void setPartnerId(int i) {
        _partnerId = i;
    }

    public int getOnlineStatus() {
        return _onlineStatus;
    }

    public void setOnlineStatus(int i) {
        _onlineStatus = i;
    }

    public int getHomeTownId() {
        return _homeTownId;
    }

    public void setHomeTownId(int i) {
        _homeTownId = i;
    }

    public int getContribution() {
        return _contribution;
    }

    public void setContribution(int i) {
        _contribution = i;
    }

    public int getPay() {
        return _pay;
    }

    public void setPay(int i) {
        _pay = i;
    }

    public int getHellTime() {
        return _hellTime;
    }

    public void setHellTime(int i) {
        _hellTime = i;
    }

    public boolean isBanned() {
        return _banned;
    }

    public void setBanned(boolean flag) {
        _banned = flag;
    }

    public int get_food() {
        return _food;
    }

    public void set_food(int i) {
        if (i > 225) {
            i = 225;
        } else if (i < 0) {
            i = 0;
        }
        _food = i;
        if (_food == 225) {
            Calendar cal = Calendar.getInstance();
            long h_time = cal.getTimeInMillis() / 1000L;
            set_h_time(h_time);
        } else {
            set_h_time(-1L);
        }
    }

    public L1EquipmentSlot getEquipSlot() {
        return _equipSlot;
    }

    public void save() throws Exception {
        if (isGhost()) {
            return;
        }
        if (isInCharReset()) {
            return;
        }
        if (_other != null) {
            CharOtherReading.get().storeOther(getId(), _other);
        }
        if (_other1 != null) {
            CharOtherReading1.get().storeOther(getId(), _other1);
        }
        if (_other2 != null) {
            CharOtherReading2.get().storeOther(getId(), _other2);
        }
        if (_other3 != null) {
            CharOtherReading3.get().storeOther(getId(), _other3);
        }
        CharacterTable.get().storeCharacter(this);
    }

    public void saveInventory() {
        Iterator<L1ItemInstance> iterator = getInventory().getItems().iterator();
        while (iterator.hasNext()) {
            L1ItemInstance item = iterator.next();
            getInventory().saveItem(item, item.getRecordingColumns());
        }
    }

    public double getMaxWeight() {
        int str = getStr();
        int con = getCon();
        double maxWeight = 150.0 * Math.floor(0.6 * str + 0.4 * con + 1.0) * get_weightUP();
        double weightReductionByArmor = getWeightReduction();
        weightReductionByArmor /= 100.0;
        int weightReductionByMagic = 0;
        if (hasSkillEffect(14) || hasSkillEffect(218)) {
            weightReductionByMagic = 180;
        }
        double originalWeightReduction = 0.0;
        originalWeightReduction += 0.04 * (getOriginalStrWeightReduction() + getOriginalConWeightReduction());
        double weightReduction = 1.0 + weightReductionByArmor + originalWeightReduction;
        maxWeight *= weightReduction;
        maxWeight += weightReductionByMagic;
        maxWeight *= ConfigRate.RATE_WEIGHT_LIMIT;
        return maxWeight;
    }

    public boolean isRibrave() {
        return hasSkillEffect(1017);
    }

    public boolean isFastMovable() {
        return hasSkillEffect(52) || hasSkillEffect(101) || hasSkillEffect(150)
                || hasSkillEffect(1017);
    }

    public boolean isFastAttackable() {
        return false;
    }

    public boolean isBrave() {
        return hasSkillEffect(1000);
    }

    public boolean isElfBrave() {
        return hasSkillEffect(1016);
    }

    public boolean isBraveX() {
        return hasSkillEffect(STATUS_BRAVE3);
    }

    public boolean isHaste() {
        return hasSkillEffect(1001) || hasSkillEffect(43) || hasSkillEffect(54)
                || getMoveSpeed() == 1;
    }

    public boolean isInvisDelay() {
        return invisDelayCounter > 0;
    }

    public void addInvisDelayCounter(int counter) {
        synchronized (_invisTimerMonitor) {
            invisDelayCounter += counter;
        }
    }

    public void beginInvisTimer() {
        addInvisDelayCounter(1);
        GeneralThreadPool.get().pcSchedule(new L1PcInvisDelay(getId()), 3000L);
    }

    public synchronized void addLawful(int i) {
        int lawful = getLawful() + i;
        if (lawful > 32767) {
            lawful = 32767;
        } else if (lawful < -32768) {
            lawful = -32768;
        }
        setLawful(lawful);
    }

    public synchronized void addExp(long exp) {
        long newexp = _exp + exp;
        if (!isAddExp(newexp)) {
            return;
        }
        setExp(newexp);
    }

    private boolean isAddExp(long exp) {
        int level = ConfigOther.PcLevelUp + 1;
        long maxExp = ExpTable.getExpByLevel(level) - 44L;
        if (exp >= maxExp) {
            _exp = maxExp;
            return false;
        }
        return true;
    }

    public synchronized void addContribution(int contribution) {
        _contribution += contribution;
    }

    private void levelUp(int gap) {
        resetLevel();
        int i = 0;
        while (i < gap) {
            short randomHp = CalcStat.calcStatHp(getType(), getBaseMaxHp(), getBaseCon(),
                    getOriginalHpup());
            short randomMp = CalcStat.calcStatMp(getType(), getBaseMaxMp(), getBaseWis(),
                    getOriginalMpup());
            addBaseMaxHp(randomHp);
            addBaseMaxMp(randomMp);
            ++i;
        }
        if (ConfigOther.logpcgiveitem && getLevel() >= ConfigOther.logpclevel) {
            try {
                L1Item l1item = ItemTable.get().getTemplate(43000);
                if (l1item != null && getInventory().checkAddItem(l1item, 1L) == 0) {
                    getInventory().storeItem(43000, 1L);
                    sendPackets(new S_ServerMessage(403, l1item.getName()));
                } else {
                    sendPackets(new S_SystemMessage("無法獲得轉生藥水。可能此道具不存在！"));
                }
            } catch (Exception e) {
                sendPackets(new S_SystemMessage("無法獲得轉生藥水。可能此道具不存在！"));
            }
        }
        resetBaseHitup();
        resetBaseDmgup();
        resetBaseAc();
        resetBaseMr();
        if (getLevel() > getHighLevel()) {
            setHighLevel(getLevel());
        }
        setCurrentHp(getMaxHp());
        setCurrentMp(getMaxMp());
        try {
            save();
        } catch (Exception e) {
            L1PcInstance._log.error(e.getLocalizedMessage(), e);
            return;
        } finally {
            getApprentice();
            sendPackets(new S_OwnCharStatus(this));
            Reward.getItem(this);
            Reward1.getItem(this);
            if (lvgiveitemcount.START) {
                L1WilliamLimitedReward.check_Task_For_Level(this);
            }
            MapLevelTable.get().get_level(getMapId(), this);
            showWindows();
            if (Addskill.START) {
                AutoAddSkillTable.get().forAutoAddSkill(this);
            }
            if (ConfigOther.newcharpra && getnewcharpra() && getLevel() >= ConfigOther.newcharpralv) {
                setnewcharpra(false);
                sendPackets(new S_ServerMessage("您的等級已達新手保護機制。"));
            }
        }
    }

    public void showWindows() {
        if (power()) {
            if (getMeteLevel() > 0 && ConfigOther.logpcpower) {
                sendPackets(new S_Bonusstats(getId()));
            } else if (getMeteLevel() == 0) {
                sendPackets(new S_Bonusstats(getId()));
            }
        }
    }

    public void isWindows() {
        if (power()) {
            sendPackets(new S_NPCTalkReturn(getId(), "y_qs_10"));
        } else {
            sendPackets(new S_NPCTalkReturn(getId(), "y_qs_00"));
        }
    }

    public boolean power() {
        if (getLevel() >= 51 && getLevel() - 50 > getBonusStats()) {
            int power = getBaseStr() + getBaseDex() + getBaseCon() + getBaseInt()
                    + getBaseWis() + getBaseCha();
            if (power < ConfigAlt.POWER * 6) {
                return true;
            }
        }
        return false;
    }

    private void levelDown(int gap) {
        resetLevel();
        int i = 0;
        while (i > gap) {
            short randomHp = CalcStat.calcStatHp(getType(), 0, getBaseCon(), getOriginalHpup());
            short randomMp = CalcStat.calcStatMp(getType(), 0, getBaseWis(), getOriginalMpup());
            addBaseMaxHp((short) (-randomHp));
            addBaseMaxMp((short) (-randomMp));
            --i;
        }
        if (getLevel() == 1) {
            int initHp = CalcInitHpMp.calcInitHp(this);
            int initMp = CalcInitHpMp.calcInitMp(this);
            addBaseMaxHp((short) (-getBaseMaxHp()));
            addBaseMaxHp((short) initHp);
            setCurrentHp((short) initHp);
            addBaseMaxMp((short) (-getBaseMaxMp()));
            addBaseMaxMp((short) initMp);
            setCurrentMp((short) initMp);
        }
        resetBaseHitup();
        resetBaseDmgup();
        resetBaseAc();
        resetBaseMr();
        getApprentice();
        try {
            save();
        } catch (Exception e) {
            L1PcInstance._log.error(e.getLocalizedMessage(), e);
        } finally {
            sendPackets(new S_OwnCharStatus(this));
            MapLevelTable.get().get_level(getMapId(), this);
        }
    }

    public boolean isGhost() {
        return _ghost;
    }

    private void setGhost(boolean flag) {
        _ghost = flag;
    }

    public int get_ghostTime() {
        return _ghostTime;
    }

    public void set_ghostTime(int ghostTime) {
        _ghostTime = ghostTime;
    }

    public boolean isGhostCanTalk() {
        return _ghostCanTalk;
    }

    private void setGhostCanTalk(boolean flag) {
        _ghostCanTalk = flag;
    }

    public boolean isReserveGhost() {
        return _isReserveGhost;
    }

    public void setReserveGhost(boolean flag) {
        _isReserveGhost = flag;
    }

    public void beginGhost(int locx, int locy, short mapid, boolean canTalk) {
        beginGhost(locx, locy, mapid, canTalk, 0);
    }

    public void beginGhost(int locx, int locy, short mapid, boolean canTalk, int sec) {
        if (isGhost()) {
            return;
        }
        setGhost(true);
        _ghostSaveLocX = getX();
        _ghostSaveLocY = getY();
        _ghostSaveMapId = getMapId();
        _ghostSaveHeading = getHeading();
        setGhostCanTalk(canTalk);
        L1Teleport.teleport(this, locx, locy, mapid, 5, true);
        if (sec > 0) {
            set_ghostTime(sec * 1000);
        }
    }

    public void makeReadyEndGhost() {
        setReserveGhost(true);
        L1Teleport.teleport(this, _ghostSaveLocX, _ghostSaveLocY, _ghostSaveMapId,
                _ghostSaveHeading, true);
    }

    public void makeReadyEndGhost(boolean effectble) {
        setReserveGhost(true);
        L1Teleport.teleport(this, _ghostSaveLocX, _ghostSaveLocY, _ghostSaveMapId,
                _ghostSaveHeading, effectble);
    }

    public void endGhost() {
        set_ghostTime(-1);
        setGhost(false);
        setGhostCanTalk(true);
        setReserveGhost(false);
    }

    public void beginHell(boolean isFirst) {
        if (getMapId() != 666) {
            final int locx = 32701;
            final int locy = 32777;
            final short mapid = 666;
            L1Teleport.teleport(this, locx, locy, mapid, 5, false);
        }
        if (isFirst) {
            if (get_PKcount() <= 10) {
                setHellTime(180);
            } else {
                setHellTime(180);
            }
            sendPackets(new S_BlueMessage(552, String.valueOf(get_PKcount()),
                    String.valueOf(getHellTime() / 60)));
        } else {
            sendPackets(new S_BlueMessage(637, String.valueOf(getHellTime())));
        }
    }

    public void endHell() {
        int[] loc = L1TownLocation.getGetBackLoc(4);
        L1Teleport.teleport(this, loc[0], loc[1], (short) loc[2], 5, true);
        try {
            save();
        } catch (Exception e) {
            L1PcInstance._log.error(e.getLocalizedMessage(), e);
        }
    }

    @Override
    public void setPoisonEffect(int effectId) {
        sendPackets(new S_Poison(getId(), effectId));
        if (!isGmInvis() && !isGhost() && !isInvisble()) {
            broadcastPacketAll(new S_Poison(getId(), effectId));
        }
    }

    @Override
    public void healHp(int pt) {
        super.healHp(pt);
        sendPackets(new S_HPUpdate(this));
    }

    @Override
    public int getKarma() {
        return _karma.get();
    }

    @Override
    public void setKarma(int i) {
        _karma.set(i);
    }

    public void addKarma(int i) {
        synchronized (_karma) {
            _karma.add(i);
            onChangeKarma();
        }
    }

    public int getKarmaLevel() {
        return _karma.getLevel();
    }

    public int getKarmaPercent() {
        return _karma.getPercent();
    }

    public Timestamp getLastPk() {
        return _lastPk;
    }

    public void setLastPk(Timestamp time) {
        _lastPk = time;
    }

    public void setLastPk() {
        _lastPk = new Timestamp(System.currentTimeMillis());
    }

    public boolean isWanted() {
        if (_lastPk == null) {
            return false;
        }
        if (System.currentTimeMillis() - _lastPk.getTime() > 3600000L) {
            setLastPk(null);
            return false;
        }
        return true;
    }

    public Timestamp getLastPkForElf() {
        return _lastPkForElf;
    }

    public void setLastPkForElf(Timestamp time) {
        _lastPkForElf = time;
    }

    public void setLastPkForElf() {
        _lastPkForElf = new Timestamp(System.currentTimeMillis());
    }

    public boolean isWantedForElf() {
        if (_lastPkForElf == null) {
            return false;
        }
        if (System.currentTimeMillis() - _lastPkForElf.getTime() > 86400000L) {
            setLastPkForElf(null);
            return false;
        }
        return true;
    }

    public Timestamp getDeleteTime() {
        return _deleteTime;
    }

    public void setDeleteTime(Timestamp time) {
        _deleteTime = time;
    }

    @Override
    public int getMagicLevel() {
        return getClassFeature().getMagicLevel(getLevel());
    }

    public double get_weightUP() {
        return _weightUP;
    }

    public void add_weightUP(int i) {
        _weightUP += i / 100.0;
    }

    public int getWeightReduction() {
        return _weightReduction;
    }

    public void addWeightReduction(int i) {
        _weightReduction += i;
    }

    public int getOriginalStrWeightReduction() {
        return _originalStrWeightReduction;
    }

    public int getOriginalConWeightReduction() {
        return _originalConWeightReduction;
    }

    public int getHasteItemEquipped() {
        return _hasteItemEquipped;
    }

    public void addHasteItemEquipped(int i) {
        _hasteItemEquipped += i;
    }

    public void removeHasteSkillEffect() {
        if (hasSkillEffect(29)) {
            removeSkillEffect(29);
        }
        if (hasSkillEffect(76)) {
            removeSkillEffect(76);
        }
        if (hasSkillEffect(152)) {
            removeSkillEffect(152);
        }
        if (hasSkillEffect(43)) {
            removeSkillEffect(43);
        }
        if (hasSkillEffect(54)) {
            removeSkillEffect(54);
        }
        if (hasSkillEffect(1001)) {
            removeSkillEffect(1001);
        }
    }

    public int getDamageReductionByArmor() {
        return _damageReductionByArmor;
    }

    public void addDamageReductionByArmor(int i) {
        _damageReductionByArmor += i;
    }

    public int getHitModifierByArmor() {
        return _hitModifierByArmor;
    }

    public void addHitModifierByArmor(int i) {
        _hitModifierByArmor += i;
    }

    public int getDmgModifierByArmor() {
        return _dmgModifierByArmor;
    }

    public void addDmgModifierByArmor(int i) {
        _dmgModifierByArmor += i;
    }

    public int getBowHitModifierByArmor() {
        return _bowHitModifierByArmor;
    }

    public void addBowHitModifierByArmor(int i) {
        _bowHitModifierByArmor += i;
    }

    public int getBowDmgModifierByArmor() {
        return _bowDmgModifierByArmor;
    }

    public void addBowDmgModifierByArmor(int i) {
        _bowDmgModifierByArmor += i;
    }

    public boolean isGresValid() {
        return _gresValid;
    }

    private void setGresValid(boolean valid) {
        _gresValid = valid;
    }

    public boolean isFishing() {
        return _isFishing;
    }

    public int get_fishX() {
        return _fishX;
    }

    public int get_fishY() {
        return _fishY;
    }

    public void setFishing(boolean flag, int fishX, int fishY) {
        _isFishing = flag;
        _fishX = fishX;
        _fishY = fishY;
    }

    public int getFishingPoleId() {
        return _fishingpoleid;
    }

    public void setFishingPoleId(int itemid) {
        _fishingpoleid = itemid;
    }

    public int getCookingId() {
        return _cookingId;
    }

    public void setCookingId(int i) {
        _cookingId = i;
    }

    public int getDessertId() {
        return _dessertId;
    }

    public void setDessertId(int i) {
        _dessertId = i;
    }

    public void resetBaseDmgup() {
        int newBaseDmgup = 0;
        int newBaseBowDmgup = 0;
        if (isKnight() || isDarkelf() || isDragonKnight()) {
            newBaseDmgup = getLevel() / 10;
            newBaseBowDmgup = 0;
        } else if (isElf()) {
            newBaseDmgup = 0;
            newBaseBowDmgup = getLevel() / 10;
        }
        addDmgup(newBaseDmgup - _baseDmgup);
        addBowDmgup(newBaseBowDmgup - _baseBowDmgup);
        _baseDmgup = newBaseDmgup;
        _baseBowDmgup = newBaseBowDmgup;
    }

    public void resetBaseHitup() {
        int newBaseHitup = 0;
        int newBaseBowHitup = 0;
        if (isCrown()) {
            newBaseHitup = getLevel() / 5;
            newBaseBowHitup = getLevel() / 5;
        } else if (isKnight()) {
            newBaseHitup = getLevel() / 3;
            newBaseBowHitup = getLevel() / 3;
        } else if (isElf()) {
            newBaseHitup = getLevel() / 5;
            newBaseBowHitup = getLevel() / 5;
        } else if (isDarkelf()) {
            newBaseHitup = getLevel() / 3;
            newBaseBowHitup = getLevel() / 3;
        } else if (isDragonKnight()) {
            newBaseHitup = getLevel() / 4;
            newBaseBowHitup = getLevel() / 4;
        } else if (isIllusionist()) {
            newBaseHitup = getLevel() / 5;
            newBaseBowHitup = getLevel() / 5;
        }
        addHitup(newBaseHitup - _baseHitup);
        addBowHitup(newBaseBowHitup - _baseBowHitup);
        _baseHitup = newBaseHitup;
        _baseBowHitup = newBaseBowHitup;
    }

    public void resetBaseAc() {
        int newAc = CalcStat.calcAc(getLevel(), getBaseDex());
        addAc(newAc - _baseAc);
        _baseAc = newAc;
    }

    public void resetBaseMr() {
        int newMr = 0;
        if (isCrown()) {
            newMr = 10;
        } else if (isElf()) {
            newMr = 25;
        } else if (isWizard()) {
            newMr = 15;
        } else if (isDarkelf()) {
            newMr = 10;
        } else if (isDragonKnight()) {
            newMr = 18;
        } else if (isIllusionist()) {
            newMr = 20;
        }
        newMr += CalcStat.calcStatMr(getWis());
        newMr += getLevel() / 2;
        addMr(newMr - _baseMr);
        _baseMr = newMr;
    }

    public void resetLevel() {
        setLevel(ExpTable.getLevelByExp(_exp));
    }

    public void resetOriginalHpup() {
        _originalHpup = L1PcOriginal.resetOriginalHpup(this);
    }

    public void resetOriginalMpup() {
        _originalMpup = L1PcOriginal.resetOriginalMpup(this);
    }

    public void resetOriginalStrWeightReduction() {
        _originalStrWeightReduction = L1PcOriginal.resetOriginalStrWeightReduction(this);
    }

    public void resetOriginalDmgup() {
        _originalDmgup = L1PcOriginal.resetOriginalDmgup(this);
    }

    public void resetOriginalConWeightReduction() {
        _originalConWeightReduction = L1PcOriginal.resetOriginalConWeightReduction(this);
    }

    public void resetOriginalBowDmgup() {
        _originalBowDmgup = L1PcOriginal.resetOriginalBowDmgup(this);
    }

    public void resetOriginalHitup() {
        _originalHitup = L1PcOriginal.resetOriginalHitup(this);
    }

    public void resetOriginalBowHitup() {
        _originalBowHitup = L1PcOriginal.resetOriginalBowHitup(this);
    }

    public void resetOriginalMr() {
        addMr(_originalMr = L1PcOriginal.resetOriginalMr(this));
    }

    public void resetOriginalMagicHit() {
        _originalMagicHit = L1PcOriginal.resetOriginalMagicHit(this);
    }

    public void resetOriginalMagicCritical() {
        _originalMagicCritical = L1PcOriginal.resetOriginalMagicCritical(this);
    }

    public void resetOriginalMagicConsumeReduction() {
        _originalMagicConsumeReduction = L1PcOriginal.resetOriginalMagicConsumeReduction(this);
    }

    public void resetOriginalMagicDamage() {
        _originalMagicDamage = L1PcOriginal.resetOriginalMagicDamage(this);
    }

    public void resetOriginalAc() {
        _originalAc = L1PcOriginal.resetOriginalAc(this);
        addAc(0 - _originalAc);
    }

    public void resetOriginalEr() {
        _originalEr = L1PcOriginal.resetOriginalEr(this);
    }

    public void resetOriginalHpr() {
        _originalHpr = L1PcOriginal.resetOriginalHpr(this);
    }

    public void resetOriginalMpr() {
        _originalMpr = L1PcOriginal.resetOriginalMpr(this);
    }

    public void refresh() {
        resetLevel();
        resetBaseHitup();
        resetBaseDmgup();
        resetBaseMr();
        resetBaseAc();
        resetOriginalHpup();
        resetOriginalMpup();
        resetOriginalDmgup();
        resetOriginalBowDmgup();
        resetOriginalHitup();
        resetOriginalBowHitup();
        resetOriginalMr();
        resetOriginalMagicHit();
        resetOriginalMagicCritical();
        resetOriginalMagicConsumeReduction();
        resetOriginalMagicDamage();
        resetOriginalAc();
        resetOriginalEr();
        resetOriginalHpr();
        resetOriginalMpr();
        resetOriginalStrWeightReduction();
        resetOriginalConWeightReduction();
    }

    public L1ExcludingList getExcludingList() {
        return _excludingList;
    }

    public int getTeleportX() {
        return _teleportX;
    }

    public void setTeleportX(int i) {
        _teleportX = i;
    }

    public int getTeleportY() {
        return _teleportY;
    }

    public void setTeleportY(int i) {
        _teleportY = i;
    }

    public short getTeleportMapId() {
        return _teleportMapId;
    }

    public void setTeleportMapId(short i) {
        _teleportMapId = i;
    }

    public int getTeleportHeading() {
        return _teleportHeading;
    }

    public void setTeleportHeading(int i) {
        _teleportHeading = i;
    }

    public int getTempCharGfxAtDead() {
        return _tempCharGfxAtDead;
    }

    private void setTempCharGfxAtDead(int i) {
        _tempCharGfxAtDead = i;
    }

    public boolean isCanWhisper() {
        return _isCanWhisper;
    }

    public void setCanWhisper(boolean flag) {
        _isCanWhisper = flag;
    }

    public boolean isShowTradeChat() {
        return _isShowTradeChat;
    }

    public void setShowTradeChat(boolean flag) {
        _isShowTradeChat = flag;
    }

    public boolean isShowWorldChat() {
        return _isShowWorldChat;
    }

    public void setShowWorldChat(boolean flag) {
        _isShowWorldChat = flag;
    }

    public int getFightId() {
        return _fightId;
    }

    public void setFightId(int i) {
        _fightId = i;
    }

    public void checkChatInterval() {
        long nowChatTimeInMillis = System.currentTimeMillis();
        if (_chatCount == 0) {
            ++_chatCount;
            _oldChatTimeInMillis = nowChatTimeInMillis;
            return;
        }
        long chatInterval = nowChatTimeInMillis - _oldChatTimeInMillis;
        if (chatInterval > 2000L) {
            _chatCount = 0;
            _oldChatTimeInMillis = 0L;
        } else {
            if (_chatCount >= 5) {
                setSkillEffect(4002, 120000);
                sendPackets(new S_PacketBox(36, 120));
                sendPackets(new S_ServerMessage(153));
                _chatCount = 0;
                _oldChatTimeInMillis = 0L;
            }
            ++_chatCount;
        }
    }

    public int getCallClanId() {
        return _callClanId;
    }

    public void setCallClanId(int i) {
        _callClanId = i;
    }

    public int getCallClanHeading() {
        return _callClanHeading;
    }

    public void setCallClanHeading(int i) {
        _callClanHeading = i;
    }

    public boolean isInCharReset() {
        return _isInCharReset;
    }

    public void setInCharReset(boolean flag) {
        _isInCharReset = flag;
    }

    public int getTempLevel() {
        return _tempLevel;
    }

    public void setTempLevel(int i) {
        _tempLevel = i;
    }

    public int getTempMaxLevel() {
        return _tempMaxLevel;
    }

    public void setTempMaxLevel(int i) {
        _tempMaxLevel = i;
    }

    public boolean isSummonMonster() {
        return _isSummonMonster;
    }

    public void setSummonMonster(boolean SummonMonster) {
        _isSummonMonster = SummonMonster;
    }

    public boolean isShapeChange() {
        return _isShapeChange;
    }

    public void setShapeChange(boolean isShapeChange) {
        _isShapeChange = isShapeChange;
    }

    public String getText() {
        return _text;
    }

    public void setText(String text) {
        _text = text;
    }

    public byte[] getTextByte() {
        return _textByte;
    }

    public void setTextByte(byte[] textByte) {
        _textByte = textByte;
    }

    public L1PcOther get_other() {
        return _other;
    }

    public void set_other(L1PcOther other) {
        _other = other;
    }

    public L1PcOther1 get_other1() {
        return _other1;
    }

    public void set_other1(L1PcOther1 other1) {
        _other1 = other1;
    }

    public L1PcOther2 get_other2() {
        return _other2;
    }

    public void set_other2(L1PcOther2 l1PcOther2) {
        _other2 = l1PcOther2;
    }

    public L1PcOther3 get_other3() {
        return _other3;
    }

    public void set_other3(L1PcOther3 other3) {
        _other3 = other3;
    }

    public L1PcOtherList get_otherList() {
        return _otherList;
    }

    public void set_otherList(L1PcOtherList other) {
        _otherList = other;
    }

    public int getOleLocX() {
        return _oleLocX;
    }

    public void setOleLocX(int oleLocx) {
        _oleLocX = oleLocx;
    }

    public int getOleLocY() {
        return _oleLocY;
    }

    public void setOleLocY(int oleLocy) {
        _oleLocY = oleLocy;
    }

    public L1Character getNowTarget() {
        return _target;
    }

    public void setNowTarget(L1Character target) {
        _target = target;
    }

    public void setPetModel() {
        try {
            Iterator<L1NpcInstance> iterator = getPetList().values().iterator();
            while (iterator.hasNext()) {
                L1NpcInstance petNpc = iterator.next();
                if (petNpc != null) {
                    if (petNpc instanceof L1SummonInstance) {
                        L1SummonInstance summon = (L1SummonInstance) petNpc;
                        summon.set_tempModel();
                    } else {
                        if (!(petNpc instanceof L1PetInstance)) {
                            continue;
                        }
                        L1PetInstance pet = (L1PetInstance) petNpc;
                        pet.set_tempModel();
                    }
                }
            }
        } catch (Exception e) {
            L1PcInstance._log.error(e.getLocalizedMessage(), e);
        }
    }

    public void getPetModel() {
        try {
            Iterator<L1NpcInstance> iterator = getPetList().values().iterator();
            while (iterator.hasNext()) {
                L1NpcInstance petNpc = iterator.next();
                if (petNpc != null) {
                    if (petNpc instanceof L1SummonInstance) {
                        L1SummonInstance summon = (L1SummonInstance) petNpc;
                        summon.get_tempModel();
                    } else {
                        if (!(petNpc instanceof L1PetInstance)) {
                            continue;
                        }
                        L1PetInstance pet = (L1PetInstance) petNpc;
                        pet.get_tempModel();
                    }
                }
            }
        } catch (Exception e) {
            L1PcInstance._log.error(e.getLocalizedMessage(), e);
        }
    }

    public L1DeInstance get_outChat() {
        return _outChat;
    }

    public void set_outChat(L1DeInstance b) {
        _outChat = b;
    }

    public long get_h_time() {
        return _h_time;
    }

    public void set_h_time(long time) {
        _h_time = time;
    }

    public boolean is_mazu() {
        return _mazu;
    }

    public void set_mazu(boolean b) {
        _mazu = b;
    }

    public long get_mazu_time() {
        return _mazu_time;
    }

    public void set_mazu_time(long time) {
        _mazu_time = time;
    }

    public void set_dmgAdd(int int1, int int2) {
        _int1 += int1;
        _int2 += int2;
    }

    public int dmgAdd() {
        if (_int2 == 0) {
            return 0;
        }
        if (L1PcInstance._random.nextInt(100) + 1 <= _int2) {
            if (!getDolls().isEmpty()) {
                Iterator<L1DollInstance> iterator = getDolls().values().iterator();
                while (iterator.hasNext()) {
                    L1DollInstance doll = iterator.next();
                    doll.show_action(1);
                }
            }
            return _int1;
        }
        return 0;
    }

    public int get_evasion() {
        return _evasion;
    }

    public void set_evasion(int int1) {
        _evasion += int1;
    }

    public double getExpAdd() {
        return _expadd;
    }

    public void add_exp(int s) {
        if (s > 0) {
            _expadd = DoubleUtil.sum(_expadd, s / 100.0);
        } else {
            _expadd = DoubleUtil.sub(_expadd, s * -1 / 100.0);
        }
    }

    public void set_dmgDowe(int int1, int int2) {
        _dd1 += int1;
        _dd2 += int2;
    }

    public int dmgDowe() {
        if (has_powerid(6611)) {
            final int rad = 10;
            if (L1PcInstance._random.nextInt(100) < rad) {
                sendPacketsAll(new S_SkillSound(getId(), 9800));
                return _dd1;
            }
        }
        if (_dd2 == 0) {
            return 0;
        }
        if (L1PcInstance._random.nextInt(100) + 1 <= _dd2) {
            if (!getDolls().isEmpty()) {
                Iterator<L1DollInstance> iterator = getDolls().values().iterator();
                while (iterator.hasNext()) {
                    L1DollInstance doll = iterator.next();
                    doll.show_action(2);
                }
            }
            return _dd1;
        }
        return 0;
    }

    public boolean isFoeSlayer() {
        return _isFoeSlayer;
    }

    public void setFoeSlayer(boolean FoeSlayer) {
        _isFoeSlayer = FoeSlayer;
    }

    public long get_weaknss_t() {
        return _weaknss_t;
    }

    public int get_weaknss() {
        return _weaknss;
    }

    public void set_weaknss(int lv, long t) {
        _weaknss = lv;
        _weaknss_t = t;
    }

    public int get_actionId() {
        return _actionId;
    }

    public void set_actionId(int actionId) {
        _actionId = actionId;
    }

    public Chapter01R get_hardinR() {
        return _hardin;
    }

    public void set_hardinR(Chapter01R hardin) {
        _hardin = hardin;
    }

    public void add_power(L1ItemPower_text value, L1ItemInstance eq) {
        if (!_allpowers.containsKey(Integer.valueOf(value.get_id()))) {
            _allpowers.put(Integer.valueOf(value.get_id()), value);
        }
        if (eq.isEquipped()) {
            value.add_pc_power(this);
            sendPackets(new S_ServerMessage("\\fW獲得" + value.getMsg() + " 效果"));
        }
        if (value.getGfx() != null) {
            int[] gfx2;
            int length = (gfx2 = value.getGfx()).length;
            int i = 0;
            while (i < length) {
                int gfx = gfx2[i];
                sendPacketsAll(new S_SkillSound(getId(), gfx));
                ++i;
            }
        }
    }

    public void remove_power(L1ItemPower_text value, L1ItemInstance eq) {
        if (_allpowers.containsKey(Integer.valueOf(value.get_id()))) {
            _allpowers.remove(Integer.valueOf(value.get_id()));
        }
        if (!eq.isEquipped()) {
            value.remove_pc_power(this);
            sendPackets(new S_ServerMessage("\\fY失去 " + value.getMsg() + " 效果"));
        }
    }

    public boolean has_powerid(int powerid) {
        return _allpowers.containsKey(Integer.valueOf(powerid));
    }

    public Map<Integer, L1ItemPower_text> get_allpowers() {
        return _allpowers;
    }

    public int get_unfreezingTime() {
        return _unfreezingTime;
    }

    public void set_unfreezingTime(int i) {
        _unfreezingTime = i;
    }

    public int get_misslocTime() {
        return _misslocTime;
    }

    public void set_misslocTime(int i) {
        _misslocTime = i;
    }

    public L1User_Power get_c_power() {
        return _c_power;
    }

    public void set_c_power(L1User_Power power) {
        _c_power = power;
    }

    public void add_dice_hp(int dice_hp, int sucking_hp) {
        _dice_hp += dice_hp;
        _sucking_hp += sucking_hp;
    }

    public int dice_hp() {
        return _dice_hp;
    }

    public int sucking_hp() {
        return _sucking_hp;
    }

    public void add_dice_mp(int dice_mp, int sucking_mp) {
        _dice_mp += dice_mp;
        _sucking_mp += sucking_mp;
    }

    public int dice_mp() {
        return _dice_mp;
    }

    public int sucking_mp() {
        return _sucking_mp;
    }

    public void add_double_dmg(int double_dmg) {
        _double_dmg += double_dmg;
    }

    public int get_double_dmg() {
        return _double_dmg;
    }

    public void add_lift(int lift) {
        _lift += lift;
    }

    public int lift() {
        return _lift;
    }

    public void add_magic_modifier_dmg(int add) {
        _magic_modifier_dmg += add;
    }

    public int get_magic_modifier_dmg() {
        return _magic_modifier_dmg;
    }

    public void add_magic_reduction_dmg(int add) {
        _magic_reduction_dmg += add;
    }

    public int get_magic_reduction_dmg() {
        return _magic_reduction_dmg;
    }

    public void rename(boolean b) {
        _rname = b;
    }

    public boolean is_rname() {
        return _rname;
    }

    public boolean is_retitle() {
        return _retitle;
    }

    public void retitle(boolean b) {
        _retitle = b;
    }

    public int is_repass() {
        return _repass;
    }

    public void repass(int b) {
        _repass = b;
    }

    public void add_trade_item(L1TradeItem info) {
        if (_trade_items.size() == 16) {
            return;
        }
        _trade_items.add(info);
    }

    public ArrayList<L1TradeItem> get_trade_items() {
        return _trade_items;
    }

    public void get_trade_clear() {
        _tradeID = 0;
        _trade_items.clear();
    }

    public int get_mode_id() {
        return _mode_id;
    }

    public void set_mode_id(int mode) {
        _mode_id = mode;
    }

    public boolean get_check_item() {
        return _check_item;
    }

    public void set_check_item(boolean b) {
        _check_item = b;
    }

    public void set_VIP1(boolean b) {
        _vip_1 = b;
    }

    public void set_VIP2(boolean b) {
        _vip_2 = b;
    }

    public void set_VIP3(boolean b) {
        _vip_3 = b;
    }

    public void set_VIP4(boolean b) {
        _vip_4 = b;
    }

    public long get_global_time() {
        return _global_time;
    }

    public void set_global_time(long global_time) {
        _global_time = global_time;
    }

    public int get_doll_hpr() {
        return _doll_hpr;
    }

    public void set_doll_hpr(int hpr) {
        _doll_hpr = hpr;
    }

    public int get_doll_hpr_time() {
        return _doll_hpr_time;
    }

    public void set_doll_hpr_time(int time) {
        _doll_hpr_time = time;
    }

    public int get_doll_hpr_time_src() {
        return _doll_hpr_time_src;
    }

    public void set_doll_hpr_time_src(int time) {
        _doll_hpr_time_src = time;
    }

    public int get_doll_mpr() {
        return _doll_mpr;
    }

    public void set_doll_mpr(int mpr) {
        _doll_mpr = mpr;
    }

    public int get_doll_mpr_time() {
        return _doll_mpr_time;
    }

    public void set_doll_mpr_time(int time) {
        _doll_mpr_time = time;
    }

    public int get_doll_mpr_time_src() {
        return _doll_mpr_time_src;
    }

    public void set_doll_mpr_time_src(int time) {
        _doll_mpr_time_src = time;
    }

    public int[] get_doll_get() {
        return _doll_get;
    }

    public void set_doll_get(int itemid, int count) {
        _doll_get[0] = itemid;
        _doll_get[1] = count;
    }

    public int get_doll_get_time() {
        return _doll_get_time;
    }

    public void set_doll_get_time(int time) {
        _doll_get_time = time;
    }

    public int get_doll_get_time_src() {
        return _doll_get_time_src;
    }

    public void set_doll_get_time_src(int time) {
        _doll_get_time_src = time;
    }

    public String get_board_title() {
        return _board_title;
    }

    public void set_board_title(String text) {
        _board_title = text;
    }

    public String get_board_content() {
        return _board_content;
    }

    public void set_board_content(String text) {
        _board_content = text;
    }

    public long get_spr_move_time() {
        return _spr_move_time;
    }

    public void set_spr_move_time(long spr_time) {
        _spr_move_time = spr_time;
    }

    public long get_spr_attack_time() {
        return _spr_attack_time;
    }

    public void set_spr_attack_time(long spr_time) {
        _spr_attack_time = spr_time;
    }

    public long get_spr_skill_time() {
        return _spr_skill_time;
    }

    public void set_spr_skill_time(long spr_time) {
        _spr_skill_time = spr_time;
    }

    public int get_delete_time() {
        return _delete_time;
    }

    public void set_delete_time(int time) {
        _delete_time = time;
    }

    public void add_up_hp_potion(int up_hp_potion) {
        _up_hp_potion += up_hp_potion;
    }

    public int get_up_hp_potion() {
        return _up_hp_potion;
    }

    public void add_uhp_number(int uhp_number) {
        _uhp_number += uhp_number;
    }

    public int get_uhp_number() {
        return _uhp_number;
    }

    public int get_venom_resist() {
        return _venom_resist;
    }

    public void set_venom_resist(int i) {
        _venom_resist += i;
    }

    public void addInviteList(String playername) {
        if (_InviteList.contains(playername)) {
            return;
        }
        _InviteList.add(playername);
    }

    public void removeInviteList(String name) {
        if (!_InviteList.contains(name)) {
            return;
        }
        _InviteList.remove(name);
    }

    public ArrayList<String> getInviteList() {
        return _InviteList;
    }

    public void addCMAList(String clanname) {
        if (_cmalist.contains(clanname)) {
            return;
        }
        _cmalist.add(clanname);
    }

    public void removeCMAList(String name) {
        if (!_cmalist.contains(name)) {
            return;
        }
        _cmalist.remove(name);
    }

    public ArrayList<String> getCMAList() {
        return _cmalist;
    }

    public final int getEmblemId() {
        if (isProtector() || getClanid() <= 0) {
            return 0;
        }
        L1Clan clan = getClan();
        if (clan == null) {
            return 0;
        }
        return clan.getEmblemId();
    }

    public AcceleratorChecker speed_Attack() {
        if (_speed == null) {
            _speed = new AcceleratorChecker(this);
        }
        return _speed;
    }

    public int get_arena() {
        return _arena;
    }

    public void set_arena(int i) {
        _arena = i;
    }

    public int get_temp_adena() {
        return _temp_adena;
    }

    public void set_temp_adena(int itemid) {
        _temp_adena = itemid;
    }

    public long get_ss_time() {
        return _ss_time;
    }

    public void set_ss_time(long ss_time) {
        _ss_time = ss_time;
    }

    public void set_ss_time(int ss) {
        _ss = ss;
    }

    public int get_ss() {
        return _ss;
    }

    public final int getKillCount() {
        return killCount;
    }

    public final void setKillCount(int killCount) {
        this.killCount = killCount;
    }

    public int getMeteLevel() {
        return _meteLevel;
    }

    public void setMeteLevel(int i) {
        _meteLevel = i;
    }

    public final L1MeteAbility getMeteAbility() {
        return _meteAbility;
    }

    public final void resetMeteAbility() {
        if (_meteAbility != null) {
            ExtraMeteAbilityTable.effectBuff(this, _meteAbility, -1);
        }
        _meteAbility = ExtraMeteAbilityTable.getInstance().get(getMeteLevel(), getType());
        if (_meteAbility != null) {
            ExtraMeteAbilityTable.effectBuff(this, _meteAbility, 1);
        }
    }

    public final boolean isEffectDADIS() {
        return _EffectDADIS;
    }

    public final void setDADIS(boolean checkFlag) {
        if (_EffectDADIS != checkFlag) {
            giveDADIS(checkFlag);
            sendPackets(new S_HPUpdate(this));
            if (isInParty()) {
                getParty().updateMiniHP(this);
            }
            sendPackets(new S_MPUpdate(this));
            sendPackets(new S_SPMR(this));
            L1PcUnlock.Pc_Unlock(this);
        }
    }

    public final void giveDADIS(boolean checkFlag) {
        _EffectDADIS = checkFlag;
        if (checkFlag) {
            addMaxHp(50);
            addMaxMp(30);
            addDmgup(3);
            addBowDmgup(3);
            addSp(3);
            addDamageReductionByArmor(3);
            addHpr(3);
            addMpr(3);
        } else {
            addMaxHp(-50);
            addMaxMp(-30);
            addDmgup(-3);
            addBowDmgup(-3);
            addSp(-3);
            addDamageReductionByArmor(-3);
            addHpr(-3);
            addMpr(-3);
            sendPackets(new S_PacketBox(180, 553, 3907, 0));
        }
    }

    public final boolean isEffectGS() {
        return _EffectGS;
    }

    public final void setGS(boolean checkFlag) {
        if (_EffectGS != checkFlag) {
            giveGS(checkFlag);
            sendPackets(new S_HPUpdate(this));
            if (isInParty()) {
                getParty().updateMiniHP(this);
            }
            sendPackets(new S_MPUpdate(this));
            sendPackets(new S_SPMR(this));
            L1PcUnlock.Pc_Unlock(this);
        }
    }

    public final void giveGS(boolean checkFlag) {
        _EffectGS = checkFlag;
        if (checkFlag) {
            addMaxHp(30);
            addMaxMp(30);
            addDmgup(2);
            addBowDmgup(2);
            addSp(2);
            addDamageReductionByArmor(2);
            addHpr(2);
            addMpr(2);
        } else {
            addMaxHp(-30);
            addMaxMp(-30);
            addDmgup(-2);
            addBowDmgup(-2);
            addSp(-2);
            addDamageReductionByArmor(-2);
            addHpr(-2);
            addMpr(-2);
        }
    }

    public final boolean isProtector() {
        return _isProtector;
    }

    public final void setProtector(boolean checkFlag) {
        if (_isProtector != checkFlag) {
            giveProtector(checkFlag);
            sendPackets(new S_HPUpdate(this));
            if (isInParty()) {
                getParty().updateMiniHP(this);
            }
            sendPackets(new S_MPUpdate(this));
            sendPackets(new S_SPMR(this));
            sendPackets(new S_OwnCharStatus(this));
            sendPackets(new S_OwnCharPack(this));
            removeAllKnownObjects();
            updateObject();
        }
    }

    public final void giveProtector(boolean checkFlag) {
        _isProtector = checkFlag;
        if (checkFlag) {
            addMaxHp(ProtectorSet.HP_UP);
            addMaxMp(ProtectorSet.MP_UP);
            addDmgup(ProtectorSet.DMG_UP);
            addBowDmgup(ProtectorSet.DMG_UP);
            addDamageReductionByArmor(ProtectorSet.DMG_DOWN);
            addSp(ProtectorSet.SP_UP);
            sendPackets(new S_PacketBox(144, 1));
        } else {
            addMaxHp(-ProtectorSet.HP_UP);
            addMaxMp(-ProtectorSet.MP_UP);
            addDmgup(-ProtectorSet.DMG_UP);
            addBowDmgup(-ProtectorSet.DMG_UP);
            addDamageReductionByArmor(-ProtectorSet.DMG_DOWN);
            addSp(-ProtectorSet.SP_UP);
            sendPackets(new S_PacketBox(144, 0));
        }
    }

    public final L1Apprentice getApprentice() {
        return _apprentice;
    }

    public final void setApprentice(L1Apprentice apprentice) {
        _apprentice = apprentice;
    }

    public final void checkEffect() {
        int checkType = 0;
        if (getApprentice() != null) {
            L1PcInstance master = World.get().getPlayer(getApprentice().getMaster().getName());
            if (master != null) {
                L1Party party = getParty();
                if (party != null) {
                    checkType = party.checkMentor(getApprentice());
                } else {
                    checkType = 1;
                }
            }
        }
        if (_tempType != checkType) {
            if (checkType > 0) {
                sendEffectBuff(_tempType, -1);
            }
            if (checkType > 0) {
                sendEffectBuff(checkType, 1);
            }
            sendPackets(new S_SPMR(this));
            sendPackets(new S_OwnCharStatus(this));
            sendPackets(new S_PacketBox(132, getEr()));
            if (checkType <= 0) {
                sendPackets(new S_PacketBox(147, 0, Math.max(_tempType - 1, 0)));
            } else {
                sendPackets(new S_PacketBox(147, (checkType != 0) ? 1 : 0, Math.max(checkType - 1, 0)));
            }
            _tempType = checkType;
        }
    }

    public void addOriginalEr(int i) {
        _originalEr += i;
    }

    private void sendEffectBuff(int buffType, int negative) {
        switch (buffType) {
            case 1: {
                addAc(-1 * negative);
                break;
            }
            case 2: {
                addAc(-1 * negative);
                addMr(1 * negative);
                break;
            }
            case 3: {
                addAc(-1 * negative);
                addMr(1 * negative);
                addWater(2 * negative);
                addWind(2 * negative);
                addFire(2 * negative);
                addEarth(2 * negative);
                break;
            }
            case 4: {
                addAc(-1 * negative);
                addMr(1 * negative);
                addWater(2 * negative);
                addWind(2 * negative);
                addFire(2 * negative);
                addEarth(2 * negative);
                addOriginalEr(1 * negative);
                break;
            }
            case 5: {
                addAc(-3 * negative);
                break;
            }
            case 6: {
                addAc(-3 * negative);
                addMr(3 * negative);
                break;
            }
            case 7: {
                addAc(-3 * negative);
                addMr(3 * negative);
                addWater(6 * negative);
                addWind(6 * negative);
                addFire(6 * negative);
                addEarth(6 * negative);
                break;
            }
            case 8: {
                addAc(-3 * negative);
                addMr(3 * negative);
                addWater(6 * negative);
                addWind(6 * negative);
                addFire(6 * negative);
                addEarth(6 * negative);
                addOriginalEr(3 * negative);
                break;
            }
        }
    }

    public final Timestamp getPunishTime() {
        return _punishTime;
    }

    public final void setPunishTime(Timestamp timestamp) {
        _punishTime = timestamp;
    }

    @Override
    public final String getViewName() {
        StringBuffer sbr = new StringBuffer();
        if (isProtector()) {
            sbr.append("**守護者**");
        } else if (hasSkillEffect(51234)) {
            sbr.append("尿尿商人");
        } else {
            sbr.append(getName());
            String getPrestigeLv = "";
            if (prestigtable.START && getPrestige() > 0) {
                getPrestigeLv = RewardPrestigeTable.get().getTitle(getPrestigeLv());
                sbr.append(getPrestigeLv);
            }
            if (_meteAbility != null) {
                sbr.append(_meteAbility.getTitle());
            }
            if (getvipname() != null) {
                sbr.append(getvipname());
            }
        }
        return sbr.toString();
    }

    public int getMagicDmgModifier() {
        return _magicDmgModifier;
    }

    public void addMagicDmgModifier(int i) {
        _magicDmgModifier += i;
    }

    public int getMagicDmgReduction() {
        return _magicDmgReduction;
    }

    public void addMagicDmgReduction(int i) {
        _magicDmgReduction += i;
    }

    public void set_elitePlateMail_Fafurion(int r, int hpmin, int hpmax) {
        _elitePlateMail_Fafurion = r;
        _fafurion_hpmin = hpmin;
        _fafurion_hpmax = hpmax;
    }

    public void set_elitePlateMail_Lindvior(int r, int mpmin, int mpmax) {
        _elitePlateMail_Lindvior = r;
        _lindvior_mpmin = mpmin;
        _lindvior_mpmax = mpmax;
    }

    public void set_elitePlateMail_Valakas(int r, int dmgmin, int dmgmax) {
        _elitePlateMail_Valakas = r;
        _valakas_dmgmin = dmgmin;
        _valakas_dmgmax = dmgmax;
    }

    public void set_hades_cloak(int r, int dmgmin, int dmgmax) {
        _hades_cloak = r;
        _hades_cloak_dmgmin = dmgmin;
        _hades_cloak_dmgmax = dmgmax;
    }

    public void set_Hexagram_Magic_Rune(int r, int hpmin, int hpmax, int gfx) {
        _Hexagram_Magic_Rune = r;
        _hexagram_hpmin = hpmin;
        _hexagram_hpmax = hpmax;
        _hexagram_gfx = gfx;
    }

    public void set_DimiterBless(int r, int mpmin, int mpmax, int r2, int time) {
        _dimiter_mpr_rnd = r;
        _dimiter_mpmin = mpmin;
        _dimiter_mpmax = mpmax;
        _dimiter_bless = r2;
        _dimiter_time = time;
    }

    public int getExpPoint() {
        return _expPoint;
    }

    public void setExpPoint(int i) {
        _expPoint = i;
    }

    public int getSummonId() {
        return _SummonId;
    }

    public void setSummonId(int SummonId) {
        _SummonId = SummonId;
    }

    public int getLap() {
        return _lap;
    }

    public void setLap(int i) {
        _lap = i;
    }

    public int getLapCheck() {
        return _lapCheck;
    }

    public void setLapCheck(int i) {
        _lapCheck = i;
    }

    public int getLapScore() {
        return _lap * 29 + _lapCheck;
    }

    public boolean isInOrderList() {
        return _order_list;
    }

    public void setInOrderList(boolean bool) {
        _order_list = bool;
    }

    public int get_bighot() {
        return _isBigHot;
    }

    public void set_bighot(int i) {
        _isBigHot = i;
    }

    public String getBighot1() {
        return _bighot1;
    }

    public void setBighot1(String bighot1) {
        _bighot1 = bighot1;
    }

    public String getBighot2() {
        return _bighot2;
    }

    public void setBighot2(String bighot2) {
        _bighot2 = bighot2;
    }

    public String getBighot3() {
        return _bighot3;
    }

    public void setBighot3(String bighot3) {
        _bighot3 = bighot3;
    }

    public String getBighot4() {
        return _bighot4;
    }

    public void setBighot4(String bighot4) {
        _bighot4 = bighot4;
    }

    public String getBighot5() {
        return _bighot5;
    }

    public void setBighot5(String bighot5) {
        _bighot5 = bighot5;
    }

    public String getBighot6() {
        return _bighot6;
    }

    public void setBighot6(String bighot6) {
        _bighot6 = bighot6;
    }

    public boolean isWindShackle() {
        return hasSkillEffect(167);
    }

    private void delSkill(int count) {
        int i = 0;
        while (i < count) {
            int index = L1PcInstance._random.nextInt(_skillList.size());
            Integer skillid = _skillList.get(index);
            L1Skills skill = SkillsTable.get().getTemplate(skillid.intValue());
            if (_skillList.remove(skillid)) {
                sendPackets(new S_DelSkill(this, skillid.intValue()));
                CharSkillReading.get().spellLost(getId(), skillid.intValue());
                World.get().broadcastPacketToAll(
                        new S_SystemMessage(String.valueOf(getName()) + " 的魔法【" + skill.getName() + "】 噴了。"));
            }
            ++i;
        }
    }

    private void checkItemSteal(L1PcInstance fightPc) {
        if (Taketreasure.getInstance().getList().isEmpty()) {
            return;
        }
        Iterator<L1Taketreasure> iterator = Taketreasure.getInstance().getList().iterator();
        while (iterator.hasNext()) {
            L1Taketreasure itemSteal = iterator.next();
            L1ItemInstance steal_item = getInventory().findItemId(itemSteal.getItemId());
            if (steal_item == null) {
                continue;
            }
            if (getLevel() < itemSteal.getLevel()) {
                continue;
            }
            if (getMeteLevel() < itemSteal.getMeteLevel()) {
                continue;
            }
            if (L1PcInstance._random.nextInt(100) + 1 >= itemSteal.getStealChance()) {
                continue;
            }
            if (itemSteal.getAntiStealItemId() <= 0
                    || !getInventory().consumeItem(itemSteal.getAntiStealItemId(), 1L)) {
                long steal_count;
                if (steal_item.isStackable()) {
                    steal_count = L1PcInstance._random
                            .nextInt(Math.max(itemSteal.getMaxStealCount() - itemSteal.getMinStealCount(), 0) + 1)
                            + itemSteal.getMinStealCount();
                    steal_count = ((steal_item.getCount() >= steal_count) ? steal_count : steal_item.getCount());
                } else {
                    steal_count = 1L;
                    getInventory().setEquipped(steal_item, false);
                }
                sendPackets(new S_ServerMessage(638, steal_item.getNumberedViewName(steal_count)));
                if (itemSteal.isDropOnFloor()) {
                    steal_item.set_showId(get_showId());
                    getInventory().tradeItem(steal_item, steal_count,
                            World.get().getInventory(getX(), getY(), getMapId()));
                    //Kevin奪寶系統廣播
                    if (itemSteal.isBroadcast()) {
                        World.get().broadcastPacketToAll(new S_SystemMessage("玩家[" + getViewName() + "]死亡, 似乎有東西["
                                + steal_item.getNumberedViewName(steal_count) + "]掉在地上了"));
                    }
                } else {
                    getInventory().tradeItem(steal_item, steal_count, fightPc.getInventory());
                    fightPc.sendPackets(new S_ServerMessage(403, steal_item.getNumberedViewName(steal_count)));
                    if (itemSteal.isBroadcast()) {
                        World.get().broadcastPacketToAll(
                                new S_SystemMessage(String.format(itemSteal.getdropmsg(), getViewName(),
                                        fightPc.getViewName(), steal_item.getNumberedViewName(steal_count))));
                    }
                }
                return;
            }
            sendPackets(new S_SystemMessage(
                    "由於身上有[" + ItemTable.get().getTemplate(itemSteal.getAntiStealItemId()).getNameId() + "] 免於被對方奪取: "
                            + steal_item.getLogName()));
        }
        return;

    }

    public boolean isATeam() {
        return _isATeam;
    }

    public void setATeam(boolean bool) {
        _isATeam = bool;
    }

    public boolean isBTeam() {
        return _isBTeam;
    }

    public void setBTeam(boolean bool) {
        _isBTeam = bool;
    }

    public Timestamp getRejoinClanTime() {
        return _rejoinClanTime;
    }

    public void setRejoinClanTime(Timestamp time) {
        _rejoinClanTime = time;
    }

    public Timestamp getCreateTime() {
        return _CreateTime;
    }

    public void setCreateTime(Timestamp time) {
        _CreateTime = time;
    }

    public int getSimpleCreateTime() {
        if (_CreateTime != null) {
            SimpleDateFormat SimpleDate = new SimpleDateFormat("yyyyMMdd");
            int BornTime = Integer.parseInt(SimpleDate.format(Long.valueOf(_CreateTime.getTime())));
            return BornTime;
        }
        return 0;
    }

    public void setCreateTime() {
        _CreateTime = new Timestamp(System.currentTimeMillis());
    }

    public int getPartyType() {
        return _partyType;
    }

    public void setPartyType(int type) {
        _partyType = type;
    }

    public int getUbScore() {
        return _ubscore;
    }

    public void setUbScore(int i) {
        _ubscore = i;
    }

    public int getInputError() {
        return _inputerror;
    }

    public void setInputError(int i) {
        _inputerror = i;
    }

    public int getSpeedError() {
        return _speederror;
    }

    public void setSpeedError(int i) {
        _speederror = i;
    }

    public int getBanError() {
        return _banerror;
    }

    public void setBanError(int i) {
        _banerror = i;
    }

    public int getInputBanError() {
        return _inputbanerror;
    }

    public void setInputBanError(int i) {
        _inputbanerror = i;
    }

    public AcceleratorChecker getAcceleratorChecker() {
        return _acceleratorChecker;
    }

    public int getSlot() {
        return _Slot;
    }

    public void setSlot(int i) {
        _Slot = i;
    }

    public boolean isItemPoly() {
        return _itempoly;
    }

    public void setItemPoly(boolean itempoly) {
        _itempoly = itempoly;
    }

    public boolean isItemPoly1() {
        return _itempoly1;
    }

    public void setItemPoly1(boolean itempoly1) {
        _itempoly1 = itempoly1;
    }

    public L1ItemInstance getPolyScroll() {
        return _polyscroll;
    }

    public void setPolyScroll(L1ItemInstance item) {
        _polyscroll = item;
    }

    public L1ItemInstance getPolyScrol2() {
        return _polyscrol2;
    }

    public void setPolyScrol2(L1ItemInstance item) {
        _polyscrol2 = item;
    }

    public void setitembox(L1ItemInstance item) {
        _itembox = item;
    }

    public L1ItemInstance getitembox() {
        return _itembox;
    }

    public L1EffectInstance get_tomb() {
        return _tomb;
    }

    public void set_tomb(L1EffectInstance tomb) {
        _tomb = tomb;
    }

    public boolean isMagicCritical() {
        return _isMagicCritical;
    }

    public void setMagicCritical(boolean flag) {
        _isMagicCritical = flag;
    }

    public boolean isPhantomTeleport() {
        return _isPhantomTeleport;
    }

    public void setPhantomTeleport(boolean flag) {
        _isPhantomTeleport = flag;
    }

    public int getRocksPrisonTime() {
        return _rocksPrisonTime;
    }

    public void setRocksPrisonTime(int time) {
        _rocksPrisonTime = time;
    }

    public int getLastabardTime() {
        return _lastabardTime;
    }

    public void setLastabardTime(int time) {
        _lastabardTime = time;
    }

    public int getIvoryTowerTime() {
        return _ivorytowerTime;
    }

    public void setIvoryTowerTime(int time) {
        _ivorytowerTime = time;
    }

    public int getDragonValleyTime() {
        return _dragonvalleyTime;
    }

    public void setDragonValleyTime(int time) {
        _dragonvalleyTime = time;
    }

    public void resetAllMapTime() {
        _rocksPrisonTime = 0;
        _lastabardTime = 0;
        _ivorytowerTime = 0;
        _dragonvalleyTime = 0;
    }

    public int getMapUseTime(int mapid) {
        int result = 0;
        switch (mapid) {
            case 53:
            case 54:
            case 55:
            case 56:
            case 807:
            case 808:
            case 809:
            case 810:
            case 811:
            case 812:
            case 813: {
                result = _rocksPrisonTime;
                break;
            }
            case 75:
            case 76:
            case 77:
            case 78:
            case 79:
            case 80:
            case 81:
            case 82: {
                result = _ivorytowerTime;
                break;
            }
            case 452:
            case 453:
            case 461:
            case 462:
            case 471:
            case 475:
            case 479:
            case 492:
            case 495: {
                result = _lastabardTime;
                break;
            }
            case 30:
            case 31:
            case 32:
            case 33:
            case 35:
            case 36:
            case 37: {
                result = _dragonvalleyTime;
                break;
            }
        }
        return result;
    }

    public void setMapUseTime(int mapid, int time) {
        switch (mapid) {
            case 53:
            case 54:
            case 55:
            case 56:
            case 807:
            case 808:
            case 809:
            case 810:
            case 811:
            case 812:
            case 813: {
                setRocksPrisonTime(time);
                break;
            }
            case 75:
            case 76:
            case 77:
            case 78:
            case 79:
            case 80:
            case 81:
            case 82: {
                setIvoryTowerTime(time);
                break;
            }
            case 452:
            case 453:
            case 461:
            case 462:
            case 471:
            case 475:
            case 479:
            case 492:
            case 495: {
                setLastabardTime(time);
                break;
            }
            case 30:
            case 31:
            case 32:
            case 33:
            case 35:
            case 36:
            case 37: {
                setDragonValleyTime(time);
                break;
            }
        }
    }

    public boolean isInTimeMap() {
        int map = getMapId();
        int maxMapUsetime = MapsTable.get().getMapTime(map);
        return maxMapUsetime > 0;
    }

    public void updateMapTime(int time) {
        int mapid = getMapId();
        if (mapid >= 4001 && mapid <= 4050) {
            mapid = 4001;
        }
        if (mapTime.get(Integer.valueOf(mapid)) == null) {
            return;
        }
        int temp = mapTime.get(Integer.valueOf(mapid)).intValue();
        mapTime.put(Integer.valueOf(getMapId()), Integer.valueOf(temp + time));
    }

    public int getMapTime(int mapid) {
        if (mapTime.get(Integer.valueOf(mapid)) == null) {
            L1PcInstance._log.error("記時地圖ID:" + mapid + "不存在");
            return -1;
        }
        return mapTime.get(Integer.valueOf(mapid)).intValue();
    }

    public ConcurrentHashMap<Integer, Integer> getMapTime() {
        return mapTime;
    }

    public void setMapTime(ConcurrentHashMap<Integer, Integer> map) {
        mapTime = map;
    }

    public boolean isTimeMap() {
        return isTimeMap;
    }

    public void setTimeMap(boolean isTimeMap) {
        this.isTimeMap = isTimeMap;
    }

    public void stopTimeMap() {
        if (isTimeMap) {
            isTimeMap = false;
        }
    }

    public void startTimeMap() {
        if (!isTimeMap) {
            isTimeMap = true;
        }
    }

    public int getClanMemberId() {
        return _clanMemberId;
    }

    public void setClanMemberId(int i) {
        _clanMemberId = i;
    }

    public String getClanMemberNotes() {
        return _clanMemberNotes;
    }

    public void setClanMemberNotes(String s) {
        _clanMemberNotes = s;
    }

    public void addStunLevel(int add) {
        _stunlevel += add;
    }

    public int getStunLevel() {
        return _stunlevel;
    }

    public int getother_ReductionDmg() {
        return _other_ReductionDmg;
    }

    public void setother_ReductionDmg(int i) {
        _other_ReductionDmg = i;
    }

    public void addother_ReductionDmg(int i) {
        _other_ReductionDmg += i;
    }

    public int getClan_ReductionDmg() {
        return _Clan_ReductionDmg;
    }

    public void setClan_ReductionDmg(int i) {
        _Clan_ReductionDmg = i;
    }

    public void addClan_ReductionDmg(int i) {
        _Clan_ReductionDmg += i;
    }

    public void add_Clanmagic_reduction_dmg(int add) {
        _Clanmagic_reduction_dmg += add;
    }

    public int get_Clanmagic_reduction_dmg() {
        return _Clanmagic_reduction_dmg;
    }

    public void addExpByArmor(double i) {
        _addExpByArmor += i;
    }

    public double getExpByArmor() {
        return _addExpByArmor;
    }

    public int getPcContribution() {
        return _PcContribution;
    }

    public void setPcContribution(int i) {
        _PcContribution = i;
    }

    public int getClanContribution() {
        return _clanContribution;
    }

    public void setClanContribution(int i) {
        _clanContribution = i;
    }

    public int getclanadena() {
        return _clanadena;
    }

    public void setclanadena(int i) {
        _clanadena = i;
    }

    public String getClanNameContribution() {
        return clanNameContribution;
    }

    public void setClanNameContribution(String s) {
        clanNameContribution = s;
    }

    public void setcheckgm(boolean checkgm) {
        _checkgm = checkgm;
    }

    public boolean getcheckgm() {
        return _checkgm;
    }

    public void setcheck_lv(boolean b) {
        check_lv = b;
    }

    public boolean getcheck_lv() {
        return check_lv;
    }

    public int getlogpcpower_SkillCount() {
        return _logpcpower_SkillCount;
    }

    public void setlogpcpower_SkillCount(int i) {
        _logpcpower_SkillCount = i;
    }

    public int getlogpcpower_SkillFor1() {
        return _logpcpower_SkillFor1;
    }

    public void setlogpcpower_SkillFor1(int i) {
        _logpcpower_SkillFor1 = i;
    }

    public int getlogpcpower_SkillFor2() {
        return _logpcpower_SkillFor2;
    }

    public void setlogpcpower_SkillFor2(int i) {
        _logpcpower_SkillFor2 = i;
    }

    public int getlogpcpower_SkillFor3() {
        return _logpcpower_SkillFor3;
    }

    public void setlogpcpower_SkillFor3(int i) {
        _logpcpower_SkillFor3 = i;
    }

    public int getlogpcpower_SkillFor4() {
        return _logpcpower_SkillFor4;
    }

    public void setlogpcpower_SkillFor4(int i) {
        _logpcpower_SkillFor4 = i;
    }

    public int getlogpcpower_SkillFor5() {
        return _logpcpower_SkillFor5;
    }

    public void setlogpcpower_SkillFor5(int i) {
        _logpcpower_SkillFor5 = i;
    }

    public int getEsotericSkill() {
        return _EsotericSkill;
    }

    public void setEsotericSkill(int i) {
        _EsotericSkill = i;
    }

    public int getEsotericCount() {
        return _EsotericCount;
    }

    public void setEsotericCount(int i) {
        _EsotericCount = i;
    }

    public boolean isEsoteric() {
        return _isEsoteric;
    }

    public void setEsoteric(boolean flag) {
        _isEsoteric = flag;
    }

    @Override
    public boolean isTripleArrow() {
        return _TripleArrow;
    }

    @Override
    public void setTripleArrow(boolean TripleArrow) {
        _TripleArrow = TripleArrow;
    }

    public void setchecklogpc(boolean checklogpc) {
        _checklogpc = checklogpc;
    }

    public boolean getchecklogpc() {
        return _checklogpc;
    }

    public int gesavepclog() {
        return _savepclog;
    }

    public void setsavepclog(int i) {
        _savepclog = i;
    }

    public int getReductionDmg() {
        return _ReductionDmg;
    }

    public void setReductionDmg(int i) {
        _ReductionDmg = i;
    }

    public int getpcdmg() {
        return _pcdmg;
    }

    public void setpcdmg(int i) {
        _pcdmg = i;
    }

    public int getpaycount() {
        return _paycount;
    }

    public void setpaycount(int i) {
        _paycount = i;
    }

    public int getArmorCount1() {
        return _ArmorCount1;
    }

    public void setArmorCount1(int i) {
        _ArmorCount1 = i;
    }

    public int getlogintime() {
        return _logintime;
    }

    public void setlogintime(int i) {
        _logintime = i;
    }

    public int getlogintime1() {
        return _logintime1;
    }

    public void setlogintime1(int i) {
        _logintime1 = i;
    }

    public double getPartyExp() {
        return _PartyExp;
    }

    public void setPartyExp(double d) {
        _PartyExp = d;
    }

    public boolean getATK_ai() {
        return ATK_ai;
    }

    public void setATK_ai(boolean b) {
        ATK_ai = b;
    }

    public final long getShopAdenaRecord() {
        return _shopAdenaRecord;
    }

    public final void setShopAdenaRecord(long i) {
        _shopAdenaRecord = i;
    }

    public int getdolldamageReductionByArmor() {
        int dolldamageReduction = 0;
        if (_dolldamageReductionByArmor > 10) {
            dolldamageReduction = 10 + (L1PcInstance._random.nextInt(_dolldamageReductionByArmor - 10) + 1);
        } else {
            dolldamageReduction = _dolldamageReductionByArmor;
        }
        return dolldamageReduction;
    }

    public void adddollDamageReductionByArmor(int i) {
        _dolldamageReductionByArmor += i;
    }

    public void addweaponMD(int weaponMD) {
        _weaponMD += weaponMD;
    }

    public int getweaponMD() {
        return _weaponMD;
    }

    public void addweaponMDC(int weaponMDC) {
        _weaponMDC += weaponMDC;
    }

    public int getweaponMDC() {
        return _weaponMDC;
    }

    public void add_reduction_dmg(int add) {
        _reduction_dmg += add;
    }

    public int get_reduction_dmg() {
        return _reduction_dmg;
    }

    public void addSkin(L1SkinInstance skin, int gfxid) {
        _skins.put(Integer.valueOf(gfxid), skin);
    }

    public void addGF(int i) {
        if (i > 0) {
            _GF = DoubleUtil.sum(_GF, i / 100.0);
        } else {
            _GF = DoubleUtil.sub(_GF, i * -1 / 100.0);
        }
    }

    public double getGF() {
        if (_GF < 0.0) {
            return 0.0;
        }
        return _GF;
    }

    public void removeSkin(int gfxid) {
        _skins.remove(Integer.valueOf(gfxid));
    }

    public L1SkinInstance getSkin(int gfxid) {
        return _skins.get(Integer.valueOf(gfxid));
    }

    public Map<Integer, L1SkinInstance> getSkins() {
        return _skins;
    }

    public boolean get_isTeleportToOk() {
        return _isTeleportToOk;
    }

    public void set_isTeleportToOk(boolean b) {
        _isTeleportToOk = b;
    }

    public boolean get_MOVE_STOP() {
        return _MOVE_STOP;
    }

    public void set_MOVE_STOP(boolean b) {
        _MOVE_STOP = b;
    }

    public void sendPacketsBossWeaponAll(ServerBasePacket packet) {
        if (_out == null) {
            return;
        }
        try {
            _out.encrypt(packet);
            if (!isGmInvis() && !isInvisble()) {
                broadcastPacketBossWeaponAll(packet);
            }
        } catch (Exception e) {
            logout();
            close();
        }
    }

    public String getIp() {
        return _netConnection.getIp().toString();
    }

    public int getAmount() {
        return _amount;
    }

    public void setAmount(int i) {
        _amount = i;
    }

    public synchronized void setExp_Direct(long i) {
        setExp(i);
        onChangeExp();
    }

    public L1Inventory getTradeWindowInventory() {
        return _tradewindow;
    }

    public long get_consume_point() {
        return _consume_point;
    }

    public void set_consume_point(long count) {
        _consume_point = count;
    }

    public final void setMapsList(HashMap<Integer, Integer> list) {
        _mapsList = list;
    }

    public final int getMapsTime(int key) {
        if (_mapsList == null || !_mapsList.containsKey(Integer.valueOf(key))) {
            return 0;
        }
        return _mapsList.get(Integer.valueOf(key)).intValue();
    }

    public void putMapsTime(int key, int value) {
        if (_mapsList == null) {
            _mapsList = CharMapTimeReading.get().addTime(getId(), key, value);
        }
        _mapsList.put(Integer.valueOf(key), Integer.valueOf(value));
    }

    public int getTempStr() {
        return _tempStr;
    }

    public void setTempStr(int i) {
        _tempStr = i;
    }

    public int getTempDex() {
        return _tempDex;
    }

    public void setTempDex(int i) {
        _tempDex = i;
    }

    public int getTempCon() {
        return _tempCon;
    }

    public void setTempCon(int i) {
        _tempCon = i;
    }

    public int getTempWis() {
        return _tempWis;
    }

    public void setTempWis(int i) {
        _tempWis = i;
    }

    public int getTempCha() {
        return _tempCha;
    }

    public void setTempCha(int i) {
        _tempCha = i;
    }

    public int getTempInt() {
        return _tempInt;
    }

    public void setTempInt(int i) {
        _tempInt = i;
    }

    public int getTempInitPoint() {
        return _tempInitPoint;
    }

    public void setTempInitPoint(int i) {
        _tempInitPoint = i;
    }

    public int getTempElixirstats() {
        return _tempElixirstats;
    }

    public void setTempElixirstats(int i) {
        _tempElixirstats = i;
    }

    public int getweapondmg() {
        return weapondmg;
    }

    public void setweapondmg(int i) {
        weapondmg = i;
    }

    public int getDmgdouble() {
        return Dmgdouble;
    }

    public void setDmgdouble(double Dmgdouble) {
        Dmgdouble += Dmgdouble;
    }

    public int[] getReward_Weapon() {
        return _reward_Weapon;
    }

    public void setReward_Weapon(int[] i) {
        _reward_Weapon = i;
    }

    public int getelfweapon() {
        return elfweapon;
    }

    public void setelfweapon(int i) {
        elfweapon = i;
    }

    public int getPVPdmg() {
        return _PVPdmg;
    }

    public void setPVPdmg(int i) {
        _PVPdmg = i;
    }

    public void addPVPdmg(int i) {
        _PVPdmg += i;
    }

    public int getPVPdmgReduction() {
        return _PVPdmgReduction;
    }

    public void setPVPdmgReduction(int i) {
        _PVPdmgReduction = i;
    }

    public void addPVPdmgReduction(int i) {
        _PVPdmgReduction += i;
    }

    public int getattr_potion_heal() {
        return _attr_potion_heal;
    }

    public void setattr_potion_heal(int i) {
        _attr_potion_heal = i;
    }

    public void addattr_potion_heal(int i) {
        _attr_potion_heal += i;
    }

    public int getpenetrate() {
        return _penetrate;
    }

    public void setpenetrate(int i) {
        _penetrate = i;
    }

    public int getattr_物理格檔() {
        return _attr_物理格檔;
    }

    public void setattr_物理格檔(int i) {
        _attr_物理格檔 = i;
    }

    public void addattr_物理格檔(int i) {
        _attr_物理格檔 += i;
    }

    public int getattr_魔法格檔() {
        return _attr_魔法格檔;
    }

    public void setattr_魔法格檔(int i) {
        _attr_魔法格檔 = i;
    }

    public void addattr_魔法格檔(int i) {
        _attr_魔法格檔 += i;
    }

    public int getNoweaponRedmg() {
        return _NoweaponRedmg;
    }

    public void setNoweaponRedmg(int i) {
        _NoweaponRedmg = i;
    }

    public int getaddStunLevel() {
        return _addStunLevel;
    }

    public void setaddStunLevel(int i) {
        _addStunLevel = i;
    }

    public void addaddStunLevel(int add) {
        _addStunLevel += add;
    }

    public int getloginpoly() {
        return _loginpoly;
    }

    public void setloginpoly(int i) {
        _loginpoly = i;
    }

    public int getBackHeading() {
        return backHeading;
    }

    public void setBackHeading(int backHeading) {
        this.backHeading = backHeading;
    }

    public int getBackX() {
        return backX;
    }

    public void setBackX(int backX) {
        this.backX = backX;
    }

    public int getBackY() {
        return backY;
    }

    public void setBackY(int backY) {
        this.backY = backY;
    }

    public boolean isAI() {
        return _isAI;
    }

    public void setAI(boolean flag) {
        _isAI = flag;
    }

    public int getAi_Number() {
        return _ai_number;
    }

    public void setAi_Number(int flag) {
        _ai_number = flag;
    }

    public int getAi_Count() {
        return _ai_count;
    }

    public void setAi_Count(int i) {
        _ai_count = i;
    }

    public void addAi_Count(int i) {
        _ai_count += i;
    }

    public int getAi_error() {
        return _ai_error;
    }

    public void setAi_error(int i) {
        _ai_error = i;
    }

    public void addAi_error(int i) {
        _ai_error += i;
    }

    public int getAi_correct() {
        return _ai_correct;
    }

    public void setAi_correct(int i) {
        _ai_correct = i;
    }

    public void addAi_correct(int i) {
        _ai_correct += i;
    }

    public void set_Imperius_Tshirt(int r, int drainingHP_min, int drainingHP_max) {
        _Imperius_Tshirt_rnd = r;
        _drainingHP_min = drainingHP_min;
        _drainingHP_max = drainingHP_max;
    }

    public int get_Imperius_Tshirt_rnd() {
        return _Imperius_Tshirt_rnd;
    }

    public int get_Tshirt_drainingHP_min() {
        return _drainingHP_min;
    }

    public int get_Tshirt_drainingHP_max() {
        return _drainingHP_max;
    }

    public void set_MoonAmulet(int r, int dmgmin, int dmgmax, int gfxid) {
        _MoonAmulet_rnd = r;
        _MoonAmulet_dmg_min = dmgmin;
        _MoonAmulet_dmg_max = dmgmax;
        _MoonAmulet_gfxid = gfxid;
    }

    public int get_MoonAmulet_rnd() {
        return _MoonAmulet_rnd;
    }

    public int get_MoonAmulet_dmg_min() {
        return _MoonAmulet_dmg_min;
    }

    public int get_MoonAmulet_dmg_max() {
        return _MoonAmulet_dmg_max;
    }

    public int get_MoonAmulet_gfxid() {
        return _MoonAmulet_gfxid;
    }

    public void set_AttrAmulet(int r, int dmg, int gfxid) {
        _AttrAmulet_rnd = r;
        _AttrAmulet_dmg = dmg;
        _AttrAmulet_gfxid = gfxid;
    }

    public int get_AttrAmulet_rnd() {
        return _AttrAmulet_rnd;
    }

    public int get_AttrAmulet_dmg() {
        return _AttrAmulet_dmg;
    }

    public int get_AttrAmulet_gfxid() {
        return _AttrAmulet_gfxid;
    }

    public int getRange() {
        return _range;
    }

    public void setRange(int i) {
        _range = i;
    }

    public PcAttackThread getAttackThread() {
        return attackThread;
    }

    public void setAttackThread(PcAttackThread attackThread) {
        this.attackThread = attackThread;
    }

    public int getday() {
        return _day;
    }

    public void setday(int i) {
        _day = i;
    }

    public void addPrestige(int i) {
        _prestige += i;
        _prestige = Math.max(_prestige, 0);
        if (_prestigeLv != RewardPrestigeTable.get().getLv(_prestige)) {
            if (_prestigeLv != 0) {
                RewardPrestigeTable.get().removePrestige(this);
            }
            _prestigeLv = RewardPrestigeTable.get().getLv(_prestige);
            if (_prestigeLv != 0) {
                RewardPrestigeTable.get().addPrestige(this);
            }
            L1Teleport.teleport(this, getX(), getY(), getMapId(), getHeading(), false);
        }
    }

    public int getPrestige() {
        return _prestige;
    }

    public void setPrestige(int i) {
        _prestige = i;
        if (RewardPrestigeTable.START && _prestige == 0) {
            _prestigeLv = RewardPrestigeTable.get().getLv(_prestige);
            sendPackets(new S_ServerMessage("\\fU你身上帶有,陣營積分受到守護!"));
        } else if (RewardPrestigeTable.START && _prestige != 0) {
            _prestigeLv = RewardPrestigeTable.get().getLv(_prestige);
        }
    }

    public int getPrestigeLv() {
        return _prestigeLv;
    }

    public void setgo_guajitele(boolean go_guajitele) {
        _go_guajitele = go_guajitele;
    }

    public boolean getgo_guajitele() {
        return _go_guajitele;
    }

    public long getoldexp() {
        return _oldexp;
    }

    public void setoldexp(long oldexp) {
        _oldexp = oldexp;
    }

    public boolean isItemName() {
        return _isItemName;
    }

    public void setItemName(boolean flag) {
        _isItemName = flag;
    }

    public boolean isItemopen() {
        return _isItemopen;
    }

    public void setItemopen(boolean flag) {
        _isItemopen = flag;
    }

    public boolean isfollow() {
        return _isfollow;
    }

    public void setfollow(boolean flag) {
        _isfollow = flag;
    }

    public boolean isfollowcheck() {
        return _isfollowcheck;
    }

    public void setfollowcheck(boolean flag) {
        _isfollowcheck = flag;
    }

    @Override
    public int get_poisonStatus2() {
        return _poisonStatus2;
    }

    @Override
    public void set_poisonStatus2(int i) {
        _poisonStatus2 = i;
    }

    @Override
    public int get_poisonStatus7() {
        return _poisonStatus7;
    }

    @Override
    public void set_poisonStatus7(int i) {
        _poisonStatus7 = i;
    }

    public final boolean _isCraftsmanHeirloom() {
        return _isCraftsmanHeirloom;
    }

    public final void setCraftsmanHeirloom(boolean checkFlag) {
        if (_isCraftsmanHeirloom != checkFlag) {
            giveCraftsmanHeirloom(checkFlag);
            sendPackets(new S_HPUpdate(this));
            if (isInParty()) {
                getParty().updateMiniHP(this);
            }
            sendPackets(new S_MPUpdate(this));
            sendPackets(new S_SPMR(this));
            L1PcUnlock.Pc_Unlock(this);
        }
    }

    public final void giveCraftsmanHeirloom(boolean checkFlag) {
        _isCraftsmanHeirloom = checkFlag;
        if (checkFlag) {
            addMaxHp(120);
            addMaxMp(100);
            addDmgup(50);
            addSp(15);
            addDamageReductionByArmor(30);
            sendPackets(new S_PacketBox(180, 1, 460));
        } else {
            addMaxHp(-120);
            addMaxMp(-100);
            addDmgup(-50);
            addSp(-15);
            addDamageReductionByArmor(-30);
            sendPackets(new S_PacketBox(180, 0, 460));
        }
    }

    public final boolean _isMarsSoul() {
        return _isMarsSoul;
    }

    public final void setMarsSoul(boolean checkFlag) {
        if (_isMarsSoul != checkFlag) {
            giveMarsSoul(checkFlag);
            sendPackets(new S_HPUpdate(this));
            if (isInParty()) {
                getParty().updateMiniHP(this);
            }
            sendPackets(new S_MPUpdate(this));
            sendPackets(new S_SPMR(this));
            L1PcUnlock.Pc_Unlock(this);
        }
    }

    public final void giveMarsSoul(boolean checkFlag) {
        _isMarsSoul = checkFlag;
        if (checkFlag) {
            addMaxHp(120);
            addMaxMp(100);
            addDmgup(15);
            addBowDmgup(15);
            addSp(5);
            addDamageReductionByArmor(8);
            sendPackets(new S_PacketBox(180, 1, 457));
        } else {
            addMaxHp(-120);
            addMaxMp(-100);
            addDmgup(-15);
            addBowDmgup(-15);
            addSp(-5);
            addDamageReductionByArmor(-8);
            sendPackets(new S_PacketBox(180, 0, 457));
        }
    }

    public int getSuper() {
        return _super;
    }

    public void setSuper(int i) {
        _super = i;
    }

    public void setguaji_poly(int b) {
        guaji_poly = b;
    }

    public int getguaji_poly() {
        return guaji_poly;
    }

    public int getIceTime() {
        return _iceTime;
    }

    public void setIceTime(int time) {
        _iceTime = time;
    }

    public void startAI() {
        if (isDead()) {
            return;
        }
        if (isGhost()) {
            return;
        }
        if (getCurrentHp() <= 0) {
            return;
        }
        if (isPrivateShop()) {
            return;
        }
        if (isParalyzed()) {
            return;
        }
        if (get_followmaster() == null) {
            _pcMove = new pcMove(this);
        }
        setAiRunning(true);
        setActived(true);
        PcAI npcai = new PcAI(this);
        npcai.startAI();
    }

    protected boolean isAiRunning() {
        return _aiRunning;
    }

    protected void setAiRunning(boolean aiRunning) {
        _aiRunning = aiRunning;
    }

    public void allTargetClear() {
        if (_pcMove != null) {
            _pcMove.clear();
        }
        _hateList.clear();
        _target = null;
        setFirstAttack(false);
    }

    public void checkTarget() {
        try {
            if (_target == null) {
                allTargetClear();
                return;
            }
            if (_target.getMapId() != getMapId()) {
                allTargetClear();
                return;
            }
            if (_target.getCurrentHp() <= 0) {
                allTargetClear();
                return;
            }
            if (_target.isDead()) {
                allTargetClear();
                return;
            }
            if (get_showId() != _target.get_showId()) {
                allTargetClear();
                return;
            }
            if (!_hateList.containsKey(_target)) {
                allTargetClear();
                return;
            }
            int distance = getLocation().getTileDistance(_target.getLocation());
            if (distance > 5) {
                allTargetClear();
                return;
            }
        } catch (Exception e) {
            return;
        }
    }

    public L1Character is_now_target() {
        return _target;
    }

    public void attackTarget(L1Character target) {
        if (getInventory().getWeight240() >= 197) {
            sendPackets(new S_ServerMessage(110));
            return;
        }
        if (hasSkillEffect(1011)) {
            return;
        }
        if (hasSkillEffect(1009)) {
            return;
        }
        if (hasSkillEffect(66)) {
            return;
        }
        if (hasSkillEffect(87)) {
            return;
        }
        if (hasSkillEffect(212)) {
            return;
        }
        if (hasSkillEffect(50)) {
            return;
        }
        if (hasSkillEffect(157)) {
            return;
        }
        if (hasSkillEffect(103)) {
            return;
        }
        if (hasSkillEffect(208)) {
            return;
        }
        if (target instanceof L1PcInstance) {
            L1PcInstance player = (L1PcInstance) target;
            if (player.isTeleport()) {
                return;
            }
            if (!player.isPinkName()) {
                allTargetClear();
                return;
            }
        } else if (target instanceof L1PetInstance) {
            L1PetInstance pet = (L1PetInstance) target;
            L1Character cha = pet.getMaster();
            if (cha instanceof L1PcInstance) {
                L1PcInstance player2 = (L1PcInstance) cha;
                if (player2.isTeleport()) {
                    return;
                }
            }
        } else if (target instanceof L1SummonInstance) {
            L1SummonInstance summon = (L1SummonInstance) target;
            L1Character cha = summon.getMaster();
            if (cha instanceof L1PcInstance) {
                L1PcInstance player2 = (L1PcInstance) cha;
                if (player2.isTeleport()) {
                    return;
                }
            }
        }
        if (target instanceof L1NpcInstance) {
            L1NpcInstance npc = (L1NpcInstance) target;
            if (npc.getHiddenStatus() != 0) {
                allTargetClear();
                return;
            }
        }
        if (target instanceof L1PetInstance) {
            allTargetClear();
            return;
        }
        if (target.getCurrentHp() > 0 && !target.isDead()) {
            target.onAction(this);
        }
        if (((get_followmaster() == null && !getfollowatkmagic())
                || (get_followmaster() != null && getfollowatkmagic()))
                && getCurrentMp() >= getMaxMp() * getma1() / 100
                && (!hasSkillEffect(1007) || !hasSkillEffect(64))) {
            SkillDmg(this, target);
        }
    }

    public void searchTarget() {
        Collection<L1Object> allObj = World.get().getVisibleObjects(this, 3);
        allObj = World.get().getVisibleObjects(this, 3);
        Iterator<L1Object> iter = allObj.iterator();
        while (iter.hasNext()) {
            L1Object obj = iter.next();
            if (!(obj instanceof L1MonsterInstance)) {
                continue;
            }
            L1MonsterInstance mob = (L1MonsterInstance) obj;
            if (mob.getNpcTemplate().is_boss() && get_followmaster() == null) {
                L1Teleport.randomTeleport(this, true);
                move = 0;
            } else {
                if (mob.getNpcTemplate().get_npcId() == 81257) {
                    continue;
                }
                if (mob.hasSkillEffect(33) || mob.hasSkillEffect(50) || mob.hasSkillEffect(1011)) {
                    continue;
                }
                if (mob.hasSkillEffect(1009)) {
                    continue;
                }
                if (mob.isDead()) {
                    continue;
                }
                if (mob.getCurrentHp() <= 0) {
                    continue;
                }
                if (mob.getHiddenStatus() > 0) {
                    continue;
                }
                if (mob.getAtkspeed() == 0) {
                    continue;
                }
                if (mob.hasSkillEffect(getId() + 100000) && !isAttackPosition(mob.getX(), mob.getY(), 1)) {
                    continue;
                }
                if (getmobatk() && mob.is_now_target() != null && mob.is_now_target() != this && mob
                        .isAttackPosition(mob.is_now_target().getX(), mob.is_now_target().getY(), mob.get_ranged())) {
                    continue;
                }
                if (mob == null) {
                    continue;
                }
                int Distance = 0;
                if (glanceCheck(mob.getX(), mob.getY())) {
                    Distance += 5;
                }
                _hateList.add(mob, Distance);
            }
        }
        Collection<L1Object> allObj2 = World.get().getVisibleObjects(this, 7);
        if (get_followmaster() != null) {
            allObj2 = World.get().getVisibleObjects(this, 3);
        } else {
            allObj2 = World.get().getVisibleObjects(this, 7);
        }
        Iterator<L1Object> iter2 = allObj2.iterator();
        while (iter2.hasNext()) {
            L1Object obj2 = iter2.next();
            if (!(obj2 instanceof L1MonsterInstance)) {
                continue;
            }
            L1MonsterInstance mob2 = (L1MonsterInstance) obj2;
            if (mob2.getNpcTemplate().is_boss() && get_followmaster() == null) {
                L1Teleport.randomTeleport(this, true);
                move = 0;
            } else {
                if (mob2.getNpcTemplate().get_npcId() == 81257) {
                    continue;
                }
                if (mob2.hasSkillEffect(33) || mob2.hasSkillEffect(50) || mob2.hasSkillEffect(1011)) {
                    continue;
                }
                if (mob2.hasSkillEffect(1009)) {
                    continue;
                }
                if (mob2.isDead()) {
                    continue;
                }
                if (mob2.getCurrentHp() <= 0) {
                    continue;
                }
                if (mob2.getHiddenStatus() > 0) {
                    continue;
                }
                if (mob2.getAtkspeed() == 0) {
                    continue;
                }
                if (mob2.hasSkillEffect(getId() + 100000) && !isAttackPosition(mob2.getX(), mob2.getY(), 1)) {
                    continue;
                }
                if (getmobatk() && mob2.is_now_target() != null && mob2.is_now_target() != this
                        && mob2.isAttackPosition(mob2.is_now_target().getX(), mob2.is_now_target().getY(),
                        mob2.get_ranged())) {
                    continue;
                }
                if (mob2 == null) {
                    continue;
                }
                int Distance2 = 0;
                if (glanceCheck(mob2.getX(), mob2.getY())) {
                    Distance2 += 5;
                }
                _hateList.add(mob2, Distance2);
            }
        }
        _target = _hateList.getMaxHateCharacter();
        if (_target == null && getgo_guajitele() && getlslocx() == 0 && getlslocy() == 0
                && !hasSkillEffect(8852) && get_followmaster() == null && getMap().isTeleportable()) {
            if (ConfigGuaji.Guaji_tele) {
                if (!hasSkillEffect(33) || !hasSkillEffect(4000) || !hasSkillEffect(192)
                        || !hasSkillEffect(50) || !hasSkillEffect(66) || !hasSkillEffect(87)
                        || !hasSkillEffect(4017) || !hasSkillEffect(192) || !hasSkillEffect(157)) {
                    L1Teleport.randomTeleportai(this);
                    move = 0;
                    setSkillEffect(8852, 2000);
                }
            } else if (!ConfigGuaji.Guaji_tele
                    && getInventory().checkItem(ConfigGuaji.Guaji_tele_item, ConfigGuaji.Guaji_tele_itemcount)
                    && (!hasSkillEffect(33) || !hasSkillEffect(4000) || !hasSkillEffect(192)
                    || !hasSkillEffect(50) || !hasSkillEffect(66) || !hasSkillEffect(87)
                    || !hasSkillEffect(4017) || !hasSkillEffect(192) || !hasSkillEffect(157))) {
                getInventory().consumeItem(ConfigGuaji.Guaji_tele_item, ConfigGuaji.Guaji_tele_itemcount);
                L1Teleport.randomTeleportai(this);
                move = 0;
                setSkillEffect(8852, 2000);
            }
        }
        allObj.clear();
        allObj2.clear();
    }

    public void targetClear() {
        if (_target == null) {
            return;
        }
        _hateList.remove(_target);
        _target = null;
    }

    public void onTarget() {
        try {
            L1Character target = _target;
            if (target == null) {
                return;
            }
            attack(target);
        } catch (Exception e) {
            L1PcInstance._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void attack(L1Character target) {
        int attack_Range = 1;
        if (getWeapon() != null) {
            if (get_followmaster() == null) {
                attack_Range = getWeapon().getItem().getRange();
            } else {
                attack_Range = 3;
            }
        }
        if (attack_Range < 0) {
            attack_Range = 9;
        }
        if (isAttackPosition(target.getX(), target.getY(), attack_Range)) {
            setHeading(targetDirection(target.getX(), target.getY()));
            attackTarget(target);
            move = 0;
            if (_pcMove != null) {
                _pcMove.clear();
            }
        } else if (_pcMove != null) {
            int dir = _pcMove.moveDirection(target.getX(), target.getY());
            if (dir == -1) {
                _target.setSkillEffect(getId() + 100000, 20000);
                allTargetClear();
            } else {
                _pcMove.setDirectionMove(dir);
                ++move;
            }
        }
    }

    public boolean isActived() {
        return _actived;
    }

    public void setActived(boolean actived) {
        _actived = actived;
    }

    protected boolean isFirstAttack() {
        return _firstAttack;
    }

    protected void setFirstAttack(boolean firstAttack) {
        _firstAttack = firstAttack;
    }

    public void setHate(L1Character cha, int hate) {
        try {
            if (cha != null && _target != null && !isFirstAttack() && hate > 0) {
                setFirstAttack(true);
                if (_pcMove != null) {
                    _pcMove.clear();
                }
                _hateList.add(cha, 5);
                _target = _hateList.getMaxHateCharacter();
                checkTarget();
            }
        } catch (Exception e) {
            return;
        }
    }

    public boolean isPathfinding() {
        return _Pathfinding;
    }

    public void setPathfinding(boolean fla) {
        _Pathfinding = fla;
    }

    public int getrandomMoveDirection() {
        return _randomMoveDirection;
    }

    public void setrandomMoveDirection(int randomMoveDirection) {
        _randomMoveDirection = randomMoveDirection;
    }

    public void noTarget() {
        if (hasSkillEffect(1011)) {
            return;
        }
        if (hasSkillEffect(1009)) {
            return;
        }
        if (hasSkillEffect(4000)) {
            return;
        }
        if (!_Pathfinding) {
            _Pathfinding = true;
        }
        if (_randomMoveDirection > 7) {
            _randomMoveDirection = 0;
        }
        if (_pcMove != null && getrandomMoveDirection() < 8) {
            int dir = _pcMove.checkObject(_randomMoveDirection);
            dir = _pcMove.openDoor(dir);
            if (dir != -1) {
                _pcMove.setDirectionMove(dir);
            } else {
                _randomMoveDirection = L1PcInstance._random.nextInt(8);
                ++move;
            }
        }
    }

    public int getguajiX() {
        return _tguajiX;
    }

    public void setguajiX(int i) {
        _tguajiX = i;
    }

    public int getguajiY() {
        return _tguajiY;
    }

    public void setguajiY(int i) {
        _tguajiY = i;
    }

    public int getguajiMapId() {
        return _guajiMapId;
    }

    public void setguajiMapId(int i) {
        _guajiMapId = i;
    }

    public void addArmorBreakLevel(int add) {
        _armorbreaklevel += add;
    }

    public int getArmorBreakLevel() {
        return _armorbreaklevel;
    }

    public int get_FoeSlayerBonusDmg() {
        return _FoeSlayerBonusDmg;
    }

    public void add_FoeSlayerBonusDmg(int Dmg) {
        _FoeSlayerBonusDmg += Dmg;
    }

    public void set_soulHp_val(int r, int hpmin, int hpmax) {
        _soulHp_r = r;
        _soulHp_hpmin = hpmin;
        _soulHp_hpmax = hpmax;
    }

    public int isSoulHp() {
        return isSoulHp;
    }

    public ArrayList<Integer> get_soulHp() {
        soulHp.add(0, Integer.valueOf(_soulHp_r));
        soulHp.add(1, Integer.valueOf(_soulHp_hpmin));
        soulHp.add(2, Integer.valueOf(_soulHp_hpmax));
        return soulHp;
    }

    public void set_soulHp(int flag) {
        isSoulHp = flag;
    }

    public String getoldtitle() {
        return oldtitle;
    }

    public void setoldtitle(String s) {
        oldtitle = s;
    }

    public final void setvipname1() {
        sendPackets(new S_HPUpdate(this));
        if (isInParty()) {
            getParty().updateMiniHP(this);
        }
        sendPackets(new S_MPUpdate(this));
        sendPackets(new S_SPMR(this));
        sendPackets(new S_OwnCharStatus(this));
        sendPackets(new S_OwnCharPack(this));
        removeAllKnownObjects();
        updateObject();
    }

    public String getvipname() {
        return vipname;
    }

    public void setvipname(String s) {
        vipname = s;
    }

    public int get_PVPdmgg() {
        return _PVPdmgg;
    }

    public void add_PVPdmgg(int i) {
        _PVPdmgg += i;
    }

    public int get_potion_healling() {
        return _potion_healling;
    }

    public void add_potion_healling(int i) {
        _potion_healling += i;
    }

    public int get_potion_heal() {
        return _potion_heal;
    }

    public void add_potion_heal(int i) {
        _potion_heal += i;
    }

    public void startPcRewardPrestigeGfxTimer(int gfx, int time) {
        (_gfxTimer4 = new L1PcRewardPrestigeGfxTimer(this, gfx, time)).start();
    }

    public void stopPcRewardPrestigeGfxTimer() {
        if (_gfxTimer4 != null) {
            _gfxTimer4.cancel();
            _gfxTimer4 = null;
        }
    }

    public int getWeaponSkillChance() {
        return _weaponSkillChance;
    }

    public void setWeaponSkillChance(int i) {
        _weaponSkillChance = i;
    }

    public double getWeaponSkillDmg() {
        return _addWeaponSkillDmg;
    }

    public void setWeaponSkillDmg(double d) {
        _addWeaponSkillDmg = d;
    }

    public String getnewaititle() {
        return newaititle;
    }

    public void setnewaititle(String s) {
        newaititle = s;
    }

    public void setnewaicount(int i) {
        _newaicount = i;
    }

    public int getnewaicount() {
        return _newaicount;
    }

    public void setproctctran(int i) {
        _proctctran = i;
    }

    public int getproctctran() {
        return _proctctran;
    }

    public void setnewcharpra(boolean newcharpra) {
        _newcharpra = newcharpra;
    }

    public boolean getnewcharpra() {
        return _newcharpra;
    }

    public int get_fwgj() {
        return _fwgj;
    }

    public void set_fwgj(int fwgj) {
        _fwgj = fwgj;
    }

    public int getlslocx() {
        return _lslocx;
    }

    public void setlslocx(int i) {
        _lslocx = i;
    }

    public int getlslocy() {
        return _lslocy;
    }

    public void setlslocy(int i) {
        _lslocy = i;
    }

    public void setguaji_count(int i) {
        _guaji_count = i;
    }

    public int getguaji_count() {
        return _guaji_count;
    }

    public void setaibig(int i) {
        _aibig = i;
    }

    public int getaibig() {
        return _aibig;
    }

    public void setaismall(int i) {
        _aismall = i;
    }

    public int getaismall() {
        return _aismall;
    }

    public void setnewaicount_2(int i) {
        _newaicount_2 = i;
    }

    public int getnewaicount_2() {
        return _newaicount_2;
    }

    public void setopengfxid(boolean opengfxid) {
        _opengfxid = opengfxid;
    }

    public boolean getopengfxid() {
        return _opengfxid;
    }

    public void removeAICheck(int itemid, long count) {
        if (this != null) {
            getInventory().consumeItem(itemid);
        }
    }

    public int getAiGxfxid() {
        return _AiGxfxid;
    }

    public void setAiGxfxid(int i) {
        _AiGxfxid = i;
    }

    public int getAierror() {
        return _Aierror;
    }

    public void setAierror(int i) {
        _Aierror = i;
    }

    public int getAdd_Er() {
        return _add_er;
    }

    public void setAdd_Er(int add_er) {
        _add_er = add_er;
    }

    public int getMoveErrorCount() {
        return moveErrorCount;
    }

    public void setMoveErrorCount(int moveErrorCount) {
        this.moveErrorCount = moveErrorCount;
    }

    public boolean isMoveStatus() {
        return moveStatus;
    }

    public void setMoveStatus(boolean moveStatus) {
        this.moveStatus = moveStatus;
    }

    public void setfollowskilltype(int followskilltype) {
        _followskilltype = followskilltype;
    }

    public int getfollowskilltype() {
        return _followskilltype;
    }

    public void setfollowskillhp(int followskillhp) {
        _followskillhp = followskillhp;
    }

    public int getfollowskillhp() {
        return _followskillhp;
    }

    public void setfollowmebuff(boolean followmebuff) {
        _followmebuff = followmebuff;
    }

    public boolean getfollowmebuff() {
        return _followmebuff;
    }

    public int getItemBlendcheckitem() {
        return _ItemBlendcheckitem;
    }

    public void setItemBlendcheckitem(int i) {
        _ItemBlendcheckitem = i;
    }

    public String get_ItemBlendcheckitemname() {
        return _ItemBlendcheckitemname;
    }

    public void set_ItemBlendcheckitemname(String s) {
        _ItemBlendcheckitemname = s;
    }

    public int getItemBlendcheckitemcount() {
        return _ItemBlendcheckitemcount;
    }

    public void setItemBlendcheckitemcount(int i) {
        _ItemBlendcheckitemcount = i;
    }

    public int get_hppotion() {
        return _hppotion;
    }

    public void add_hppotion(int i) {
        _hppotion += i;
    }

    public int get_pvp() {
        return _pvp;
    }

    public void add_pvp(int i) {
        _pvp += i;
    }

    public int get_bowpvp() {
        return _bowpvp;
    }

    public void add_bowpvp(int i) {
        _bowpvp += i;
    }

    public void setfollowxy1(int followxy1) {
        _followxy1 = followxy1;
    }

    public int getfollowxy1() {
        return _followxy1;
    }

    public void setpolyarrow(int i) {
        _polyarrow = i;
    }

    public int getpolyarrow() {
        return _polyarrow;
    }

    public int getcallclanal() {
        return callclanal;
    }

    public void setcallclanal(int s) {
        callclanal = s;
    }

    public void setchangtype1(int i) {
        _changtype1 = i;
    }

    public int getchangtype1() {
        return _changtype1;
    }

    public void setchangtype2(int i) {
        _changtype2 = i;
    }

    public int getchangtype2() {
        return _changtype2;
    }

    public void setchangtype3(int i) {
        _changtype3 = i;
    }

    public int getchangtype3() {
        return _changtype3;
    }

    public void setchangtype4(int i) {
        _changtype4 = i;
    }

    public int getchangtype4() {
        return _changtype4;
    }

    public void setchangtype5(int i) {
        _changtype5 = i;
    }

    public int getchangtype5() {
        return _changtype5;
    }

    public String getchangtypename1() {
        return changtypename1;
    }

    public void setchangtypename1(String s) {
        changtypename1 = s;
    }

    public String getchangtypename2() {
        return changtypename2;
    }

    public void setchangtypename2(String s) {
        changtypename2 = s;
    }

    public String getchangtypename3() {
        return changtypename3;
    }

    public void setchangtypename3(String s) {
        changtypename3 = s;
    }

    public String getchangtypename4() {
        return changtypename4;
    }

    public void setchangtypename4(String s) {
        changtypename4 = s;
    }

    public void setpag(int i) {
        _pag = i;
    }

    public int getpag() {
        return _pag;
    }

    public boolean IsKeyInEnemy() {
        return _keyenemy;
    }

    public void setKeyInEnemy(boolean b) {
        _keyenemy = b;
    }

    public void setInEnemyList(String id) {
        if (!_attackenemy.contains(new String(id))) {
            _attackenemy.add(new String(id));
        }
    }

    public void removeInEnemyList(String id) {
        if (_attackenemy.contains(new String(id))) {
            _attackenemy.remove(new String(id));
        }
    }

    public boolean isInEnemyList(String id) {
        return _attackenemy.contains(new String(id));
    }

    public ArrayList<String> InEnemyList() {
        return _attackenemy;
    }

    public void clearInEnemyList() {
        _attackenemy.clear();
    }

    public boolean IsKeyOutEnemy() {
        return _outenemy;
    }

    public void setKeyOutEnemy(boolean b) {
        _outenemy = b;
    }

    public boolean IsEnemyTeleport() {
        return _enemyteleport;
    }

    public void setIsEnemyTeleport(boolean b) {
        _enemyteleport = b;
    }

    public boolean IsAttackTeleport() {
        return _attackteleport;
    }

    public void setIsAttackTeleport(boolean b) {
        _attackteleport = b;
    }

    public boolean isBuffSkillList(Integer id) {
        return _autobuff.contains(new Integer(id.intValue()));
    }

    public ArrayList<Integer> BuffSkillList() {
        return _autobuff;
    }

    public int BuffSkillSize() {
        return _autobuff.size();
    }

    public void clearBuffSkillList() {
        _autobuff.clear();
    }

    public void setBuffSkillList(Integer id) {
        if (!_autobuff.contains(new Integer(id.intValue()))) {
            _autobuff.add(new Integer(id.intValue()));
        }
    }

    public boolean isAttackSkillList(Integer id) {
        return _autoattack.contains(new Integer(id.intValue()));
    }

    public int AttackSkillSize() {
        return _autoattack.size();
    }

    public int AttackSkillId() {
        int id = L1PcInstance._random.nextInt(_autoattack.size());
        if (id == _autoattack.size()) {
            --id;
        }
        int skillid = _autoattack.get(id).intValue();
        return skillid;
    }

    public void clearAttackSkillList() {
        _autoattack.clear();
    }

    public void setAttackSkillList(Integer id) {
        if (!_autoattack.contains(new Integer(id.intValue()))) {
            _autoattack.add(new Integer(id.intValue()));
        }
    }

    public ArrayList<Integer> AttackSkillList() {
        return _autoattack;
    }

    public boolean IsBuffSkill() {
        return _buffskill;
    }

    public void setBuffSkill(boolean b) {
        _buffskill = b;
    }

    public boolean IsAttackSkill() {
        return _attackskill;
    }

    public void setAttackSkill(boolean b) {
        _attackskill = b;
    }

    public void setitempotion(int itempotion) {
        _itempotion = itempotion;
    }

    public int getitempotion() {
        return _itempotion;
    }

    public void setitempotion1(int itempotion1) {
        _itempotion1 = itempotion1;
    }

    public int getitempotion1() {
        return _itempotion1;
    }

    public void setitempotion2(int itempotion2) {
        _itempotion2 = itempotion2;
    }

    public int getitempotion2() {
        return _itempotion2;
    }

    public void setitemitemid(int itemitemid) {
        _itemitemid = itemitemid;
    }

    public int getitemitemid() {
        return _itemitemid;
    }

    public void setitemitemid1(int itemitemid1) {
        _itemitemid1 = itemitemid1;
    }

    public int getitemitemid1() {
        return _itemitemid1;
    }

    public void setitemitemid2(int itemitemid2) {
        _itemitemid2 = itemitemid2;
    }

    public int getitemitemid2() {
        return _itemitemid2;
    }

    public void setitemadena(int itemadena) {
        _itemadena = itemadena;
    }

    public int getitemadena() {
        return _itemadena;
    }

    public void setitemadena1(int itemadena1) {
        _itemadena1 = itemadena1;
    }

    public int getitemadena1() {
        return _itemadena1;
    }

    public void setitemadena2(int itemadena2) {
        _itemadena2 = itemadena2;
    }

    public int getitemadena2() {
        return _itemadena2;
    }

    public void setpotioncount(int potioncount) {
        _potioncount = potioncount;
    }

    public int getpotioncount() {
        return _potioncount;
    }

    public void setpotioncount1(int potioncount1) {
        _potioncount1 = potioncount1;
    }

    public int getpotioncount1() {
        return _potioncount1;
    }

    public void setpotioncount2(int potioncount2) {
        _potioncount2 = potioncount2;
    }

    public int getpotioncount2() {
        return _potioncount2;
    }

    public boolean getgo_guajired() {
        return _go_guajired;
    }

    public void setgo_guajired(boolean flag) {
        _go_guajired = flag;
    }

    public void setma1(int ma1) {
        _ma1 = ma1;
    }

    public int getma1() {
        return _ma1;
    }

    private boolean SkillDmg(L1PcInstance pc, L1Character targets) {
        try {
            if (pc.AttackSkillSize() > 0 && L1PcInstance._random.nextInt(100) <= 20) {
                int skillid = pc.AttackSkillId();
                int[] MAP_IDSKILL = ConfigOther.MAP_IDSKILL;
                int[] MAP_SKILL = ConfigOther.MAP_SKILL;
                int[] array;
                int i = (array = MAP_IDSKILL).length;
                int j = 0;
                while (j < i) {
                    int mapid = array[j];
                    int[] array2;
                    int length = (array2 = MAP_SKILL).length;
                    int k = 0;
                    while (k < length) {
                        int mapskill = array2[k];
                        if (pc.getMapId() == mapid && skillid == mapskill) {
                            return false;
                        }
                        ++k;
                    }
                    ++j;
                }
                int[] bow_GFX_Arrow = ConfigGuaji.Guaji_map_stopskill;
                int[] array3;
                int length2 = (array3 = bow_GFX_Arrow).length;
                i = 0;
                while (i < length2) {
                    int gfx = array3[i];
                    if (skillid == gfx && pc.isActived()) {
                        return false;
                    }
                    ++i;
                }
                if (!pc.isSkillDelay()) {
                    L1Skills skill = SkillsTable.get().getTemplate(skillid);
                    L1SkillUse skillUse = new L1SkillUse();
                    skillUse.handleCommands(pc, skillid, targets.getId(), targets.getX(), targets.getY(),
                            skill.getBuffDuration(), 0);
                }
                return targets.isDead();
            }
            return false;
        } catch (Exception e) {
            L1PcInstance._log.error(e.getLocalizedMessage(), e);
            return false;
        }
    }

    public void setnpcdmg(double d) {
        _npcdmg = d;
    }

    public double getnpcdmg() {
        return _npcdmg;
    }

    public int getnewai1() {
        return _newai1;
    }

    public void setnewai1(int i) {
        _newai1 = i;
    }

    public int getnewai2() {
        return _newai2;
    }

    public void setnewai2(int i) {
        _newai2 = i;
    }

    public int getnewai3() {
        return _newai3;
    }

    public void setnewai3(int i) {
        _newai3 = i;
    }

    public int getnewai4() {
        return _newai4;
    }

    public void setnewai4(int i) {
        _newai4 = i;
    }

    public int getnewai5() {
        return _newai5;
    }

    public void setnewai5(int i) {
        _newai5 = i;
    }

    public int getnewai6() {
        return _newai6;
    }

    public void setnewai6(int i) {
        _newai6 = i;
    }

    public int getnewaiq1() {
        return _newaiq1;
    }

    public void setnewaiq1(int i) {
        _newaiq1 = i;
    }

    public int getnewaiq2() {
        return _newaiq2;
    }

    public void setnewaiq2(int i) {
        _newaiq2 = i;
    }

    public int getnewaiq3() {
        return _newaiq3;
    }

    public void setnewaiq3(int i) {
        _newaiq3 = i;
    }

    public int getnewaiq4() {
        return _newaiq4;
    }

    public void setnewaiq4(int i) {
        _newaiq4 = i;
    }

    public int getnewaiq5() {
        return _newaiq5;
    }

    public void setnewaiq5(int i) {
        _newaiq5 = i;
    }

    public int getnewaiq6() {
        return _newaiq6;
    }

    public void setnewaiq6(int i) {
        _newaiq6 = i;
    }

    public int getnewaiq7() {
        return _newaiq7;
    }

    public void setnewaiq7(int i) {
        _newaiq7 = i;
    }

    public int getnewaiq8() {
        return _newaiq8;
    }

    public void setnewaiq8(int i) {
        _newaiq8 = i;
    }

    public int getnewaiq9() {
        return _newaiq9;
    }

    public void setnewaiq9(int i) {
        _newaiq9 = i;
    }

    public int getnewaiq0() {
        return _newaiq0;
    }

    public void setnewaiq0(int i) {
        _newaiq0 = i;
    }

    public int getnpciddmg() {
        return _npciddmg;
    }

    public void setnpciddmg(int i) {
        _npciddmg = i;
    }

    public void broadcastPacket(ServerBasePacket packet) {
        ArrayList<L1PcInstance> objs = World.get().getVisiblePlayer(this);
        try {
            L1PcInstance pc = null;
            int i = 0;
            while (i < objs.size()) {
                pc = objs.get(i);
                if (pc.getMapId() < 16384 || pc.getMapId() > 25088) {
                    pc.sendPackets(packet);
                }
                ++i;
            }
        } catch (Exception e) {
            L1PcInstance._log.error(e.getLocalizedMessage(), e);
        } finally {
            objs.clear();
            objs = null;
        }
    }

    public void setNpcSpeed() {
        try {
            if (!getDolls().isEmpty()) {
                Object[] array;
                int length = (array = getDolls().values().toArray()).length;
                int i = 0;
                while (i < length) {
                    Object obj = array[i];
                    L1DollInstance doll = (L1DollInstance) obj;
                    if (doll != null) {
                        doll.setNpcMoveSpeed();
                    }
                    ++i;
                }
            }
        } catch (Exception e) {
            L1PcInstance._log.error(e.getLocalizedMessage(), e);
        }
        try {
            if (getHierarchs() != null) {
                getHierarchs().setNpcMoveSpeed();
            }
        } catch (Exception e) {
            L1PcInstance._log.error(e.getLocalizedMessage(), e);
        }
    }

    public void setfollowatk(boolean followatk) {
        _followatk = followatk;
    }

    public boolean getfollowatk() {
        return _followatk;
    }

    public void setfollowatkmagic(boolean followatkmagic) {
        _followatkmagic = followatkmagic;
    }

    public boolean getfollowatkmagic() {
        return _followatkmagic;
    }

    public boolean isfollowskill26() {
        return _isfollowskill26;
    }

    public void setfollowskill26(boolean flag) {
        _isfollowskill26 = flag;
    }

    public boolean isfollowskill42() {
        return _isfollowskill42;
    }

    public void setfollowskill42(boolean flag) {
        _isfollowskill42 = flag;
    }

    public boolean isfollowskill55() {
        return _isfollowskill55;
    }

    public void setfollowskill55(boolean flag) {
        _isfollowskill55 = flag;
    }

    public boolean isfollowskill68() {
        return _isfollowskill68;
    }

    public void setfollowskill68(boolean flag) {
        _isfollowskill68 = flag;
    }

    public boolean isfollowskill160() {
        return _isfollowskill160;
    }

    public void setfollowskill160(boolean flag) {
        _isfollowskill160 = flag;
    }

    public boolean isfollowskill79() {
        return _isfollowskill79;
    }

    public void setfollowskill79(boolean flag) {
        _isfollowskill79 = flag;
    }

    public boolean isfollowskill148() {
        return _isfollowskill148;
    }

    public void setfollowskill148(boolean flag) {
        _isfollowskill148 = flag;
    }

    public boolean isfollowskill151() {
        return _isfollowskill151;
    }

    public void setfollowskill151(boolean flag) {
        _isfollowskill151 = flag;
    }

    public boolean isfollowskill149() {
        return _isfollowskill149;
    }

    public void setfollowskill149(boolean flag) {
        _isfollowskill149 = flag;
    }

    public boolean isfollowskill158() {
        return _isfollowskill158;
    }

    public void setfollowskill158(boolean flag) {
        _isfollowskill158 = flag;
    }

    public boolean isnomoveguaji() {
        return _isnomoveguaji;
    }

    public void setnomoveguaji(boolean flag) {
        _isnomoveguaji = flag;
    }

    public boolean IsBadKeyInEnemy() {
        return _Badkeyenemy;
    }

    public void setBadKeyInEnemy(boolean b) {
        _Badkeyenemy = b;
    }

    public void setBadInEnemyList(String id) {
        if (!_Badattackenemy.contains(new String(id))) {
            _Badattackenemy.add(new String(id));
        }
    }

    public void removeBadInEnemyList(String id) {
        if (_Badattackenemy.contains(new String(id))) {
            _Badattackenemy.remove(new String(id));
        }
    }

    public boolean isBadInEnemyList(String id) {
        return _Badattackenemy.contains(new String(id));
    }

    public ArrayList<String> InBadEnemyList() {
        return _Badattackenemy;
    }

    public void clearBadInEnemyList() {
        _Badattackenemy.clear();
    }

    public boolean IsBadKeyOutEnemy() {
        return _Badoutenemy;
    }

    public void setBadKeyOutEnemy(boolean b) {
        _Badoutenemy = b;
    }

    public short getoldMapId() {
        return _oldMapId;
    }

    public void setoldMapId(short i) {
        _oldMapId = i;
    }

    public boolean getcheckpoly() {
        return _ischeckpoly;
    }

    public void setcheckpoly(boolean flag) {
        _ischeckpoly = flag;
    }

    public void setitemactionhtml(String itemactionhtml) {
        _itemactionhtml = itemactionhtml;
    }

    public String getitemactionhtml() {
        return _itemactionhtml;
    }

    public boolean getOutbur() {
        return _isOutbur;
    }

    public void setOutbur(boolean flag) {
        _isOutbur = flag;
    }

    public boolean getcheckOutbur() {
        return _ischeckOutbur;
    }

    public void setcheckOutbur(boolean flag) {
        _ischeckOutbur = flag;
    }

    public int getQuburcount() {
        return _Quburcount;
    }

    public void setQuburcount(int i) {
        _Quburcount = i;
    }

    public int getWeaponTotalDmg() {
        return _WeaponTotalDmg;
    }

    public void setWeaponTotalDmg(int i) {
        _WeaponTotalDmg = i;
    }

    public void addWeaponTotalDmg(int i) {
        _WeaponTotalDmg += i;
    }

    public void setweaknss1(int i) {
        _weaknss1 = i;
    }

    public int getweaknss1() {
        return _weaknss1;
    }

    public void addweaknss1(int i) {
        _weaknss += i;
    }

    public int getWeaponSkillPro() {
        return _WeaponSkillPro;
    }

    public void setWeaponSkillPro(int i) {
        _WeaponSkillPro = i;
    }

    public void addWeaponSkillPro(int i) {
        _WeaponSkillPro += i;
    }

    public int getPcMagicPro() {
        return _PcMagicPro;
    }

    public void setPcMagicPro(int i) {
        _PcMagicPro = i;
    }

    public void addPcMagicPro(int i) {
        _PcMagicPro += i;
    }

    public int getSave_Quest_Map1() {
        return _Save_Quest_Map1;
    }

    public void setSave_Quest_Map1(int i) {
        _Save_Quest_Map1 = i;
    }

    public int getSave_Quest_Map2() {
        return _Save_Quest_Map2;
    }

    public void setSave_Quest_Map2(int i) {
        _Save_Quest_Map2 = i;
    }

    public int getSave_Quest_Map3() {
        return _Save_Quest_Map3;
    }

    public void setSave_Quest_Map3(int i) {
        _Save_Quest_Map3 = i;
    }

    public int getSave_Quest_Map4() {
        return _Save_Quest_Map4;
    }

    public void setSave_Quest_Map4(int i) {
        _Save_Quest_Map4 = i;
    }

    public int getSave_Quest_Map5() {
        return _Save_Quest_Map5;
    }

    public void setSave_Quest_Map5(int i) {
        _Save_Quest_Map5 = i;
    }

    /**
     * 新版动态纹样系统
     */

    public void setCarId(int i) {
        _CardId = i;
    }

    public int getCardId() {
        return _CardId;
    }

    public Thread getTempThread() {
        return _tempThread;
    }

    public void setTempThread(SoulTowerThread thread) {
        _tempThread = thread;
    }

    public boolean isSoulTower() {
        return soulTower == 0;
    }

    public int getSoulTower() {
        return soulTower;
    }

    public void setSoulTower(int i) {
        soulTower = i;
    }

    public boolean petReceive(int itemObjectId) {
        if (!getMap().isTakePets()) {
            sendPackets(new S_ServerMessage(563));
            return false;
        }
        int petCost = 0;
        int petCount = 0;
        int divisor = 6;
        Object[] petList = getPetList().values().toArray();
        if (petList.length > ConfigOther.petcountchatype1) {
            sendPackets(new S_ServerMessage(489));
            return false;
        }
        Object[] array;
        int length = (array = petList).length;
        int i = 0;
        while (i < length) {
            Object pet = array[i];
            if (pet instanceof L1PetInstance && ((L1PetInstance) pet).getItemObjId() == itemObjectId) {
                return false;
            }
            petCost += ((L1NpcInstance) pet).getPetcost();
            ++i;
        }
        int charisma = getCha();
        L1Pet l1pet = PetReading.get().getTemplate(itemObjectId);
        if (l1pet == null) {
            return false;
        }
        int npcId = l1pet.get_npcid();
        charisma -= petCost;
        if (npcId == 45313 || npcId == 45710 || npcId == 45711 || npcId == 45712) {
            divisor = 12;
        } else {
            divisor = 2;
        }
        petCount = charisma / divisor;
        if (petCount <= 0) {
            sendPackets(new S_ServerMessage(489));
            return false;
        }
        L1Npc npcTemp = NpcTable.get().getTemplate(npcId);
        L1PetInstance pet2 = new L1PetInstance(npcTemp, this, l1pet);
        pet2.setPetcost(divisor);
        return true;
    }

    public boolean petReceive1(int itemObjectId) {
        if (!getMap().isTakePets()) {
            sendPackets(new S_ServerMessage(563));
            return false;
        }
        int petCost = 0;
        int petCount = 0;
        int divisor = 6;
        Object[] petList = getPetList().values().toArray();
        if (petList.length > ConfigOther.petcountchatype) {
            sendPackets(new S_ServerMessage(489));
            return false;
        }
        Object[] array;
        int length = (array = petList).length;
        int i = 0;
        while (i < length) {
            Object pet = array[i];
            petCost += ((L1NpcInstance) pet).getPetcost();
            ++i;
        }
        int charisma = getCha();
        if (isCrown()) {
            charisma += 6;
        } else if (isElf()) {
            charisma += 12;
        } else if (isWizard()) {
            charisma += 6;
        }
        L1Pet l1pet = PetReading.get().getTemplate(itemObjectId);
        if (l1pet == null) {
            return false;
        }
        int npcId = l1pet.get_npcid();
        charisma -= petCost;
        if (npcId == 45313 || npcId == 45710 || npcId == 45711 || npcId == 45712) {
            divisor = 12;
        } else {
            divisor = 6;
        }
        petCount = charisma / divisor;
        if (petCount <= 0) {
            sendPackets(new S_ServerMessage(489));
            return false;
        }
        L1Npc npcTemp = NpcTable.get().getTemplate(npcId);
        L1PetInstance pet2 = new L1PetInstance(npcTemp, this, l1pet);
        pet2.setPetcost(divisor);
        return true;
    }

    public boolean getarmor_setgive() {
        return _isarmor_setgive;
    }

    public void setarmor_setgive(boolean flag) {
        _isarmor_setgive = flag;
    }

    public String getSummon_npcid() {
        return _Summon_npcid;
    }

    public void setSummon_npcid(String Summon_npcid) {
        _Summon_npcid = Summon_npcid;
    }

    public void setsummon_skillid(int i) {
        _summon_skillid = i;
    }

    public int getsummon_skillid() {
        return _summon_skillid;
    }

    public void setsummon_skillidmp(int i) {
        _summon_skillidmp = i;
    }

    public int getsummon_skillidmp() {
        return _summon_skillidmp;
    }

    public boolean getchecksummid() {
        return _checksummid;
    }

    public void setchecksummid(boolean flag) {
        _checksummid = flag;
    }

    public boolean getchecksummidhp() {
        return _checksummidhp;
    }

    public void setchecksummidhp(boolean flag) {
        _checksummidhp = flag;
    }

    public boolean getmobatk() {
        return _mobatk;
    }

    public void setmobatk(boolean flag) {
        _mobatk = flag;
    }

    public int getTowerIsWhat() {
        return _towerIsWhat;
    }

    public void setTowerIsWhat(int i) {
        _towerIsWhat = i;
    }

    public int getAvatar() {
        return _avatar;
    }

    public void setAvatar(int i) {
        _avatar = i;
    }

    public boolean getIsTRIPLE_ARROW() {
        return _TRIPLEARROW;
    }

    public void setIsTRIPLE_ARROW(boolean s) {
        _TRIPLEARROW = s;
    }

    /**
     * Kevin 穿雲箭開關
     *
     * @return
     */
    public int getAllCall_clan() {
        return _AllCall_clan;
    }

    public void setAllCall_clan(int i) {
        _AllCall_clan = i;
    }

    public int getzhufudianshu() {
        return _zhufudianshu;
    }

    public void setzhufudianshu(int i) {
        _zhufudianshu = i;
    }

    public int getyiwa() {
        return _yiwa;
    }

    public void setyiwa(int i) {
        _yiwa = i;
    }

    public int getyiwacishu() {
        return _yiwacishu;
    }

    public void setyiwacishu(int i) {
        _yiwacishu = i;
    }

    public double getyiwajilv1() {
        return _yiwajilv1;
    }

    public void setyiwajilv1(double i) {
        _yiwajilv1 = i;
    }

    public double getyiwajilv2() {
        return _yiwajilv2;
    }

    public void setyiwajilv2(double i) {
        _yiwajilv2 = i;
    }

    public double getyiwajilv3() {
        return _yiwajilv3;
    }

    public void setyiwajilv3(double i) {
        _yiwajilv3 = i;
    }

    public int getyiwajilvdj() {
        return _yiwajilvdengji;
    }

    public void setyiwajilvdj(int i) {
        _yiwajilvdengji = i;
    }

    public int getshaha() {
        return _shaha;
    }

    public void setshaha(int i) {
        _shaha = i;
    }

    public int getshahacishu() {
        return _shahacishu;
    }

    public void setshahacishu(int i) {
        _shahacishu = i;
    }

    public double getshahajilv1() {
        return _shahajilv1;
    }

    public void setshahajilv1(double i) {
        _shahajilv1 = i;
    }

    public double getshahajilv2() {
        return _shahajilv2;
    }

    public void setshahajilv2(double i) {
        _shahajilv2 = i;
    }

    public double getshahajilv3() {
        return _shahajilv3;
    }

    public void setshahajilv3(double i) {
        _shahajilv3 = i;
    }

    public int getshahajilvdj() {
        return _shahajilvdengji;
    }

    public void setshahajilvdj(int i) {
        _shahajilvdengji = i;
    }

    public int getmapule() {
        return _mapule;
    }

    public void setmapule(int i) {
        _mapule = i;
    }

    public int getmapulecishu() {
        return _mapulecishu;
    }

    public void setmapulecishu(int i) {
        _mapulecishu = i;
    }

    public double getmapulejilv1() {
        return _mapulejilv1;
    }

    public void setmapulejilv1(double i) {
        _mapulejilv1 = i;
    }

    public double getmapulejilv2() {
        return _mapulejilv2;
    }

    public void setmapulejilv2(double i) {
        _mapulejilv2 = i;
    }

    public double getmapulejilv3() {
        return _mapulejilv3;
    }

    public void setmapulejilv3(double i) {
        _mapulejilv3 = i;
    }

    public int getmapulejilvdj() {
        return _mapulejilvdengji;
    }

    public void setmapulejilvdj(int i) {
        _mapulejilvdengji = i;
    }

    public int getpageliao() {
        return _pageliao;
    }

    public void setpageliao(int i) {
        _pageliao = i;
    }

    public int getpageliaocishu() {
        return _pageliaocishu;
    }

    public void setpageliaocishu(int i) {
        _pageliaocishu = i;
    }

    public double getpageliaojilv1() {
        return _pageliaojilv1;
    }

    public void setpageliaojilv1(double i) {
        _pageliaojilv1 = i;
    }

    public double getpageliaojilv2() {
        return _pageliaojilv2;
    }

    public void setpageliaojilv2(double i) {
        _pageliaojilv2 = i;
    }

    public double getpageliaojilv3() {
        return _pageliaojilv3;
    }

    public void setpageliaojilv3(double i) {
        _pageliaojilv3 = i;
    }

    public int getpageliaojilvdj() {
        return _pageliaojilvdengji;
    }

    public void setpageliaojilvdj(int i) {
        _pageliaojilvdengji = i;
    }

    public int getyinhaisa() {
        return _yinhaisa;
    }

    public void setyinhaisa(int i) {
        _yinhaisa = i;
    }

    public int getyinhaisacishu() {
        return _yinhaisacishu;
    }

    public void setyinhaisacishu(int i) {
        _yinhaisacishu = i;
    }

    public double getyinhaisajilv1() {
        return _yinhaisajilv1;
    }

    public void setyinhaisajilv1(double i) {
        _yinhaisajilv1 = i;
    }

    public double getyinhaisajilv2() {
        return _yinhaisajilv2;
    }

    public void setyinhaisajilv2(double i) {
        _yinhaisajilv2 = i;
    }

    public double getyinhaisajilv3() {
        return _yinhaisajilv3;
    }

    public void setyinhaisajilv3(double i) {
        _yinhaisajilv3 = i;
    }

    public int getyinhaisajilvdj() {
        return _yinhaisajilvdengji;
    }

    public void setyinhaisajilvdj(int i) {
        _yinhaisajilvdengji = i;
    }

    public void addExpRateToPc(int s) {// src013
        if (s > 0) {
            _expRateToPc = DoubleUtil.sum(_expRateToPc, (double) s / 100D);
        } else {
            _expRateToPc = DoubleUtil.sub(_expRateToPc, (double) (s * -1) / 100D);
        }
    }

    public double getExpRateToPc() {
        if (_expRateToPc < 0.0D) {
            return 0.0D;
        } else {
            return _expRateToPc;
        }
    }

    public Boolean getChatListenAlliance() {
        return chatListenAlliance;
    }

    public void setChatListenAlliance(Boolean chatListenAlliance) {
        this.chatListenAlliance = chatListenAlliance;
    }

    public Boolean getChatListenShout() {
        return chatListenShout;
    }

    public void setChatListenShout(Boolean chatListenShout) {
        this.chatListenShout = chatListenShout;
    }

    public Boolean getChatListenNormal() {
        return chatListenNormal;
    }

    public void setChatListenNormal(Boolean chatListenNormal) {
        this.chatListenNormal = chatListenNormal;
    }

    public Boolean getChatListenParty() {
        return chatListenParty;
    }

    public void setChatListenParty(Boolean chatListenParty) {
        this.chatListenParty = chatListenParty;
    }

    public Boolean getChatListenClan() {
        return chatListenClan;
    }

    public void setChatListenClan(Boolean chatListenClan) {
        this.chatListenClan = chatListenClan;
    }

    public Boolean getChatListenWhisper() {
        return chatListenWhisper;
    }

    public void setChatListenWhisper(Boolean chatListenWhisper) {
        this.chatListenWhisper = chatListenWhisper;
    }

    private class Death implements Runnable {
        private L1Character _lastAttacker;

        private Death(L1Character cha) {
            _lastAttacker = cha;
        }

        @Override
        public void run() {
            L1Character lastAttacker = _lastAttacker;
            L1PcInstance targetpc = null;
            _lastAttacker = null;
            setCurrentHp(0);
            setCurrentHp(0);
            setGresValid(false);
            while (isTeleport()) {
                try {
                    Thread.sleep(300L);
                } catch (Exception ex) {
                }
            }
            if (isInParty()) {
                Iterator<L1PcInstance> iterator = getParty().partyUsers().values().iterator();
                while (iterator.hasNext()) {
                    L1PcInstance member = iterator.next();
                    member.sendPackets(new S_PacketBoxParty(getParty(), L1PcInstance.this));
                }
            }
            if (isActived()) {
                L1PcInstance enemy = null;
                if (lastAttacker instanceof L1PcInstance) {
                    enemy = (L1PcInstance) lastAttacker;
                    if (enemy != null) {
                        NewAutoPractice.get().AddAutoList(L1PcInstance.this, enemy);
                    }
                }
                setActived(false);
            }
            set_delete_time(180);
            if (getnpcdmg() > 0.0) {
                sendPackets(new S_SystemMessage("\\fU因您死亡,攻擊傷害累積重新計算"));
                setnpcdmg(0.0);
                setnpciddmg(0);
            }
            if (!getPetList().isEmpty()) {
                Object[] array;
                int n = (array = getPetList().values().toArray()).length;
                int i = 0;
                while (i < n) {
                    Object petList = array[i];
                    if (petList instanceof L1SummonInstance) {
                        L1SummonInstance summon = (L1SummonInstance) petList;
                        // final S_NewMaster packet = new
                        // S_NewMaster((L1NpcInstance)summon);
                        if (summon != null) {
                            if (summon.destroyed()) {
                                return;
                            }
                            summon.Death(null);
                        }
                    }
                    ++i;
                }
            }
            if (!getDolls().isEmpty()) {
                Object[] array;
                int n = (array = getDolls().values().toArray()).length;
                int i = 0;
                while (i < n) {
                    Object obj = array[i];
                    L1DollInstance doll = (L1DollInstance) obj;
                    doll.deleteDoll();
                    ++i;
                }
            }
            if (getHierarchs() != null) {
                getHierarchs().deleteHierarch();
            }
            stopHpRegeneration();
            stopMpRegeneration();
            getMap().setPassable(getLocation(), true);
            int tempchargfx = 0;
            if (hasSkillEffect(67)) {
                tempchargfx = getTempCharGfx();
                setTempCharGfxAtDead(tempchargfx);
            } else {
                setTempCharGfxAtDead(getClassId());
            }
            L1SkillUse l1skilluse = new L1SkillUse();
            l1skilluse.handleCommands(L1PcInstance.this, 44, getId(), getX(),
                    getY(), 0, 1);
            if (tempchargfx != 0) {
                sendPacketsAll(new S_ChangeShape(L1PcInstance.this, tempchargfx));
            } else {
                try {
                    Thread.sleep(1000L);
                } catch (Exception ex2) {
                }
            }
            sendPacketsAll(new S_DoActionGFX(getId(), 8));
            L1EffectInstance tomb = L1SpawnUtil.spawnEffect(86126, 300, getX(),
                    getY(), getMapId(), L1PcInstance.this, 0);
            set_tomb(tomb);
            boolean isSafetyZone = false;
            boolean isCombatZone = false;
            boolean isWar = false;
            if (isSafetyZone()) {
                isSafetyZone = true;
            }
            if (isCombatZone()) {
                isCombatZone = true;
            }
            if (lastAttacker instanceof L1GuardInstance) {
                if (get_PKcount() > 0) {
                    set_PKcount(get_PKcount() - 1);
                }
                setLastPk(null);
            }
            if (lastAttacker instanceof L1GuardianInstance) {
                if (getPkCountForElf() > 0) {
                    setPkCountForElf(getPkCountForElf() - 1);
                }
                setLastPkForElf(null);
            }
            L1PcInstance fightPc = null;
            if (lastAttacker instanceof L1PcInstance) {
                fightPc = (L1PcInstance) lastAttacker;
            } else if (lastAttacker instanceof L1PetInstance) {
                L1PetInstance npc = (L1PetInstance) lastAttacker;
                if (npc.getMaster() != null) {
                    fightPc = (L1PcInstance) npc.getMaster();
                }
            } else if (lastAttacker instanceof L1SummonInstance) {
                L1SummonInstance npc2 = (L1SummonInstance) lastAttacker;
                if (npc2.getMaster() != null) {
                    fightPc = (L1PcInstance) npc2.getMaster();
                }
            } else if (lastAttacker instanceof L1IllusoryInstance) {
                L1IllusoryInstance npc3 = (L1IllusoryInstance) lastAttacker;
                if (npc3.getMaster() != null) {
                    fightPc = (L1PcInstance) npc3.getMaster();
                }
            } else if (lastAttacker instanceof L1EffectInstance) {
                L1EffectInstance npc4 = (L1EffectInstance) lastAttacker;
                if (npc4.getMaster() != null) {
                    fightPc = (L1PcInstance) npc4.getMaster();
                }
            }
            if (fightPc != null) {
                if (getFightId() == fightPc.getId()
                        && fightPc.getFightId() == getId()) {
                    setFightId(0);
                    sendPackets(new S_PacketBox(5, 0, 0));
                    fightPc.setFightId(0);
                    fightPc.sendPackets(new S_PacketBox(5, 0, 0));
                    return;
                }
                if (isEncounter() && fightPc.getLevel() > getLevel()
                        && fightPc.getLevel() - getLevel() >= 10) {
                    return;
                }
                if (castleWarResult()) {
                    isWar = true;
                }
                if (simWarResult(lastAttacker)) {
                    isWar = true;
                }
                if (isInWarAreaAndWarTime(L1PcInstance.this, fightPc)) {
                    isWar = true;
                }
                NewAutoPractice.get().AddAutoList(L1PcInstance.this, fightPc);
                if (getLevel() >= ConfigOther.killlevel) {
                    boolean isShow = false;
                    if (isWar) {
                        isShow = true;
                    } else if (!isCombatZone) {
                        isShow = true;
                    }
                    if (isShow && !isGm()) {
                        if (fightPc.getWeapon() != null) {
                            ConfigKill.get().msg(getName(), fightPc.getName(),
                                    fightPc.getWeapon().getViewName());
                        } else {
                            ConfigKill.get().msgnoweapon(fightPc.getName(), getName(), "空手");
                        }
                        RecordTable.get().killpc(fightPc.getName(), getName());
                    }
                }
                fightPc.get_other().add_killCount(1);
                get_other().add_deathCount(1);
            }
            if (isSafetyZone && !(lastAttacker instanceof L1MonsterInstance)) {
                return;
            }
            if (isCombatZone && !(lastAttacker instanceof L1MonsterInstance)) {
                return;
            }
            if (!getMap().isEnabledDeathPenalty()) {
                return;
            }
            boolean castle_area = L1CastleLocation.checkInAllWarArea(getX(),
                    getY(), getMapId());
            if (castleWarResult() && !ConfigAlt.ALT_WARPUNISHMENT) {
                return;
            }
            c1TypeRate(fightPc);
            expRate(fightPc);

            if (fightPc != null) { //Kevin 紅茶 正義值>=0 不管對象是什麼 殺死人就是-15000
                if (fightPc.getLawful() >= 0) {
                    fightPc.setLawful(-15000);
                    fightPc.sendPacketsAll(new S_Lawful(fightPc));
                } else {
                    fightPc.addLawful(-1000); // 正義值<0 往後殺死人就是-1000
                    fightPc.sendPacketsAll(new S_Lawful(fightPc));
                }
            }

            if (getLawful() < 32767) {
                if (castleWarResult() && castle_area) {
                    return;
                }
                if (!isProtector() || ProtectorSet.DEATH_VALUE_ITEM) {
                    lostRate(1);
                    lostSkillRate(1);
                }
            }
            if (taketreasure.START && fightPc != null) {
                checkItemSteal(fightPc);
            }
            if (fightPc != null) {
                if (isWar) {
                    return;
                }
                if (fightPc.getClan() != null && getClan() != null && WorldWar.get()
                        .isWar(fightPc.getClan().getClanName(), getClan().getClanName())) {
                    return;
                }
                if (fightPc.isSafetyZone()) {
                    return;
                }
                if (fightPc.isCombatZone()) {
                    return;
                }
                if (getLawful() >= 0 && !hasSkillEffect(5122)) {
                    boolean isChangePkCount = false;
                    if (fightPc.getLawful() < 30000) {
                        fightPc.set_PKcount(fightPc.get_PKcount() + 1);
                        isChangePkCount = true;
                        if (fightPc.isElf() && isElf()) {
                            fightPc.setPkCountForElf(fightPc.getPkCountForElf() + 1);
                        }
                    }
                    fightPc.setLastPk();
                    if (fightPc.getLawful() == 32767) {
                        fightPc.setLastPk(null);
                    }
                    if (fightPc.isElf() && isElf()) {
                        fightPc.setLastPkForElf();
                    }
                    //Kevin 以下為原本扣正義值的方式
                    /*int lawful = -1 * (int) (Math.pow(fightPc.getLevel(), 3.0) * 0.08);

                    if (fightPc.getLevel() < 50) {
                        lawful = -1 * (int) (Math.pow(fightPc.getLevel(), 2.0) * 4.0); //原本公式 Kevin

                    }


                    if (fightPc.getLawful() - 1000 < lawful) {
                        lawful = fightPc.getLawful() - 1000;
                    }

                    if (lawful <= -32768) {
                        lawful = -32768;
                    }
                    fightPc.setLawful(lawful);
                    fightPc.sendPacketsAll(new S_Lawful(fightPc));*/
                    /**
                     * Kevin
                     * PK次數達多少進入地獄 預設100
                     * 已改為 1000
                     */
                    if (ConfigAlt.ALT_PUNISHMENT) {
                        if (isChangePkCount && fightPc.get_PKcount() >= 1 && fightPc.get_PKcount() < 1000) {
                            fightPc.sendPackets(new S_BlueMessage(551, String.valueOf(fightPc.get_PKcount()), "1000"));
                        } else if (isChangePkCount && fightPc.get_PKcount() >= 1000) {
                            fightPc.beginHell(true);
                        }
                    }
                } else {
                    setPinkName(false);
                }
            }
        }

        private void c1TypeRate(L1Character lastAttacker) {
            L1PcInstance attacker = null;
            if (getPrestigeLv() > 0) {
                addPrestige(-ConfigOther.dead_score);
                if (getPrestigeLv() <= 0) {
                    setPrestige(0);
                }
            }
            if (CampSet.CAMPSTART && _c_power != null && _c_power.get_c1_type() != 0
                    && _c_power.get_c1_type() != 0) {
                L1ItemInstance item1 = getInventory().checkItemX(44165, 1L);
                if (item1 != null) {
                    getInventory().removeItem(item1, 1L);
                    sendPackets(new S_ServerMessage("\\fU你身上帶有" + item1.getName() + ",陣營積分受到守護!"));
                    return;
                }
                L1Name_Power power = _c_power.get_power();
                int score = _other.get_score() - power.get_down();
                if (score > 0) {
                    _other.set_score(score);
                    sendPackets(new S_ServerMessage(
                            String.valueOf(L1WilliamSystemMessage.ShowMessage(8)) + power.get_down()));
                    if (score < 0) {
                        _other.set_score(0);
                    }
                    if (power.get_getscore() > 0 && lastAttacker instanceof L1PcInstance) {
                        attacker = (L1PcInstance) lastAttacker;
                        if (attacker._other.get_score() > 0) {
                            attacker._other.set_score(attacker._other.get_score() + power.get_getscore());
                            if (attacker._other.get_score() < 0) {
                                attacker._other.set_score(0);
                            }
                            sendPackets(new S_ServerMessage("\\fU您被搶奪陣營積分:" + power.get_getscore()));
                            attacker.sendPackets(new S_ServerMessage("\\fU您搶奪陣營積分獲得:" + power.get_getscore()));
                        }
                    }
                } else {
                    _other.set_score(0);
                    sendPackets(new S_ServerMessage(L1WilliamSystemMessage.ShowMessage(9)));
                }
                int lv = C1_Name_Type_Table.get().getLv(_c_power.get_c1_type(),
                        _other.get_score());
                if (lv != _c_power.get_power().get_c1_id()) {
                    _c_power.set_power(L1PcInstance.this, false);
                    sendPackets(new S_ServerMessage(
                            "\\fR階級變更:" + _c_power.get_power().get_c1_name_type()));
                    sendPacketsAll(new S_ChangeName(L1PcInstance.this, true));
                }
            }
        }

        private void expRate(L1PcInstance fightPc) {
            if (isProtector() && !ProtectorSet.DEATH_VALUE_EXP) {
                return;
            }
            if (_isCraftsmanHeirloom()) {
                return;
            }

            L1ItemInstance item1 = getInventory().checkItemX2(44164, 1L); //丹丹 Kevin 原本為未裝備有效果，更改為裝備中才有效果
            if (item1 != null) {
                getInventory().removeItem(item1, 1L);//扣除一個羽毛
                ItemVIPTable.get().deleItemVIP(L1PcInstance.this, 44164);

                L1ItemInstance steal_item = getInventory().findItemId(ConfigOther.Lost_Item);
                L1Item steal_item_msg = ItemTable.get().getTemplate(ConfigOther.Lost_Item);
                if (ConfigOther.LostItem_ON == 1) {

                    if (steal_item != null) {
                        long count = (long) ThreadLocalRandom.current().nextInt(ConfigOther.Lost_Count_1, ConfigOther.Lost_Count_2) + 1;
                        count = steal_item.getCount() >= count ? count : steal_item.getCount();
                        getInventory().tradeItem(steal_item, count, fightPc.getInventory());//給予綁定元寶
                        fightPc.sendPackets(new S_ServerMessage("\\aD對方死亡!並發現對方裝備特殊道具"));
                        fightPc.sendPackets(new S_ServerMessage("獲得" + steal_item_msg + "：" + count));
                    } else {
                        L1Item steal_item_name = ItemTable.get().getTemplate(ConfigOther.Lost_Item); //Kevin 丹丹 從L1Item取得暫存名稱
                        steal_item_name.getName();//Kevin 丹丹 取得道具名稱
                        fightPc.sendPackets(new S_ServerMessage("\\aD對方死亡!並發現對方裝備特殊道具"));
                        fightPc.sendPackets(new S_ServerMessage("\\aD可惡!對方身上沒有【" + steal_item_name.getName() + "】可以搶奪"));
                    }
                }


                if (ConfigOther.LostItem2_ON == 1) {
                    long count2 = (long) ThreadLocalRandom.current().nextInt(ConfigOther.Lost_Count2_1, ConfigOther.Lost_Count2_2) + 1;
                    L1ItemInstance _item2 = ItemTable.get().createItem(ConfigOther.Lost_Item2);
                    L1Item _item2_msg = ItemTable.get().getTemplate(ConfigOther.Lost_Item2); //Kevin 丹丹 從L1Item取得暫存名稱
                    _item2_msg.getName();//Kevin 丹丹 取得道具名稱
                    if (fightPc != null && _item2 != null) {
                        _item2.setCount(count2);
                        fightPc.getInventory().storeItem(_item2);
                        fightPc.sendPackets(new S_ServerMessage("\\aD恭喜殺死【" + getName() + "】" + "獲得" + "【" + _item2_msg.getName() + "】"));
                        fightPc.sendPackets(new S_ServerMessage(403, _item2.getLogName())); // 获得0%。
                        fightPc.saveInventory();

                    }
                }
                if (ConfigOther.ExpRateLost == 1) {
                    sendPackets(new S_ServerMessage("\\fU" + item1.getName() + "的關係,因此經驗受到保護。"));
                    return;
                }

            }

            if (_vip_2) {
                sendPackets(new S_ServerMessage("受到經驗加值卡的保護,死亡不損失經驗。"));
                return;
            }

            if (hasSkillEffect(8000) && getMapId() == 537) {
                killSkillEffectTimer(8000);
                sendPackets(new S_ServerMessage("\\fU受到祝福之光的保護,剛剛死掉沒有掉%。"));
                return;
            }
            if (L1WilliamGfxIdOrginal.DeadExp(getTempCharGfx())) {
                setExpRes(1);
                return;
            }
            if (L1WilliamGfxIdOrginalpoly.DeadExp(getTempCharGfx())) {
                setExpRes(1);
                return;
            }
            deathPenalty();
            setGresValid(true);
            if (getExpRes() == 0) {
                setExpRes(1);
            }
        }

        //小天 Kevin 新增 正義值範圍機率掉落裝備
        private void lostRate(int mode) {
            if (_isCraftsmanHeirloom()) {
                return;
            }
            if (getMap().isdropitem()) {
                sendPackets(new S_ServerMessage("\\fU此地圖死亡不會噴出物品。"));
                return;
            }
            L1ItemInstance item1 = getInventory().checkItemX(44163, 1L);
            if (item1 != null) {
                getInventory().removeItem(item1, 1L);
                sendPackets(new S_ServerMessage("\\fU" + item1.getName() + "的關係,因此裝備受到保護。"));
                return;
            }
            if (isProtector() && ProtectorSet.DEATH_VALUE_ITEM) {
                L1ItemInstance item2 = getInventory().findItemId(ProtectorSet.ITEM_ID);
                if (item2 != null) {
                    item2.set_showId(get_showId());
                    int x = getX();
                    int y = getY();
                    short m = getMapId();
                    getInventory().tradeItem(item2, item2.isStackable() ? item2.getCount() : 1L,
                            World.get().getInventory(x, y, m));
                }
                sendPackets(new S_ServerMessage(638, item2.getLogName()));
                return;
            }


            int lostRate = (int) (((getLawful() + 32768.0) / 1000.0 - 65.0) * 4.0);
            if (lostRate < 0) {
                lostRate *= -1;
                if (getLawful() < 0) {
                    lostRate = 1000;
                }
                //  int rnd = L1PcInstance._random.nextInt(1000) + 1;
                // if (rnd <= lostRate) {
                switch (mode) {
                    case 1:
                        int lawful2 = getLawful();
                        int rnd = _random.nextInt(100) + 1;
                        if (lawful2 > -ConfigOther.Lost_Item_high && lawful2 <= -ConfigOther.Lost_Item_Low) {
                            if (rnd <= ConfigOther.Lost_Item_Rnd) {
                                if (ConfigOther.Lost_Item_Count > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Item_Count) + 1;
                                    caoPenaltyResult(c);
                                }
                            }
                        }
                        if (lawful2 > -ConfigOther.Lost_Item_high_2 && lawful2 <= -ConfigOther.Lost_Item_Low_2) {
                            if (rnd <= ConfigOther.Lost_Item_Rnd_2) {
                                if (ConfigOther.Lost_Item_Count_2 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Item_Count_2) + 1;
                                    caoPenaltyResult(c);
                                }
                            }
                        }
                        if (lawful2 > -ConfigOther.Lost_Item_high_3 && lawful2 <= -ConfigOther.Lost_Item_Low_3) {
                            if (rnd <= ConfigOther.Lost_Item_Rnd_3) {
                                if (ConfigOther.Lost_Item_Count_3 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Item_Count_3) + 1;
                                    caoPenaltyResult(c);
                                }
                            }
                        }
                        if (lawful2 > -ConfigOther.Lost_Item_high_4 && lawful2 <= -ConfigOther.Lost_Item_Low_4) {
                            if (rnd <= ConfigOther.Lost_Item_Rnd_4) {
                                if (ConfigOther.Lost_Item_Count_4 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Item_Count_4) + 1;
                                    caoPenaltyResult(c);
                                }
                            }
                        }//以下藍人區
                        if (lawful2 > ConfigOther.Lost_Item_high_5 && lawful2 < ConfigOther.Lost_Item_Low_5) {
                            if (rnd <= ConfigOther.Lost_Item_Rnd_5) {
                                if (ConfigOther.Lost_Item_Count_5 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Item_Count_5) + 1;
                                    caoPenaltyResult(c);
                                }
                            }
                        }
                        if (lawful2 > ConfigOther.Lost_Item_high_6 && lawful2 < ConfigOther.Lost_Item_Low_6) {
                            if (rnd <= ConfigOther.Lost_Item_Rnd_6) {
                                if (ConfigOther.Lost_Item_Count_6 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Item_Count_6) + 1;
                                    caoPenaltyResult(c);
                                }
                            }
                        }
                        if (lawful2 > ConfigOther.Lost_Item_high_7 && lawful2 < ConfigOther.Lost_Item_Low_7) {
                            if (rnd <= ConfigOther.Lost_Item_Rnd_7) {
                                if (ConfigOther.Lost_Item_Count_7 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Item_Count_7) + 1;
                                    caoPenaltyResult(c);
                                }
                            }
                        }
                        if (lawful2 > ConfigOther.Lost_Item_high_8 && lawful2 < ConfigOther.Lost_Item_Low_8) {
                            if (rnd <= ConfigOther.Lost_Item_Rnd_8) {
                                if (ConfigOther.Lost_Item_Count_8 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Item_Count_8) + 1;
                                    caoPenaltyResult(c);
                                }
                            }
                        }
                        break;
                    case 2:
                        int count = 0;
                        int lawful = getLawful();
                        if (lawful >= -32768 && lawful <= -30000) {
                            count = L1PcInstance._random.nextInt(4) + 1;
                        } else if (lawful > -30000 && lawful <= -20000) {
                            count = L1PcInstance._random.nextInt(3) + 1;
                        } else if (lawful > -20000 && lawful <= -10000) {
                            count = L1PcInstance._random.nextInt(2) + 1;
                        } else if (lawful > -10000 && lawful <= 32767) {
                            count = L1PcInstance._random.nextInt(1) + 1;
                        }
                        if (count > 0) {
                            caoPenaltyResult(count);
                        }
                }
            }
        }
        //以下註解為原版本
        /*private void lostRate() {
            if (_isCraftsmanHeirloom()) {
                return;
            }
            if (getMap().isdropitem()) {
                sendPackets(new S_ServerMessage("\\fU此地圖死亡不會噴出物品。"));
                return;
            }
            L1ItemInstance item1 = getInventory().checkItemX(44163, 1L);
            if (item1 != null) {
                getInventory().removeItem(item1, 1L);
                sendPackets(new S_ServerMessage("\\fU" + item1.getName() + "的關係,因此裝備受到保護。"));
                return;
            }
            if (isProtector() && ProtectorSet.DEATH_VALUE_ITEM) {
                L1ItemInstance item2 = getInventory().findItemId(ProtectorSet.ITEM_ID);
                if (item2 != null) {
                    item2.set_showId(get_showId());
                    int x = getX();
                    int y = getY();
                    short m = getMapId();
                    getInventory().tradeItem(item2, item2.isStackable() ? item2.getCount() : 1L,
                            World.get().getInventory(x, y, m));
                }
                sendPackets(new S_ServerMessage(638, item2.getLogName()));
                return;
            }
            int lostRate = (int) (((getLawful() + 32768.0) / 1000.0 - 65.0) * 4.0);
            if (lostRate < 0) {
                lostRate *= -1;
                if (getLawful() < 0) {
                    lostRate = 1000;
                }
                int rnd = L1PcInstance._random.nextInt(1000) + 1;
                if (rnd <= lostRate) {
                    int count = 0;
                    int lawful = getLawful();
                    if (lawful >= -32768 && lawful <= -30000) {
                        count = L1PcInstance._random.nextInt(4) + 1;
                    } else if (lawful > -30000 && lawful <= -20000) {
                        count = L1PcInstance._random.nextInt(3) + 1;
                    } else if (lawful > -20000 && lawful <= -10000) {
                        count = L1PcInstance._random.nextInt(2) + 1;
                    } else if (lawful > -10000 && lawful <= 32767) {
                        count = L1PcInstance._random.nextInt(1) + 1;
                    }
                    if (count > 0) {
                        caoPenaltyResult(count);
                    }
                }
            }
        }*/


        //小天 Kevin 新增 正義值範圍機率掉落技能
        private void lostSkillRate(int mode) {
            int skillCount = _skillList.size();
            if (skillCount > 0) {
                int count = 0;
                int lawful = getLawful();
                switch (mode) {
                    case 1:
                        int lawful3 = getLawful();
                        int rnd = _random.nextInt(100) + 1;
                        if (lawful3 > -ConfigOther.Lost_Skill_high && lawful3 <= -ConfigOther.Lost_Skill_Low) {
                            if (rnd <= ConfigOther.Lost_Skill_Rnd) {
                                if (ConfigOther.Lost_Skill_Count > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Skill_Count) + 1;
                                    delSkill(c);
                                }
                            }
                        }

                        if (lawful3 > -ConfigOther.Lost_Skill_high_2 && lawful3 <= -ConfigOther.Lost_Skill_Low_2) {
                            if (rnd <= ConfigOther.Lost_Skill_Rnd_2) {
                                if (ConfigOther.Lost_Skill_Count_2 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Skill_Count_2) + 1;
                                    delSkill(c);
                                }
                            }
                        }

                        if (lawful3 > -ConfigOther.Lost_Skill_high_3 && lawful3 <= -ConfigOther.Lost_Skill_Low_3) {
                            if (rnd <= ConfigOther.Lost_Skill_Rnd_3) {
                                if (ConfigOther.Lost_Skill_Count_3 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Skill_Count_3) + 1;
                                    delSkill(c);
                                }
                            }
                        }

                        if (lawful3 > -ConfigOther.Lost_Skill_high_4 && lawful3 <= -ConfigOther.Lost_Skill_Low_4) {
                            if (rnd <= ConfigOther.Lost_Skill_Rnd_4) {
                                if (ConfigOther.Lost_Skill_Count_4 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Skill_Count_4) + 1;
                                    delSkill(c);
                                }
                            }
                        }
                        //以下藍人區
                        if (lawful3 > ConfigOther.Lost_Skill_high_5 && lawful3 <= ConfigOther.Lost_Skill_Low_5) {
                            if (rnd <= ConfigOther.Lost_Skill_Rnd_5) {
                                if (ConfigOther.Lost_Skill_Count_5 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Skill_Count_5) + 1;
                                    delSkill(c);
                                }
                            }
                        }

                        if (lawful3 > ConfigOther.Lost_Skill_high_6 && lawful3 <= ConfigOther.Lost_Skill_Low_6) {
                            if (rnd <= ConfigOther.Lost_Skill_Rnd_6) {
                                if (ConfigOther.Lost_Skill_Count_6 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Skill_Count_6) + 1;
                                    delSkill(c);
                                }
                            }
                        }

                        if (lawful3 > ConfigOther.Lost_Skill_high_7 && lawful3 <= ConfigOther.Lost_Skill_Low_7) {
                            if (rnd <= ConfigOther.Lost_Skill_Rnd_7) {
                                if (ConfigOther.Lost_Skill_Count_7 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Skill_Count_7) + 1;
                                    delSkill(c);
                                }
                            }
                        }

                        if (lawful3 > ConfigOther.Lost_Skill_high_8 && lawful3 <= ConfigOther.Lost_Skill_Low_8) {
                            if (rnd <= ConfigOther.Lost_Skill_Rnd_8) {
                                if (ConfigOther.Lost_Skill_Count_8 > 0) {
                                    int c = _random.nextInt(ConfigOther.Lost_Skill_Count_8) + 1;
                                    delSkill(c);
                                }
                            }
                        }
                        break;

                    case 2:

                        if (lawful < 0 && lawful >= -32767) {
                            count = 1 + L1PcInstance._random.nextInt(3);
                        }
                        if (lawful == -32768) {
                            count = 4;
                        }
                        if (count > 0) {
                            delSkill(count);
                        }


                }
                /*
                if (lawful < 0 && lawful >= -32767) {
                    count = 1 + L1PcInstance._random.nextInt(3);
                }
                if (lawful == -32768) {
                    count = 4;
                }
                if (count > 0) {
                    delSkill(count);
                }

                 */
            }
        }
    }
}
