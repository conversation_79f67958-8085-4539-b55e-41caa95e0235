package com.lineage.server.model.classes;

class L1DragonKnightClassFeature extends L1ClassFeature {
	@Override
	public int getAcDefenseMax(final int ac) {
		return ac / 3;
	}

	@Override
	public int getMagicLevel(final int playerLevel) {
		return Math.min(4, playerLevel / 9);
	}

	@Override
	public int getAttackLevel(final int playerLevel) {
		return playerLevel / 10;
	}

	@Override
	public int getHitLevel(final int playerLevel) {
		return playerLevel / 4;
	}
}
