package com.lineage.server.model;

import java.util.Iterator;
import java.util.List;
import java.util.Collections;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Map;

public class L1UbPattern {
	private boolean _isFrozen;
	private Map<Integer, ArrayList<L1UbSpawn>> _groups;

	public L1UbPattern() {
		this._isFrozen = false;
		this._groups = new HashMap();
	}

	public void addSpawn(final int groupNumber, final L1UbSpawn spawn) {
		if (this._isFrozen) {
			return;
		}
		ArrayList spawnList = this._groups.get(Integer.valueOf(groupNumber));
		if (spawnList == null) {
			spawnList = new ArrayList();
			this._groups.put(Integer.valueOf(groupNumber), spawnList);
		}
		spawnList.add(spawn);
	}

	public void freeze() {
		if (this._isFrozen) {
			return;
		}
		final Iterator<ArrayList<L1UbSpawn>> iterator = this._groups.values().iterator();
		while (iterator.hasNext()) {
			final ArrayList spawnList = iterator.next();
			Collections.sort((List<Comparable>) spawnList);
		}
		this._isFrozen = true;
	}

	public boolean isFrozen() {
		return this._isFrozen;
	}

	public ArrayList<L1UbSpawn> getSpawnList(final int groupNumber) {
		if (!this._isFrozen) {
			return null;
		}
		return this._groups.get(Integer.valueOf(groupNumber));
	}
}
