package com.lineage.server.model.shop;

import java.io.IOException;
import java.io.Writer;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.sql.Timestamp;
import com.lineage.server.serverpackets.S_Disconnect;
import com.lineage.server.datatables.lock.TownReading;
import com.lineage.server.model.L1TownLocation;
import com.lineage.server.world.World;
import com.lineage.server.templates.L1Castle;
import com.lineage.server.model.L1TaxCalculator;
import com.lineage.server.datatables.lock.CastleReading;
import com.lineage.server.model.L1CastleLocation;
import com.lineage.server.templates.L1Item;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.utils.RangeInt;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.ArrayList;
import com.lineage.server.model.L1PcInventory;
import com.lineage.config.ConfigRate;
import com.lineage.server.model.Instance.L1ItemInstance;
import java.util.Iterator;
import com.lineage.server.templates.L1ShopItem;
import java.util.List;

public class L1Shop {
	private final int _npcId;
	private final int _currencyItemId;
	private final List<L1ShopItem> _sellingItems;
	private final List<L1ShopItem> _purchasingItems;

	public L1Shop(final int npcId, final int currencyItemId, final List<L1ShopItem> sellingItems,
			final List<L1ShopItem> purchasingItems) {
		if (sellingItems == null || purchasingItems == null) {
			throw new NullPointerException();
		}
		this._npcId = npcId;
		this._currencyItemId = currencyItemId;
		this._sellingItems = sellingItems;
		this._purchasingItems = purchasingItems;
	}

	public int getNpcId() {
		return this._npcId;
	}

	public List<L1ShopItem> getSellingItems() {
		return this._sellingItems;
	}

	public List<L1ShopItem> getPurchasingItems() {
		return this._purchasingItems;
	}

	public boolean isSelling(final int itemid) {
		final Iterator<L1ShopItem> iterator = this._sellingItems.iterator();
		while (iterator.hasNext()) {
			final L1ShopItem shopitem = iterator.next();
			if (shopitem.getItemId() == itemid) {
				return true;
			}
		}
		return false;
	}

	public boolean isPurchasing(final int itemid) {
		final Iterator<L1ShopItem> iterator = this._purchasingItems.iterator();
		while (iterator.hasNext()) {
			final L1ShopItem shopitem = iterator.next();
			if (shopitem.getItemId() == itemid) {
				return true;
			}
		}
		return false;
	}

	private boolean isPurchaseableItem(final L1ItemInstance item) {
		return item != null && !item.isEquipped() && item.getEnchantLevel() == 0 && item.getBless() < 128;
	}

	private L1ShopItem getPurchasingItem(final int itemId) {
		final Iterator<L1ShopItem> iterator = this._purchasingItems.iterator();
		while (iterator.hasNext()) {
			final L1ShopItem shopItem = iterator.next();
			if (shopItem.getItemId() == itemId) {
				return shopItem;
			}
		}
		return null;
	}

	public L1AssessedItem assessItem(final L1ItemInstance item) {
		final L1ShopItem shopItem = this.getPurchasingItem(item.getItemId());
		if (shopItem == null) {
			return null;
		}
		return new L1AssessedItem(item.getId(), this.getAssessedPrice(shopItem), item.getName2());
	}

	private int getAssessedPrice(final L1ShopItem item) {
		return (int) (item.getPrice() * ConfigRate.RATE_SHOP_PURCHASING_PRICE / item.getPackCount());
	}

	public List<L1AssessedItem> assessItems(final L1PcInventory inv) {
		final List<L1AssessedItem> result = new ArrayList();
		final Iterator<L1ShopItem> iterator = this._purchasingItems.iterator();
		while (iterator.hasNext()) {
			final L1ShopItem item = iterator.next();
			final L1ItemInstance[] itemsId;
			final int length = (itemsId = inv.findItemsId(item.getItemId())).length;
			int i = 0;
			while (i < length) {
				final L1ItemInstance targetItem = itemsId[i];
				if (this.isPurchaseableItem(targetItem)) {
					result.add(
							new L1AssessedItem(targetItem.getId(), this.getAssessedPrice(item), targetItem.getName()));
				}
				++i;
			}
		}
		return result;
	}

	private boolean ensureSell(final L1PcInstance pc, final L1ShopBuyOrderList orderList) {
		final int price = orderList.getTotalPrice();
		if (!RangeInt.includes(price, 0, 2000000000)) {
			pc.sendPackets(new S_ServerMessage(904, "2000000000"));
			return false;
		}
		final Iterator<L1ShopBuyOrder> iterator = orderList.getList().iterator();
		while (iterator.hasNext()) {
			final L1ShopBuyOrder order = iterator.next();
			final int itemid = order.getItem().getItemId();
			if (itemid == 56148
					&& (pc.getInventory().findItemId(56147) != null || pc.getInventory().findItemId(56148) != null)) {
				pc.sendPackets(new S_ServerMessage("身上已有妲蒂斯的魔力。"));
				return false;
			}
			if (!pc.getInventory().checkItem(this._currencyItemId, price)) {
				final L1Item item = ItemTable.get().getTemplate(this._currencyItemId);
				pc.sendPackets(new S_ServerMessage(337, item.getName()));
				return false;
			}
			final int currentWeight = pc.getInventory().getWeight() * 1000;
			if (currentWeight + orderList.getTotalWeight() > pc.getMaxWeight() * 1000.0) {
				pc.sendPackets(new S_ServerMessage(82));
				return false;
			}
			final int DailyBuyingCount = order.getItem().getDailyBuyingCount();
			if (DailyBuyingCount <= 0) {
				continue;
			}
			final int AlreadyBoughtCount = pc.getQuest().get_step(itemid);
			final int buyingcount = order.getCount();
			if (AlreadyBoughtCount >= DailyBuyingCount || buyingcount + AlreadyBoughtCount > DailyBuyingCount) {
				pc.sendPackets(new S_ServerMessage("超過每日限制購買數量上限。"));
				return false;
			}
			pc.getQuest().set_step(itemid, buyingcount + AlreadyBoughtCount);
		}
		int totalCount = pc.getInventory().getSize();
		final Iterator<L1ShopBuyOrder> iterator2 = orderList.getList().iterator();
		while (iterator2.hasNext()) {
			final L1ShopBuyOrder order2 = iterator2.next();
			final L1Item temp = order2.getItem().getItem();
			if (temp.isStackable()) {
				if (pc.getInventory().checkItem(temp.getItemId())) {
					continue;
				}
				++totalCount;
			} else {
				++totalCount;
			}
		}
		if (totalCount > 180) {
			pc.sendPackets(new S_ServerMessage(263));
			return false;
		}
		return true;
	}

	private void payCastleTax(final L1ShopBuyOrderList orderList) {
		final L1TaxCalculator calc = orderList.getTaxCalculator();
		final int price = orderList.getTotalPrice();
		final int castleId = L1CastleLocation.getCastleIdByNpcid(this._npcId);
		int castleTax = calc.calcCastleTaxPrice(price);
		int nationalTax = calc.calcNationalTaxPrice(price);
		if (castleId == 7 || castleId == 8) {
			castleTax += nationalTax;
			nationalTax = 0;
		}
		if (castleId != 0 && castleTax > 0) {
			final L1Castle castle = CastleReading.get().getCastleTable(castleId);
			synchronized (castle) {
				long money = castle.getPublicMoney();
				money += castleTax;
				castle.setPublicMoney(money);
				CastleReading.get().updateCastle(castle);
			}
			if (nationalTax > 0) {
				final L1Castle aden = CastleReading.get().getCastleTable(7);
				synchronized (aden) {
					long money2 = aden.getPublicMoney();
					money2 += nationalTax;
					aden.setPublicMoney(money2);
					CastleReading.get().updateCastle(aden);
				}
			}
		}
	}

	private void payDiadTax(final L1ShopBuyOrderList orderList) {
		final L1TaxCalculator calc = orderList.getTaxCalculator();
		final int price = orderList.getTotalPrice();
		final int diadTax = calc.calcDiadTaxPrice(price);
		if (diadTax <= 0) {
			return;
		}
		final L1Castle castle = CastleReading.get().getCastleTable(8);
		synchronized (castle) {
			long money = castle.getPublicMoney();
			money += diadTax;
			castle.setPublicMoney(money);
			CastleReading.get().updateCastle(castle);
		}
	}

	private void payTownTax(final L1ShopBuyOrderList orderList) {
		final int price = orderList.getTotalPrice();
		if (!World.get().isProcessingContributionTotal()) {
			final int town_id = L1TownLocation.getTownIdByNpcid(this._npcId);
			if (town_id >= 1 && town_id <= 10) {
				TownReading.get().addSalesMoney(town_id, price);
			}
		}
	}

	private void payTax(final L1ShopBuyOrderList orderList) {
		this.payCastleTax(orderList);
		this.payTownTax(orderList);
		this.payDiadTax(orderList);
	}

	private void sellItems(final L1PcInventory inv, final L1ShopBuyOrderList orderList, final L1PcInstance pc) {
		if (orderList == null) {
			return;
		}
		final int price = orderList.getTotalPrice();
		if (price <= 0) {
			return;
		}
		if (!inv.consumeItem(this._currencyItemId, price)) {
			return;
		}
		final Iterator<L1ShopBuyOrder> iterator = orderList.getList().iterator();
		while (iterator.hasNext()) {
			final L1ShopBuyOrder order = iterator.next();
			final int itemId = order.getItem().getItemId();
			final long amount = order.getCount();
			final int EnchantLevel = order.getItem().getEnchantLevel();
			final L1ItemInstance item = ItemTable.get().createItem(itemId);
			if (amount <= 0L) {
				continue;
			}
			item.setCount(amount);
			item.setEnchantLevel(EnchantLevel);
			item.setIdentified(true);
			inv.storeItem(item);
			pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
		}
	}

	public void sellItems(final L1PcInstance pc, final L1ShopBuyOrderList orderList) {
		if (orderList.isEmpty()) {
			return;
		}
		if (!this.ensureSell(pc, orderList)) {
			return;
		}
		if (pc.getInventory().getSize() + orderList.getList().size() >= 180) {
			pc.sendPackets(new S_ServerMessage(263));
			return;
		}
		if (this.getNpcId() != 93214) {
			this.sellItems(pc.getInventory(), orderList, pc);
			return;
		}
		if (!this.ensureCashSell2(pc, orderList, this.getNpcId())) {
			return;
		}
		this.sellCashItems2(pc, pc.getInventory(), orderList, this.getNpcId());
	}

	private boolean ensureCashSell2(final L1PcInstance pc, final L1ShopBuyOrderList orderList, final int npcId) {
		final int price = orderList.getTotalPrice();
		final L1Item item = ItemTable.get().getTemplate(95294);
		final long srcCount = pc.getInventory().countItems(95294);
		final long nc = price - srcCount;
		if (!pc.getInventory().checkItem(95294, price)) {
			pc.sendPackets(new S_ServerMessage(337, String.valueOf(item.getNameId()) + "(" + nc + ")"));
			return false;
		}
		final int currentWeight = pc.getInventory().getWeight() * 1000;
		if (currentWeight + orderList.getTotalWeight() > pc.getMaxWeight() * 1000.0) {
			pc.sendPackets(new S_ServerMessage(82));
			return false;
		}
		int totalCount = pc.getInventory().getSize();
		final Iterator<L1ShopBuyOrder> iterator = orderList.getList().iterator();
		while (iterator.hasNext()) {
			final L1ShopBuyOrder order = iterator.next();
			final L1Item temp = order.getItem().getItem();
			if (temp.isStackable()) {
				if (pc.getInventory().checkItem(temp.getItemId())) {
					continue;
				}
				++totalCount;
			} else {
				++totalCount;
			}
		}
		if (totalCount > 180) {
			pc.sendPackets(new S_ServerMessage(263));
			return false;
		}
		if (price <= 0 || price > 100000000) {
			pc.sendPackets(new S_Disconnect());
			return false;
		}
		return true;
	}

	private void sellCashItems2(final L1PcInstance pc, final L1PcInventory inv, final L1ShopBuyOrderList orderList,
			final int npcId) {
		if (!inv.consumeItem(95294, orderList.getTotalPrice())) {
			throw new IllegalStateException("屍魂幣不足.");
		}
		L1ItemInstance item = null;
		final Iterator<L1ShopBuyOrder> iterator = orderList.getList().iterator();
		while (iterator.hasNext()) {
			final L1ShopBuyOrder order = iterator.next();
			final int itemId = order.getItem().getItemId();
			final int amount = order.getCount();
			final int EnchantLevel = order.getItem().getEnchantLevel();
			item = ItemTable.get().createItem(itemId);
			if (amount <= 0) {
				continue;
			}
			item.setCount(amount);
			item.setEnchantLevel(EnchantLevel);
			item.setIdentified(true);
			inv.storeItem(item);
			pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
		}
	}

	public void buyItems(final L1PcInstance pc, final L1ShopSellOrderList orderList) {
		final L1PcInventory inv = orderList.getPc().getInventory();
		int totalPrice = 0;
		final Iterator<L1ShopSellOrder> iterator = orderList.getList().iterator();
		while (iterator.hasNext()) {
			final L1ShopSellOrder order = iterator.next();
			final long count = inv.removeItem(order.getItem().getTargetId(), order.getCount());
			if (count > 0L) {
				totalPrice = (int) (totalPrice + order.getItem().getAssessedPrice() * count);
				sellnpcitem("IP(" + pc.getNetConnection().getIp() + ")" + "玩家" + ":【" + pc.getName() + "】將"
						+ order.getItem().getTargetName() + "(" + order.getCount() + ")個,賣給Npc獲得金幣("
						+ order.getItem().getAssessedPrice() * count + ")時間:" + "("
						+ new Timestamp(System.currentTimeMillis()) + ")。");
			} else {
				sellnpcitem("IP(" + pc.getNetConnection().getIp() + ")" + "玩家非法販售,數量小於0" + ":【" + pc.getName() + "】將"
						+ order.getItem().getTargetId() + "(" + order.getCount() + ")個,賣給Npc獲得金幣("
						+ order.getItem().getAssessedPrice() * count + ")時間:" + "("
						+ new Timestamp(System.currentTimeMillis()) + ")。");
			}
		}
		totalPrice = RangeInt.ensure(totalPrice, 0, 2000000000);
		if (totalPrice > 0) {
			final L1ItemInstance item = ItemTable.get().createItem(this._currencyItemId);
			item.setCount(totalPrice);
			pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
			inv.storeItem(item);
		}
	}

	public L1ShopBuyOrderList newBuyOrderList() {
		return new L1ShopBuyOrderList(this);
	}

	public L1ShopSellOrderList newSellOrderList(final L1PcInstance pc) {
		return new L1ShopSellOrderList(this, pc);
	}

	private static void sellnpcitem(final String info) {
		try {
			final BufferedWriter out = new BufferedWriter(new FileWriter("玩家紀錄/萬物回收商紀錄.txt", true));
			out.write(String.valueOf(info) + "\r\n");
			out.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
