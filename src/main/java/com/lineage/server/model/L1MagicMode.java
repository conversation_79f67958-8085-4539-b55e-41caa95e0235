package com.lineage.server.model;

import com.lineage.server.model.Instance.L1DollInstance;
import java.util.Iterator;
import java.util.ConcurrentModificationException;
import org.apache.commons.logging.LogFactory;
import java.util.Random;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.Log;

public abstract class L1MagicMode {
	private static final Log _log;
	protected int _calcType;
	protected static final int PC_PC = 1;
	protected static final int PC_NPC = 2;
	protected static final int NPC_PC = 3;
	protected static final int NPC_NPC = 4;
	protected L1PcInstance _pc;
	protected L1PcInstance _targetPc;
	protected L1NpcInstance _npc;
	protected L1NpcInstance _targetNpc;
	protected int _leverage;
	protected static final Random _random;

	static {
		_log = LogFactory.getLog(L1MagicMode.class);
		_random = new Random();
	}

	public L1MagicMode() {
		this._pc = null;
		this._targetPc = null;
		this._npc = null;
		this._targetNpc = null;
		this._leverage = 10;
	}

	protected static boolean dmg0(final L1Character character) {
		try {
			if (character == null) {
				return false;
			}
			if (character.getSkillisEmpty()) {
				return false;
			}
			if (character.getSkillEffect().size() <= 0) {
				return false;
			}
			final Iterator<Integer> iterator = character.getSkillEffect().iterator();
			while (iterator.hasNext()) {
				final Integer key = iterator.next();
				final Integer integer = L1AttackList.SKM0.get(key);
				if (integer != null) {
					return true;
				}
			}
		} catch (ConcurrentModificationException ex) {
		} catch (Exception e) {
			L1MagicMode._log.error(e.getLocalizedMessage(), e);
			return false;
		}
		return false;
	}

	protected static double getDamageUpByClan(final L1PcInstance pc) {
		double dmg = 0.0;
		try {
			if (pc == null) {
				return 0.0;
			}
			dmg += pc.get_magic_modifier_dmg();
			final L1Clan clan = pc.getClan();
			if (clan == null) {
				return dmg;
			}
			if (clan.isClanskill() && pc.get_other().get_clanskill() == 4) {
				final int clanMan = clan.getOnlineClanMemberSize();
				dmg += 0.25 * clanMan;
			}
		} catch (Exception e) {
			L1MagicMode._log.error(e.getLocalizedMessage(), e);
			return 0.0;
		}
		return dmg;
	}

	protected static double getDamageReductionByClan(final L1PcInstance targetPc) {
		double dmg = 0.0;
		try {
			if (targetPc == null) {
				return 0.0;
			}
			dmg += targetPc.get_magic_reduction_dmg();
			final L1Clan clan = targetPc.getClan();
			if (clan == null) {
				return 0.0;
			}
			if (clan.isClanskill() && targetPc.get_other().get_clanskill() == 8) {
				final int clanMan = clan.getOnlineClanMemberSize();
				dmg += 0.25 * clanMan;
			}
		} catch (Exception e) {
			L1MagicMode._log.error(e.getLocalizedMessage(), e);
			return 0.0;
		}
		return dmg;
	}

	public void setLeverage(final int i) {
		this._leverage = i;
	}

	protected int getLeverage() {
		return this._leverage;
	}

	protected int getTargetSp() {
		int sp = 0;
		switch (this._calcType) {
		case 1:
		case 2: {
			sp = this._pc.getSp() - this._pc.getTrueSp();
			switch (this._pc.guardianEncounter()) {
			case 3: {
				++sp;
				break;
			}
			case 4: {
				sp += 2;
				break;
			}
			case 5: {
				sp += 3;
				break;
			}
			}
			break;
		}
		case 3:
		case 4: {
			sp = this._npc.getSp() - this._npc.getTrueSp();
			break;
		}
		}
		return sp;
	}

	protected int getTargetMr() {
		int mr = 0;
		switch (this._calcType) {
		case 1:
		case 3: {
			if (this._targetPc == null) {
				return 0;
			}
			mr = this._targetPc.getMr();
			switch (this._targetPc.guardianEncounter()) {
			case 0: {
				mr += 3;
				break;
			}
			case 1: {
				mr += 6;
				break;
			}
			case 2: {
				mr += 9;
				break;
			}
			}
			break;
		}
		case 2:
		case 4: {
			if (this._targetNpc == null) {
				return 0;
			}
			mr = this._targetNpc.getMr();
			break;
		}
		}
		return mr;
	}

	protected boolean calcEvasion() {
		if (this._targetPc == null) {
			return false;
		}
		final int ev = this._targetPc.get_evasion();
		if (ev == 0) {
			return false;
		}
		final int rnd = L1MagicMode._random.nextInt(1000) + 1;
		if (rnd <= ev) {
			if (!this._targetPc.getDolls().isEmpty()) {
				final Iterator<L1DollInstance> iterator = this._targetPc.getDolls().values().iterator();
				while (iterator.hasNext()) {
					final L1DollInstance doll = iterator.next();
					doll.show_action(2);
				}
			}
			return true;
		}
		return false;
	}

	protected double calcAttrResistance(final int attr) {
		int resist = 0;

		switch (this._calcType) {
		case 1:
		case 3: {
			if (this._targetPc == null) {
				return 0.0;
			}
			switch (attr) {
			case 1: {
				resist = this._targetPc.getEarth();
				break;
			}
			case 2: {
				resist = this._targetPc.getFire();
				break;
			}
			case 4: {
				resist = this._targetPc.getWater();
				break;
			}
			case 8: {
				resist = this._targetPc.getWind();
				break;
			}
			}
		}
		case 2:
		case 4: {
			if (this._targetNpc == null) {
				return 0.0;
			}
			switch (attr) {
			case 1: {
				resist = this._targetNpc.getEarth();
				break;
			}
			case 2: {
				resist = this._targetNpc.getFire();
				break;
			}
			case 4: {
				resist = this._targetNpc.getWater();
				break;
			}
			case 8: {
				resist = this._targetNpc.getWind();
				break;
			}
			}
		}
		}

		int resistFloor = (int) (0.16 * Math.abs(resist));
		if (resist >= 0) {
			resistFloor *= 1;
		} else {
			resistFloor *= -1;
		}
		final double attrDeffence = resistFloor / 32.0;
		return attrDeffence;
	}

	public abstract boolean calcProbabilityMagic(final int p0);

	public abstract int calcMagicDamage(final int p0);

	public abstract int calcHealing(final int p0);

	public abstract void commit(final int p0, final int p1);
}
