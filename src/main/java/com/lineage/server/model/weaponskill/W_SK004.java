package com.lineage.server.model.weaponskill;

import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import java.util.Random;
import org.apache.commons.logging.Log;

public class W_SK004 extends L1WeaponSkillType {
	private static final Log _log;
	private static final Random _random;

	static {
		_log = LogFactory.getLog(W_SK004.class);
		_random = new Random();
	}

	public static L1WeaponSkillType get() {
		return new W_SK004();
	}

	@Override
	public double start_weapon_skill(final L1PcInstance pc, final L1Character target, final L1ItemInstance weapon,
			final double srcdmg) {
		try {
			if (target.getCurrentMp() <= 0) {
				return 0.0;
			}
			final int chance = W_SK004._random.nextInt(1000) + 1;
			final int random = this.random(weapon);
			int mpadd = 0;
			if (this._type1 > 1) {
				mpadd += W_SK004._random.nextInt(this._type1) + 1;
			} else {
				mpadd += this._type1;
			}
			if (this._type3 == 1 && weapon.getEnchantLevel() > 0) {
				mpadd += W_SK004._random.nextInt(weapon.getEnchantLevel()) + 1;
			}
			mpadd = Math.max(mpadd, this._type1);
			mpadd = Math.min(mpadd, this._type2);
			int tg_new_mp = target.getCurrentMp() - mpadd;
			tg_new_mp = Math.max(tg_new_mp, 0);
			target.setCurrentMp(tg_new_mp);
			pc.setCurrentMp(pc.getCurrentMp() + mpadd);
			return 0.0;
		} catch (Exception e) {
			W_SK004._log.error(e.getLocalizedMessage(), e);
			return 0.0;
		}
	}
}
