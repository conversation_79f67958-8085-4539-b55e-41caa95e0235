package com.lineage.server.world;

import java.util.Iterator;
import com.lineage.server.model.map.L1Map;
import com.lineage.server.types.Point;
import java.util.ArrayList;
import java.util.Collections;
import org.apache.commons.logging.LogFactory;
import java.util.Collection;
import com.lineage.server.model.Instance.L1NpcInstance;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.logging.Log;

public class WorldNpc {
	private static final Log _log;
	private static WorldNpc _instance;
	private final ConcurrentHashMap<Integer, L1NpcInstance> _isNpc;
	private Collection<L1NpcInstance> _allNpcValues;

	static {
		_log = LogFactory.getLog(WorldNpc.class);
	}

	public static WorldNpc get() {
		if (WorldNpc._instance == null) {
			WorldNpc._instance = new WorldNpc();
		}
		return WorldNpc._instance;
	}

	private WorldNpc() {
		this._isNpc = new ConcurrentHashMap();
	}

	public Collection<L1NpcInstance> all() {
		try {
			final Collection<L1NpcInstance> vs = this._allNpcValues;
			return (vs != null) ? vs : (this._allNpcValues = Collections.unmodifiableCollection(this._isNpc.values()));
		} catch (Exception e) {
			WorldNpc._log.error(e.getLocalizedMessage(), e);
			return null;
		}
	}

	public ConcurrentHashMap<Integer, L1NpcInstance> map() {
		return this._isNpc;
	}

	public void put(final Integer key, final L1NpcInstance value) {
		try {
			this._isNpc.put(key, value);
		} catch (Exception e) {
			WorldNpc._log.error(e.getLocalizedMessage(), e);
		}
	}

	public void remove(final Integer key) {
		try {
			this._isNpc.remove(key);
		} catch (Exception e) {
			WorldNpc._log.error(e.getLocalizedMessage(), e);
		}
	}

	public ArrayList<L1NpcInstance> getVisibleMob(final L1NpcInstance src) {
		final L1Map map = src.getMap();
		final Point pt = src.getLocation();
		final ArrayList<L1NpcInstance> result = new ArrayList();
		final Iterator<L1NpcInstance> iter = this.all().iterator();
		while (iter.hasNext()) {
			final L1NpcInstance element = iter.next();
			if (element.equals(src)) {
				continue;
			}
			if (map != element.getMap()) {
				continue;
			}
			if (!pt.isInScreen(element.getLocation())) {
				continue;
			}
			result.add(element);
		}
		return result;
	}

	public int getVisibleCount(final L1NpcInstance src) {
		final L1Map map = src.getMap();
		final Point pt = src.getLocation();
		int count = 0;
		final Iterator<L1NpcInstance> iter = this.all().iterator();
		while (iter.hasNext()) {
			final L1NpcInstance element = iter.next();
			if (element.equals(src)) {
				continue;
			}
			if (map != element.getMap()) {
				continue;
			}
			if (!pt.isInScreen(element.getLocation()) || src.getNpcId() != element.getNpcId()) {
				continue;
			}
			++count;
		}
		return count;
	}
}
