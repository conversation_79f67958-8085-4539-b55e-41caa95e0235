package com.lineage.server.world;

import com.lineage.server.model.L1Object;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.map.L1Map;
import java.util.ArrayList;
import java.util.Iterator;
import com.lineage.server.types.Point;
import com.lineage.server.model.L1Location;
import java.util.Collections;
import org.apache.commons.logging.LogFactory;
import java.util.Collection;
import com.lineage.server.model.Instance.L1EffectInstance;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.logging.Log;

public class WorldEffect {
	private static final Log _log;
	private static WorldEffect _instance;
	private final ConcurrentHashMap<Integer, L1EffectInstance> _isEff;
	private Collection<L1EffectInstance> _allEffValues;

	static {
		_log = LogFactory.getLog(WorldEffect.class);
	}

	public static WorldEffect get() {
		if (WorldEffect._instance == null) {
			WorldEffect._instance = new WorldEffect();
		}
		return WorldEffect._instance;
	}

	private WorldEffect() {
		this._isEff = new ConcurrentHashMap();
	}

	public Collection<L1EffectInstance> all() {
		try {
			final Collection<L1EffectInstance> vs = this._allEffValues;
			return (vs != null) ? vs : (this._allEffValues = Collections.unmodifiableCollection(this._isEff.values()));
		} catch (Exception e) {
			WorldEffect._log.error(e.getLocalizedMessage(), e);
			return null;
		}
	}

	public ConcurrentHashMap<Integer, L1EffectInstance> map() {
		return this._isEff;
	}

	public void put(final Integer key, final L1EffectInstance value) {
		try {
			this._isEff.put(key, value);
		} catch (Exception e) {
			WorldEffect._log.error(e.getLocalizedMessage(), e);
		}
	}

	public void remove(final Integer key) {
		try {
			this._isEff.remove(key);
		} catch (Exception e) {
			WorldEffect._log.error(e.getLocalizedMessage(), e);
		}
	}

	public boolean isEffect(final L1Location loc, final int npcid) {
		final Iterator<L1EffectInstance> iter = this.all().iterator();
		while (iter.hasNext()) {
			final L1EffectInstance element = iter.next();
			if (loc.getMapId() != element.getMap().getId()) {
				continue;
			}
			if (npcid != element.getNpcId()) {
				continue;
			}
			if (loc.isSamePoint(element.getLocation())) {
				return true;
			}
		}
		return false;
	}

	public ArrayList<L1EffectInstance> getVisibleEffect(final L1Location loc) {
		final ArrayList<L1EffectInstance> result = new ArrayList();
		final Iterator<L1EffectInstance> iter = this.all().iterator();
		while (iter.hasNext()) {
			final L1EffectInstance element = iter.next();
			if (loc.getMapId() != element.getMap().getId()) {
				continue;
			}
			if (!loc.isSamePoint(element.getLocation())) {
				continue;
			}
			result.add(element);
		}
		return result;
	}

	public ArrayList<L1EffectInstance> getVisibleEffect(final L1EffectInstance src) {
		final L1Map map = src.getMap();
		final Point pt = src.getLocation();
		final ArrayList<L1EffectInstance> result = new ArrayList();
		final Iterator<L1EffectInstance> iter = this.all().iterator();
		while (iter.hasNext()) {
			final L1EffectInstance element = iter.next();
			if (element.equals(src)) {
				continue;
			}
			if (map.getId() != element.getMap().getId()) {
				continue;
			}
			if (src.getNpcId() != element.getNpcId()) {
				continue;
			}
			if (!pt.isSamePoint(element.getLocation())) {
				continue;
			}
			result.add(element);
		}
		return result;
	}

	public int getVisibleCount(final L1EffectInstance src) {
		final L1Map map = src.getMap();
		final Point pt = src.getLocation();
		int count = 0;
		final Iterator<L1EffectInstance> iter = this.all().iterator();
		while (iter.hasNext()) {
			final L1EffectInstance element = iter.next();
			if (element.equals(src)) {
				continue;
			}
			if (map != element.getMap()) {
				continue;
			}
			if (!pt.isInScreen(element.getLocation()) || src.getNpcId() != element.getNpcId()) {
				continue;
			}
			++count;
		}
		return count;
	}

	public ArrayList<L1Character> getFirewall(final L1EffectInstance firewall) {
		final L1Map map = firewall.getMap();
		final Point pt = firewall.getLocation();
		final ArrayList<L1Character> result = new ArrayList();
		final ArrayList<L1Object> mapSrc = World.get().getVisibleObjects(firewall, 2);
		if (mapSrc == null) {
			WorldEffect._log.error("遊戲世界儲存中心並未建立該地圖編號資料檔案: " + map.getId());
			return result;
		}
		if (mapSrc.isEmpty()) {
			return result;
		}
		if (World.get().findObject(firewall.getId()) == null) {
			return result;
		}
		if (World.get().findObject(firewall.getMaster().getId()) == null) {
			return result;
		}
		if (firewall.destroyed()) {
			return result;
		}
		if (firewall.getMaster() == null) {
			return result;
		}
		final Iterator<L1Object> iterator = mapSrc.iterator();
		while (iterator.hasNext()) {
			final L1Object element = iterator.next();
			if (!(element instanceof L1Character)) {
				continue;
			}
			if (firewall.getMaster().equals(element)) {
				continue;
			}
			final L1Character cha = (L1Character) element;
			if (cha.isDead()) {
				continue;
			}
			final int r = pt.getTileLineDistance(element.getLocation());
			if (r > 1) {
				continue;
			}
			result.add((L1Character) element);
		}
		return result;
	}
}
