package com.lineage.server.templates;

import com.lineage.server.world.World;
import com.lineage.server.serverpackets.S_SystemMessage;
import java.util.Random;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.datatables.BlendTable;

public class L1Blend {
	private int _item_id;
	private int _checkLevel;
	private int _checkClass;
	private int _rnd;
	private int _checkItem;
	private int _hpConsume;
	private int _mpConsume;
	private int _material;
	private int _material_count;
	private int _material_2;
	private int _material_2_count;
	private int _material_3;
	private int _material_3_count;
	private int _material_4;
	private int _material_4_count;
	private int _material_5;
	private int _material_5_count;
	private int _new_item;
	private int _new_item_counts;
	private int _new_Enchantlvl_SW;
	private int _new_item_Enchantlvl;
	private int _removeItem;
	private String _message;
	private int _item_Html;

	public L1Blend(final int item_id, final int checkLevel, final int checkClass, final int rnd, final int checkItem,
			final int hpConsume, final int mpConsume, final int material, final int material_count,
			final int material_2, final int material_2_count, final int material_3, final int material_3_count,
			final int material_4, final int material_4_count, final int material_5, final int material_5_count,
			final int new_item, final int new_item_counts, final int new_Enchantlvl_SW, final int new_item_Enchantlvl,
			final int removeItem, final String message, final int item_Html) {
		this._item_id = item_id;
		this._checkLevel = checkLevel;
		this._checkClass = checkClass;
		this._rnd = rnd;
		this._checkItem = checkItem;
		this._hpConsume = hpConsume;
		this._mpConsume = mpConsume;
		this._material = material;
		this._material_count = material_count;
		this._material_2 = material_2;
		this._material_2_count = material_2_count;
		this._material_3 = material_3;
		this._material_3_count = material_3_count;
		this._material_4 = material_4;
		this._material_4_count = material_4_count;
		this._material_5 = material_5;
		this._material_5_count = material_5_count;
		this._new_item = new_item;
		this._new_item_counts = new_item_counts;
		this._new_Enchantlvl_SW = new_Enchantlvl_SW;
		this._new_item_Enchantlvl = new_item_Enchantlvl;
		this._removeItem = removeItem;
		this._message = message;
		this._item_Html = item_Html;
	}

	public int getItemId() {
		return this._item_id;
	}

	public int getCheckLevel() {
		return this._checkLevel;
	}

	public int getCheckClass() {
		return this._checkClass;
	}

	public int getRandom() {
		return this._rnd;
	}

	public int getCheckItem() {
		return this._checkItem;
	}

	public int getHpConsume() {
		return this._hpConsume;
	}

	public int getMpConsume() {
		return this._mpConsume;
	}

	public int getMaterial() {
		return this._material;
	}

	public int getMaterial_count() {
		return this._material_count;
	}

	public int getMaterial_2() {
		return this._material_2;
	}

	public int getMaterial_2_count() {
		return this._material_2_count;
	}

	public int getMaterial_3() {
		return this._material_3;
	}

	public int getMaterial_3_count() {
		return this._material_3_count;
	}

	public int getMaterial_4() {
		return this._material_4;
	}

	public int getMaterial_4_count() {
		return this._material_4_count;
	}

	public int getMaterial_5() {
		return this._material_5;
	}

	public int getMaterial_5_count() {
		return this._material_5_count;
	}

	public int getNew_item() {
		return this._new_item;
	}

	public int getNew_item_counts() {
		return this._new_item_counts;
	}

	public int getNew_Enchantlvl_SW() {
		return this._new_Enchantlvl_SW;
	}

	public int getNew_item_Enchantlvl() {
		return this._new_item_Enchantlvl;
	}

	public int getRemoveItem() {
		return this._removeItem;
	}

	public String getMessage() {
		return this._message;
	}

	public int getitem_Html() {
		return this._item_Html;
	}

	public static int checkItemId(final int itemId) {
		final L1Blend Item_Blend = BlendTable.getInstance().getTemplate(itemId);
		if (Item_Blend == null) {
			return 0;
		}
		final int item_id = Item_Blend.getItemId();
		return item_id;
	}

	public static void getItemBlend(final L1PcInstance pc, final L1ItemInstance l1iteminstance, final int itemId) {
		final L1Blend Item_Blend = BlendTable.getInstance().getTemplate(itemId);
		boolean isBlend = false;
		boolean isConsumeItem = false;
		boolean isConsumeItem_2 = false;
		boolean isConsumeItem_3 = false;
		boolean isConsumeItem_4 = false;
		boolean isConsumeItem_5 = false;
		boolean isOK = false;
		boolean OpenHtml = false;
		final int I_B_ma = Item_Blend.getMaterial();
		final int I_B_ma2 = Item_Blend.getMaterial_2();
		final int I_B_ma3 = Item_Blend.getMaterial_3();
		final int I_B_ma4 = Item_Blend.getMaterial_4();
		final int I_B_ma5 = Item_Blend.getMaterial_5();
		final int ma_C = Item_Blend.getMaterial_count();
		final int ma_C2 = Item_Blend.getMaterial_2_count();
		final int ma_C3 = Item_Blend.getMaterial_3_count();
		final int ma_C4 = Item_Blend.getMaterial_4_count();
		final int ma_C5 = Item_Blend.getMaterial_5_count();
		final L1Item temp = ItemTable.get().getTemplate(Item_Blend.getItemId());
		final L1Item CheckItemtemp = ItemTable.get().getTemplate(Item_Blend.getCheckItem());
		final L1Item maNeme = ItemTable.get().getTemplate(I_B_ma);
		final L1Item maNeme2 = ItemTable.get().getTemplate(I_B_ma2);
		final L1Item maNeme3 = ItemTable.get().getTemplate(I_B_ma3);
		final L1Item maNeme4 = ItemTable.get().getTemplate(I_B_ma4);
		final L1Item maNeme5 = ItemTable.get().getTemplate(I_B_ma5);
		final L1Item NewItemNeme = ItemTable.get().getTemplate(Item_Blend.getNew_item());
		if (!isOK) {
			if (Item_Blend == null) {
				return;
			}
			if (Item_Blend.getCheckLevel() != 0 && pc.getLevel() < Item_Blend.getCheckLevel()) {
				OpenHtml = true;
				pc.sendPackets(
						new S_ServerMessage(318, new StringBuilder().append(Item_Blend.getCheckLevel()).toString()));
			}
			if (Item_Blend.getCheckClass() != 0) {
				byte class_id = 0;
				String Classmsg = "";
				if (pc.isCrown()) {
					class_id = 1;
				} else if (pc.isKnight()) {
					class_id = 2;
				} else if (pc.isWizard()) {
					class_id = 3;
				} else if (pc.isElf()) {
					class_id = 4;
				} else if (pc.isDarkelf()) {
					class_id = 5;
				} else if (pc.isDragonKnight()) {
					class_id = 6;
				} else if (pc.isIllusionist()) {
					class_id = 7;
				}
				switch (Item_Blend.getCheckClass()) {
				case 1: {
					Classmsg = "王族";
					break;
				}
				case 2: {
					Classmsg = "騎士";
					break;
				}
				case 3: {
					Classmsg = "法師";
					break;
				}
				case 4: {
					Classmsg = "妖精";
					break;
				}
				case 5: {
					Classmsg = "黑暗妖精";
					break;
				}
				case 6: {
					Classmsg = "龍騎士";
					break;
				}
				case 7: {
					Classmsg = "幻術士";
					break;
				}
				}
				if (Item_Blend.getCheckClass() != class_id) {
					if (!OpenHtml) {
						OpenHtml = true;
					}
					pc.sendPackets(new S_ServerMessage(166, "職業必須是", Classmsg, "才能使用此道具"));
				}
			}
			if (Item_Blend.getCheckItem() != 0 && !pc.getInventory().checkItem(Item_Blend.getCheckItem())) {
				if (!OpenHtml) {
					OpenHtml = true;
				}
				pc.sendPackets(new S_ServerMessage(166, "必須有", "【" + CheckItemtemp.getName() + "】", "才能使用此道具"));
			}
			if (Item_Blend.getHpConsume() != 0 || Item_Blend.getMpConsume() != 0) {
				if (pc.getCurrentHp() < Item_Blend.getHpConsume()) {
					if (!OpenHtml) {
						OpenHtml = true;
					}
					pc.sendPackets(
							new S_ServerMessage(166, "$1083", "必須有 (" + Item_Blend.getHpConsume() + ") ", "才能使用此道具"));
				}
				if (pc.getCurrentMp() < Item_Blend.getMpConsume()) {
					if (!OpenHtml) {
						OpenHtml = true;
					}
					pc.sendPackets(
							new S_ServerMessage(166, "$1084", "必須有 (" + Item_Blend.getMpConsume() + ") ", "才能使用此道具"));
				}
			}
			if (I_B_ma != 0 && ma_C != 0) {
				if (!pc.getInventory().checkItem(I_B_ma, ma_C)) {
					if (!OpenHtml) {
						OpenHtml = true;
					}
					pc.sendPackets(new S_ServerMessage(337, String.valueOf(maNeme.getName()) + "("
							+ (ma_C - pc.getInventory().countItems(maNeme.getItemId())) + ")"));
				} else {
					isConsumeItem = true;
				}
			}
			if (I_B_ma2 != 0 && ma_C2 != 0) {
				if (!pc.getInventory().checkItem(I_B_ma2, ma_C2)) {
					if (!OpenHtml) {
						OpenHtml = true;
					}
					pc.sendPackets(new S_ServerMessage(337, String.valueOf(maNeme2.getName()) + "("
							+ (ma_C2 - pc.getInventory().countItems(maNeme2.getItemId())) + ")"));
				} else {
					isConsumeItem_2 = true;
				}
			}
			if (I_B_ma3 != 0 && ma_C3 != 0) {
				if (!pc.getInventory().checkItem(I_B_ma3, ma_C3)) {
					if (!OpenHtml) {
						OpenHtml = true;
					}
					pc.sendPackets(new S_ServerMessage(337, String.valueOf(maNeme3.getName()) + "("
							+ (ma_C3 - pc.getInventory().countItems(maNeme3.getItemId())) + ")"));
				} else {
					isConsumeItem_3 = true;
				}
			}
			if (I_B_ma4 != 0 && ma_C4 != 0) {
				if (!pc.getInventory().checkItem(I_B_ma4, ma_C4)) {
					if (!OpenHtml) {
						OpenHtml = true;
					}
					pc.sendPackets(new S_ServerMessage(337, String.valueOf(maNeme4.getName()) + "("
							+ (ma_C4 - pc.getInventory().countItems(maNeme4.getItemId())) + ")"));
				} else {
					isConsumeItem_4 = true;
				}
			}
			if (I_B_ma5 != 0 && ma_C5 != 0) {
				if (!pc.getInventory().checkItem(I_B_ma5, ma_C5)) {
					if (!OpenHtml) {
						OpenHtml = true;
					}
					pc.sendPackets(new S_ServerMessage(337, String.valueOf(maNeme5.getName()) + "("
							+ (ma_C5 - pc.getInventory().countItems(maNeme5.getItemId())) + ")"));
				} else {
					isConsumeItem_5 = true;
				}
			}
			if (OpenHtml) {
				if (Item_Blend.getitem_Html() == 1) {
					String msg0 = "";
					String msg2 = "";
					String msg3 = "";
					String msg4 = "";
					String msg5 = "";
					String msg6 = "";
					String msg7 = "";
					String msg8 = "";
					String msg9 = "";
					String msg10 = "";
					String msg11 = "";
					String msg12 = "";
					msg0 = temp.getName();
					if (Item_Blend.getCheckLevel() != 0) {
						msg2 = Item_Blend.getCheckLevel() + " 級 ";
					} else {
						msg2 = " 無限制 ";
					}
					if (Item_Blend.getCheckClass() == 1) {
						msg3 = " 王族";
					} else if (Item_Blend.getCheckClass() == 2) {
						msg3 = " 騎士";
					} else if (Item_Blend.getCheckClass() == 3) {
						msg3 = " 法師";
					} else if (Item_Blend.getCheckClass() == 4) {
						msg3 = " 妖精";
					} else if (Item_Blend.getCheckClass() == 5) {
						msg3 = " 黑妖";
					} else if (Item_Blend.getCheckClass() == 6) {
						msg3 = " 龍騎士";
					} else if (Item_Blend.getCheckClass() == 7) {
						msg3 = " 幻術師";
					} else if (Item_Blend.getCheckClass() == 0) {
						msg3 = " 所有職業";
					}
					if (Item_Blend.getCheckItem() != 0) {
						msg4 = new StringBuilder().append(CheckItemtemp.getName()).toString();
					} else {
						msg4 = " 無";
					}
					if (Item_Blend.getHpConsume() != 0) {
						msg5 = Item_Blend.getHpConsume() + " 滴 ";
					} else {
						msg5 = " 無限制 ";
					}
					if (Item_Blend.getMpConsume() != 0) {
						msg6 = Item_Blend.getMpConsume() + " 滴 ";
					} else {
						msg6 = " 無限制 ";
					}
					if (I_B_ma != 0) {
						msg7 = maNeme.getName() + "(" + ma_C + ")";
					} else {
						msg7 = " 無";
					}
					if (I_B_ma2 != 0) {
						msg8 = maNeme2.getName() + "(" + ma_C2 + ")";
					} else {
						msg8 = " 無";
					}
					if (I_B_ma3 != 0) {
						msg9 = maNeme3.getName() + "(" + ma_C3 + ")";
					} else {
						msg9 = " 無";
					}
					if (I_B_ma4 != 0) {
						msg10 = maNeme4.getName() + "(" + ma_C4 + ")";
					} else {
						msg10 = " 無";
					}
					if (I_B_ma5 != 0) {
						msg11 = maNeme5.getName() + "(" + ma_C5 + ")";
					} else {
						msg11 = " 無";
					}
					msg12 = new StringBuilder().append(NewItemNeme.getName()).toString();
					final String[] msg13 = { msg0, msg2, msg3, msg4, msg5, msg6, msg7, msg8, msg9, msg10, msg11,
							msg12 };
					pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "ItemBlend6", msg13));
					return;
				}
				return;
			}
			isOK = true;
		}
		final Random _random = new Random();
		final int DBrnd = Item_Blend.getRandom();
		final int Rnd100 = _random.nextInt(100) + 1;
		final int New_item = Item_Blend.getNew_item();
		final int New_item_counts = Item_Blend.getNew_item_counts();
		final int ItemLV_SW = Item_Blend.getNew_Enchantlvl_SW();
		final int ItemLV = Item_Blend.getNew_item_Enchantlvl();
		final int ItemLV_rnd = _random.nextInt(ItemLV + 1);
		if (isOK) {
			if (Rnd100 <= DBrnd) {
				final int type2 = NewItemNeme.getType2();
				if (Item_Blend.getMessage() != null) {
					pc.sendPackets(new S_ServerMessage(166, Item_Blend.getMessage()));
				} else {
					pc.sendPackets(new S_SystemMessage("進行道具融合，請稍後。"));
				}
				if (type2 == 1 || type2 == 2) {
					if (ItemLV == 0) {
						createNewItem(pc, New_item, 1);
					} else if (ItemLV_SW == 0) {
						createNewItem_LV(pc, New_item, 1, ItemLV);
					} else {
						createNewItem_LV(pc, New_item, 1, ItemLV_rnd);
					}
				} else {
					createNewItem(pc, New_item, New_item_counts);
				}
			} else {
				pc.sendPackets(new S_SystemMessage("道具融合失敗。"));
			}
			isBlend = true;
			if (isBlend) {
				if (Item_Blend.getRemoveItem() == 1) {
					pc.getInventory().removeItem(l1iteminstance, 1L);
				}
				if (isConsumeItem) {
					pc.getInventory().consumeItem(I_B_ma, ma_C);
				}
				if (isConsumeItem_2) {
					pc.getInventory().consumeItem(I_B_ma2, ma_C2);
				}
				if (isConsumeItem_3) {
					pc.getInventory().consumeItem(I_B_ma3, ma_C3);
				}
				if (isConsumeItem_4) {
					pc.getInventory().consumeItem(I_B_ma4, ma_C4);
				}
				if (isConsumeItem_5) {
					pc.getInventory().consumeItem(I_B_ma5, ma_C5);
				}
				pc.setCurrentHp(pc.getCurrentHp() - Item_Blend.getHpConsume());
				pc.setCurrentMp(pc.getCurrentMp() - Item_Blend.getMpConsume());
			}
			return;
		}
	}

	public static boolean createNewItem(final L1PcInstance pc, final int item_id, final int count) {
		final L1ItemInstance item = ItemTable.get().createItem(item_id);
		item.setCount(count);
		if (item != null) {
			if (pc.getInventory().checkAddItem(item, count) == 0) {
				pc.getInventory().storeItem(item);
			} else {
				World.get().getInventory(pc.getX(), pc.getY(), pc.getMapId()).storeItem(item);
			}
			pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
			return true;
		}
		return false;
	}

	public static boolean createNewItem_LV(final L1PcInstance pc, final int item_id, final int count,
			final int EnchantLevel) {
		final L1ItemInstance item = ItemTable.get().createItem(item_id);
		item.setCount(count);
		item.setEnchantLevel(EnchantLevel);
		if (item != null) {
			if (pc.getInventory().checkAddItem(item, count) == 0) {
				pc.getInventory().storeItem(item);
			} else {
				World.get().getInventory(pc.getX(), pc.getY(), pc.getMapId()).storeItem(item);
			}
			pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
			return true;
		}
		return false;
	}
}
