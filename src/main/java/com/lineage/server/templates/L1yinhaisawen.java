package com.lineage.server.templates;

public class L1yinhaisawen {
	private int _type;
	private int _classid;
	private int _gif;
	private int _gif_time;
	private int _add_wmd;
	private int _add_wmc;
	private int _add_str;
	private int _add_dex;
	private int _add_con;
	private int _add_int;
	private int _add_wis;
	private int _add_cha;
	private int _add_ac;
	private int _add_hp;
	private int _add_mp;
	private int _add_hpr;
	private int _add_mpr;
	private int _add_dmg;
	private int _add_hit;
	private int _add_bow_dmg;
	private int _add_bow_hit;
	private int _add_dmg_r;
	private int _add_magic_r;
	private int _add_mr;
	private int _add_sp;
	private int _add_fire;
	private int _add_wind;
	private int _add_earth;
	private int _add_water;
	private int _add_stun;
	private int _add_stone;
	private int _add_sleep;
	private int _add_freeze;
	private int _add_sustain;
	private int _add_blind;
	private int _add_exp;
	private boolean _death_exp;
	private boolean _death_item;
	private boolean _death_skill;
	private boolean _death_score;
	private int _add_adena;
	private int _add_mf;
	private int _skin_id;
	private int _effect_icon;
	private int _polyId;
	private int _polyTime;
	private String _vipname;

	public L1yinhaisawen() {
		this._type = 0;
		this._gif = 0;
		this._gif_time = 0;
		this._add_wmd = 0;
		this._add_wmc = 0;
		this._add_str = 0;
		this._add_dex = 0;
		this._add_con = 0;
		this._add_int = 0;
		this._add_wis = 0;
		this._add_cha = 0;
		this._add_ac = 0;
		this._add_hp = 0;
		this._add_mp = 0;
		this._add_hpr = 0;
		this._add_mpr = 0;
		this._add_dmg = 0;
		this._add_hit = 0;
		this._add_bow_dmg = 0;
		this._add_bow_hit = 0;
		this._add_dmg_r = 0;
		this._add_magic_r = 0;
		this._add_mr = 0;
		this._add_sp = 0;
		this._add_fire = 0;
		this._add_wind = 0;
		this._add_earth = 0;
		this._add_water = 0;
		this._add_stun = 0;
		this._add_stone = 0;
		this._add_sleep = 0;
		this._add_freeze = 0;
		this._add_sustain = 0;
		this._add_blind = 0;
		this._add_exp = 0;
		this._death_exp = false;
		this._death_item = false;
		this._death_skill = false;
		this._death_score = false;
		this._add_adena = 0;
		this._add_mf = 0;
		this._effect_icon = 0;
		this._skin_id = 0;
		this._polyId = 0;
		this._polyTime = 0;
	}

	public int get_polyId() {
		return this._polyId;
	}

	public void set_polyId(final int i) {
		this._polyId = i;
	}

	public int get_polyTime() {
		return this._polyTime;
	}

	public void set_polyTime(final int i) {
		this._polyTime = i;
	}

	public int get_type() {
		return this._type;
	}

	public void set_type(final int i) {
		this._type = i;
	}

	public int get_classid() {
		return this._classid;
	}

	public void set_classid(final int i) {
		this._classid = i;
	}

	public int get_gif() {
		return this._gif;
	}

	public void set_gif(final int gif) {
		this._gif = gif;
	}

	public int get_gif_time() {
		return this._gif_time;
	}

	public void set_gif_time(final int gif_time) {
		this._gif_time = gif_time;
	}

	public int get_add_wmd() {
		return this._add_wmd;
	}

	public void set_add_wmd(final int add_wmd) {
		this._add_wmd = add_wmd;
	}

	public int get_add_wmc() {
		return this._add_wmc;
	}

	public void set_add_wmc(final int add_wmc) {
		this._add_wmc = add_wmc;
	}

	public int get_add_str() {
		return this._add_str;
	}

	public void set_add_str(final int add_str) {
		this._add_str = add_str;
	}

	public int get_add_dex() {
		return this._add_dex;
	}

	public void set_add_dex(final int add_dex) {
		this._add_dex = add_dex;
	}

	public int get_add_con() {
		return this._add_con;
	}

	public void set_add_con(final int add_con) {
		this._add_con = add_con;
	}

	public int get_add_int() {
		return this._add_int;
	}

	public void set_add_int(final int add_int) {
		this._add_int = add_int;
	}

	public int get_add_wis() {
		return this._add_wis;
	}

	public void set_add_wis(final int add_wis) {
		this._add_wis = add_wis;
	}

	public int get_add_cha() {
		return this._add_cha;
	}

	public void set_add_cha(final int add_cha) {
		this._add_cha = add_cha;
	}

	public int get_add_ac() {
		return this._add_ac;
	}

	public void set_add_ac(final int add_ac) {
		this._add_ac = add_ac;
	}

	public int get_add_hp() {
		return this._add_hp;
	}

	public void set_add_hp(final int add_hp) {
		this._add_hp = add_hp;
	}

	public int get_add_mp() {
		return this._add_mp;
	}

	public void set_add_mp(final int add_mp) {
		this._add_mp = add_mp;
	}

	public int get_add_hpr() {
		return this._add_hpr;
	}

	public void set_add_hpr(final int add_hpr) {
		this._add_hpr = add_hpr;
	}

	public int get_add_mpr() {
		return this._add_mpr;
	}

	public void set_add_mpr(final int add_mpr) {
		this._add_mpr = add_mpr;
	}

	public int get_add_dmg() {
		return this._add_dmg;
	}

	public void set_add_dmg(final int add_dmg) {
		this._add_dmg = add_dmg;
	}

	public int get_add_hit() {
		return this._add_hit;
	}

	public void set_add_hit(final int add_hit) {
		this._add_hit = add_hit;
	}

	public int get_add_bow_dmg() {
		return this._add_bow_dmg;
	}

	public void set_add_bow_dmg(final int add_bow_dmg) {
		this._add_bow_dmg = add_bow_dmg;
	}

	public int get_add_bow_hit() {
		return this._add_bow_hit;
	}

	public void set_add_bow_hit(final int add_bow_hit) {
		this._add_bow_hit = add_bow_hit;
	}

	public int get_add_dmg_r() {
		return this._add_dmg_r;
	}

	public void set_add_dmg_r(final int add_dmg_r) {
		this._add_dmg_r = add_dmg_r;
	}

	public int get_add_magic_r() {
		return this._add_magic_r;
	}

	public void set_add_magic_r(final int add_magic_r) {
		this._add_magic_r = add_magic_r;
	}

	public int get_add_mr() {
		return this._add_mr;
	}

	public void set_add_mr(final int add_mr) {
		this._add_mr = add_mr;
	}

	public int get_add_sp() {
		return this._add_sp;
	}

	public void set_add_sp(final int add_sp) {
		this._add_sp = add_sp;
	}

	public int get_add_fire() {
		return this._add_fire;
	}

	public void set_add_fire(final int add_fire) {
		this._add_fire = add_fire;
	}

	public int get_add_wind() {
		return this._add_wind;
	}

	public void set_add_wind(final int add_wind) {
		this._add_wind = add_wind;
	}

	public int get_add_earth() {
		return this._add_earth;
	}

	public void set_add_earth(final int add_earth) {
		this._add_earth = add_earth;
	}

	public int get_add_water() {
		return this._add_water;
	}

	public void set_add_water(final int add_water) {
		this._add_water = add_water;
	}

	public int get_add_stun() {
		return this._add_stun;
	}

	public void set_add_stun(final int add_stun) {
		this._add_stun = add_stun;
	}

	public int get_add_stone() {
		return this._add_stone;
	}

	public void set_add_stone(final int add_stone) {
		this._add_stone = add_stone;
	}

	public int get_add_sleep() {
		return this._add_sleep;
	}

	public void set_add_sleep(final int add_sleep) {
		this._add_sleep = add_sleep;
	}

	public int get_add_freeze() {
		return this._add_freeze;
	}

	public void set_add_freeze(final int add_freeze) {
		this._add_freeze = add_freeze;
	}

	public int get_add_sustain() {
		return this._add_sustain;
	}

	public void set_add_sustain(final int add_sustain) {
		this._add_sustain = add_sustain;
	}

	public int get_add_blind() {
		return this._add_blind;
	}

	public void set_add_blind(final int add_blind) {
		this._add_blind = add_blind;
	}

	public int get_add_exp() {
		return this._add_exp;
	}

	public void set_add_exp(final int add_exp) {
		this._add_exp = add_exp;
	}

	public boolean get_death_exp() {
		return this._death_exp;
	}

	public void set_death_exp(final boolean death_exp) {
		this._death_exp = death_exp;
	}

	public boolean get_death_item() {
		return this._death_item;
	}

	public void set_death_item(final boolean death_item) {
		this._death_item = death_item;
	}

	public boolean get_death_skill() {
		return this._death_skill;
	}

	public void set_death_skill(final boolean death_skill) {
		this._death_skill = death_skill;
	}

	public boolean get_death_score() {
		return this._death_score;
	}

	public void set_death_score(final boolean death_score) {
		this._death_score = death_score;
	}

	public int get_add_adena() {
		return this._add_adena;
	}

	public void set_add_adena(final int add_adena) {
		this._add_adena = add_adena;
	}

	public int get_add_mf() {
		return this._add_mf;
	}

	public void set_add_mf(final int mf) {
		this._add_mf = mf;
	}

	public int get_skin_id() {
		return this._skin_id;
	}

	public void set_skin_id(final int i) {
		this._skin_id = i;
	}

	public int get_effect_icon() {
		return this._effect_icon;
	}

	public void set_effect_icon(final int effect_icon) {
		this._effect_icon = effect_icon;
	}

	public void set_vipname(final String vipname) {
		this._vipname = vipname;
	}

	public final String getvipname() {
		return this._vipname;
	}
}
