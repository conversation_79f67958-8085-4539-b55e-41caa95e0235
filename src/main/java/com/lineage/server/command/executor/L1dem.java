package com.lineage.server.command.executor;

import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.command.GmHtml;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class L1dem implements L1CommandExecutor {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(L1dem.class);
	}

	private L1dem() {
	}

	public static L1CommandExecutor getInstance() {
		return new L1dem();
	}

	@Override
	public void execute(final L1PcInstance pc, final String cmdName, final String arg) {
		try {
			final GmHtml gmHtml = new GmHtml(pc, 1);
			gmHtml.show();
		} catch (Exception e) {
			L1dem._log.error("錯誤的GM指令格式: " + this.getClass().getSimpleName() + " 執行的GM:" + pc.getName());
			pc.sendPackets(new S_ServerMessage(261));
		}
	}
}
