package com.lineage.server.command.executor;

import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.utils.ConnectionPoolMonitor;
import com.lineage.server.utils.ConnectionPoolHealthChecker;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * 連接池管理命令
 * 用於管理員監控和管理資料庫連接池
 */
public class L1ConnectionPoolCommand implements L1CommandExecutor {

    private static final Log _log = LogFactory.getLog(L1ConnectionPoolCommand.class);

    private L1ConnectionPoolCommand() {
    }

    public static L1CommandExecutor getInstance() {
        return new L1ConnectionPoolCommand();
    }
    
    @Override
    public void execute(L1PcInstance pc, String cmdName, String arg) {
        try {
            if (pc.getAccessLevel() < 200) { // 只有高級管理員可以使用
                pc.sendPackets(new S_SystemMessage("您沒有權限使用此命令"));
                return;
            }
            
            if (arg == null || arg.trim().isEmpty()) {
                showHelp(pc);
                return;
            }
            
            String[] args = arg.trim().split("\\s+");
            String subCommand = args[0].toLowerCase();
            
            switch (subCommand) {
                case "status":
                case "狀態":
                    showConnectionPoolStatus(pc);
                    break;
                    
                case "report":
                case "報告":
                    showDetailedReport(pc);
                    break;
                    
                case "health":
                case "健康":
                    performHealthCheck(pc);
                    break;
                    
                case "monitor":
                case "監控":
                    if (args.length > 1) {
                        handleMonitorCommand(pc, args[1]);
                    } else {
                        showMonitorStatus(pc);
                    }
                    break;
                    
                default:
                    showHelp(pc);
                    break;
            }
            
        } catch (Exception e) {
            pc.sendPackets(new S_SystemMessage("執行命令時發生錯誤: " + e.getMessage()));
        }
    }
    
    /**
     * 顯示幫助信息
     */
    private void showHelp(L1PcInstance pc) {
        pc.sendPackets(new S_SystemMessage("=== 連接池管理命令 ==="));
        pc.sendPackets(new S_SystemMessage(".connpool status - 顯示連接池狀態"));
        pc.sendPackets(new S_SystemMessage(".connpool report - 顯示詳細報告"));
        pc.sendPackets(new S_SystemMessage(".connpool health - 執行健康檢查"));
        pc.sendPackets(new S_SystemMessage(".connpool monitor [start|stop|status] - 監控管理"));
    }
    
    /**
     * 顯示連接池狀態
     */
    private void showConnectionPoolStatus(L1PcInstance pc) {
        try {
            String report = ConnectionPoolMonitor.getInstance().getDetailedReport();
            String[] lines = report.split("\n");
            
            for (String line : lines) {
                if (!line.trim().isEmpty()) {
                    pc.sendPackets(new S_SystemMessage(line));
                }
            }
            
        } catch (Exception e) {
            pc.sendPackets(new S_SystemMessage("獲取連接池狀態時發生錯誤: " + e.getMessage()));
        }
    }
    
    /**
     * 顯示詳細報告
     */
    private void showDetailedReport(L1PcInstance pc) {
        try {
            // 顯示監控報告
            String monitorReport = ConnectionPoolMonitor.getInstance().getDetailedReport();
            String[] monitorLines = monitorReport.split("\n");
            
            for (String line : monitorLines) {
                if (!line.trim().isEmpty()) {
                    pc.sendPackets(new S_SystemMessage(line));
                }
            }
            
            // 顯示健康檢查報告
            String healthReport = ConnectionPoolHealthChecker.getInstance().getHealthReport();
            String[] healthLines = healthReport.split("\n");
            
            for (String line : healthLines) {
                if (!line.trim().isEmpty()) {
                    pc.sendPackets(new S_SystemMessage(line));
                }
            }
            
        } catch (Exception e) {
            pc.sendPackets(new S_SystemMessage("獲取詳細報告時發生錯誤: " + e.getMessage()));
        }
    }
    
    /**
     * 執行健康檢查
     */
    private void performHealthCheck(L1PcInstance pc) {
        try {
            pc.sendPackets(new S_SystemMessage("正在執行連接池健康檢查..."));
            
            boolean isHealthy = ConnectionPoolHealthChecker.getInstance().performManualHealthCheck();
            
            if (isHealthy) {
                pc.sendPackets(new S_SystemMessage("健康檢查完成 - 所有連接池狀態正常"));
            } else {
                pc.sendPackets(new S_SystemMessage("健康檢查完成 - 發現連接池問題，請查看日誌"));
            }
            
            // 顯示詳細健康報告
            String healthReport = ConnectionPoolHealthChecker.getInstance().getHealthReport();
            String[] lines = healthReport.split("\n");
            
            for (String line : lines) {
                if (!line.trim().isEmpty()) {
                    pc.sendPackets(new S_SystemMessage(line));
                }
            }
            
        } catch (Exception e) {
            pc.sendPackets(new S_SystemMessage("執行健康檢查時發生錯誤: " + e.getMessage()));
        }
    }
    
    /**
     * 處理監控命令
     */
    private void handleMonitorCommand(L1PcInstance pc, String action) {
        try {
            switch (action.toLowerCase()) {
                case "start":
                case "啟動":
                    ConnectionPoolMonitor.getInstance().startMonitoring();
                    ConnectionPoolHealthChecker.getInstance().startHealthCheck();
                    pc.sendPackets(new S_SystemMessage("連接池監控服務已啟動"));
                    break;
                    
                case "stop":
                case "停止":
                    ConnectionPoolMonitor.getInstance().stopMonitoring();
                    ConnectionPoolHealthChecker.getInstance().stopHealthCheck();
                    pc.sendPackets(new S_SystemMessage("連接池監控服務已停止"));
                    break;
                    
                case "status":
                case "狀態":
                    showMonitorStatus(pc);
                    break;
                    
                default:
                    pc.sendPackets(new S_SystemMessage("無效的監控命令: " + action));
                    pc.sendPackets(new S_SystemMessage("可用選項: start, stop, status"));
                    break;
            }
            
        } catch (Exception e) {
            pc.sendPackets(new S_SystemMessage("處理監控命令時發生錯誤: " + e.getMessage()));
        }
    }
    
    /**
     * 顯示監控狀態
     */
    private void showMonitorStatus(L1PcInstance pc) {
        pc.sendPackets(new S_SystemMessage("=== 連接池監控狀態 ==="));
        pc.sendPackets(new S_SystemMessage("監控服務: 運行中"));
        pc.sendPackets(new S_SystemMessage("健康檢查: 運行中"));
        pc.sendPackets(new S_SystemMessage("監控間隔: 5分鐘"));
        pc.sendPackets(new S_SystemMessage("健康檢查間隔: 10分鐘"));
    }
}
