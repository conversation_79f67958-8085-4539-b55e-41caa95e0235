package com.lineage.server.types;

public class UByte8 {
	public static byte[] fromArray(final long[] buff) {
		final byte[] byteBuff = new byte[buff.length * 4];
		int i = 0;
		while (i < buff.length) {
			byteBuff[i * 4 + 0] = (byte) (int) (buff[i] & 0xFFL);
			byteBuff[i * 4 + 1] = (byte) (int) (buff[i] >> 8 & 0xFFL);
			byteBuff[i * 4 + 2] = (byte) (int) (buff[i] >> 16 & 0xFFL);
			byteBuff[i * 4 + 3] = (byte) (int) (buff[i] >> 24 & 0xFFL);
			++i;
		}
		return byteBuff;
	}

	public static byte[] fromArray(final char[] buff) {
		final byte[] byteBuff = new byte[buff.length];
		int i = 0;
		while (i < buff.length) {
			byteBuff[i] = (byte) (buff[i] & 'ÿ');
			++i;
		}
		return byteBuff;
	}

	public static byte fromUChar8(final char c) {
		return (byte) (c & 'ÿ');
	}

	public static byte[] fromULong32(final long l) {
		final byte[] byteBuff = { (byte) (int) (l & 0xFFL), (byte) (int) (l >> 8 & 0xFFL),
				(byte) (int) (l >> 16 & 0xFFL), (byte) (int) (l >> 24 & 0xFFL) };
		return byteBuff;
	}
}
