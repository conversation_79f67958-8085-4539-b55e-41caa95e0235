package com.lineage.server.Instance.character;

import com.lineage.DatabaseFactory;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.utils.L1QueryUtil;
import com.lineage.server.utils.SQLUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class CharacterPunishInstance {
    private static final Log log = LogFactory.getLog(CharacterPunishInstance.class);

    private static final Map<Integer, String> punishList = new ConcurrentHashMap<>();

    private static CharacterPunishInstance instance;

    public CharacterPunishInstance() {
        load();
    }

    public static CharacterPunishInstance get() {
        if (CharacterPunishInstance.instance == null) {
            CharacterPunishInstance.instance = new CharacterPunishInstance();
        }
        return CharacterPunishInstance.instance;
    }

    public void reload() {
        punishList.clear();
        load();
    }

    public void load() {
        Connection co = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            co = DatabaseFactory.get().getConnection();
            ps = co.prepareStatement("SELECT * FROM `character_punish`");
            rs = ps.executeQuery();
            while (rs.next()) {
                punishList.put(rs.getInt("chart_id"), rs.getString("char_name"));
            }
        } catch (SQLException e) {
            log.error(e.getLocalizedMessage(), e);
        } finally {
            SQLUtil.close(rs);
            SQLUtil.close(ps);
            SQLUtil.close(co);
        }
        log.info("載入玩家懲罰名單:" + CharacterPunishInstance.punishList.size());
    }


    public void addCharacter(L1PcInstance pc) {
        addCharacterToDb(pc.getId(), pc.getName());
        punishList.put(pc.getId(), pc.getName());
    }

    public void addCharacterToDb(Integer chartId, String chartName) {
        final String sql = "INSERT INTO character_punish (chart_id, char_name) VALUE (?, ?)";
        L1QueryUtil.execute(sql, new Object[]{chartId, chartName});
    }

    public void removeCharacter(L1PcInstance pc) {
        removeCharacter(pc.getId());
        punishList.remove(pc.getId());
    }

    public void removeCharacter(Integer chartId) {
        final String sql = "DELETE FROM `character_punish` WHERE `chart_id`=?";
        L1QueryUtil.execute(sql, new Object[]{chartId});
    }


    public Boolean checkCharacter(Integer chartId) {
        return punishList.containsKey(chartId);
    }

}
