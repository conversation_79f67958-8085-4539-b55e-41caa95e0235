package com.lineage.server.Instance;

import com.enums.MapLimitType;
import com.lineage.server.datatables.map.MapLimitItemTable;
import com.lineage.server.datatables.map.MapLimitSettingTable;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;

public class MapLimitInstance {
    private static final Log log = LogFactory.getLog(MapLimitInstance.class);

    private static MapLimitInstance instance;

    public static MapLimitInstance get() {
        if (MapLimitInstance.instance == null) {
            MapLimitInstance.instance = new MapLimitInstance();
        }
        return MapLimitInstance.instance;
    }

    public void setEquippedItem(L1PcInstance pc, Integer mapId) {
        MapLimitSettingTable mapLimitConfig = MapLimitSettingInstance.get().getConfigByMapId(mapId);
        if (mapLimitConfig == null) {
            return;
        }

        if (MapLimitType.CAN_USE.getCode().equals(mapLimitConfig.getLimitType())) {
            setUnequippedLimitItem(pc, mapId);
        } else {
            setEquippedLimitItem(pc, mapId);
        }
    }


    private void setEquippedLimitItem(L1PcInstance pc, Integer mapId) {
        List<MapLimitItemTable> limitItems = MapLimitItemInstance.get().getLimitItemsByMapId(mapId);

        if (limitItems == null) {
            return;
        }

        List<L1ItemInstance> inventoryItems = pc.getInventory().getItems();
        for (L1ItemInstance inventoryItem : inventoryItems) {
            System.out.println(inventoryItem.getItemId());
            if (!inventoryItem.isEquipped()) {
                continue;
            }

            if (MapLimitItemInstance.get().checkLimitItemByMapId(mapId, inventoryItem.getItemId())) {
                pc.getInventory().setEquipped(inventoryItem,Boolean.FALSE);
            }
        }
    }

    private void setUnequippedLimitItem(L1PcInstance pc, Integer mapId) {
        List<MapLimitItemTable> limitItems = MapLimitItemInstance.get().getLimitItemsByMapId(mapId);

        if (limitItems == null) {
            return;
        }

        List<L1ItemInstance> inventoryItems = pc.getInventory().getItems();
        for (L1ItemInstance inventoryItem : inventoryItems) {
            if (!inventoryItem.isEquipped()) {
                continue;
            }

            if (!MapLimitItemInstance.get().checkLimitItemByMapId(mapId, inventoryItem.getItemId())) {
                pc.getInventory().setEquipped(inventoryItem,Boolean.FALSE);
            }
        }
    }


    public Boolean canEquippedItem(Integer mapId, Integer itemId) {
        MapLimitSettingTable mapLimitConfig = MapLimitSettingInstance.get().getConfigByMapId(mapId);

        if (mapLimitConfig == null) {
            return Boolean.TRUE;
        }

        if (MapLimitType.CAN_USE.getCode().equals(mapLimitConfig.getLimitType())) {
            return checkItemOnlyEquipped(mapId, itemId);
        } else {
            return checkItemLimitEquipped(mapId, itemId);
        }
    }

    private Boolean checkItemLimitEquipped(Integer mapId, Integer itemId) {
        if (MapLimitItemInstance.get().checkLimitItemByMapId(mapId, itemId)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private Boolean checkItemOnlyEquipped(Integer mapId, Integer itemId) {
        if (MapLimitItemInstance.get().checkLimitItemByMapId(mapId, itemId)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
