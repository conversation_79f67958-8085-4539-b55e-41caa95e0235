package com.lineage;

import java.sql.Connection;
import java.sql.SQLException;
import com.lineage.config.ConfigSQL;
import org.apache.commons.logging.LogFactory;
import com.mchange.v2.c3p0.ComboPooledDataSource;
import org.apache.commons.logging.Log;

public class DatabaseFactoryLogin {
	private static final Log _log;
	private static DatabaseFactoryLogin _instance;
	public ComboPooledDataSource _source; // 改為 public 以支持監控
	private static String _driver;
	private static String _url;
	private static String _user;
	private static String _password;

	static {
		_log = LogFactory.getLog(DatabaseFactoryLogin.class);
	}

	public static void setDatabaseSettings() {
		DatabaseFactoryLogin._driver = ConfigSQL.DB_DRIVER_LOGIN;
		DatabaseFactoryLogin._url = String.valueOf(ConfigSQL.DB_URL1_LOGIN) + ConfigSQL.DB_URL2_LOGIN
				+ ConfigSQL.DB_URL3_LOGIN;
		DatabaseFactoryLogin._user = ConfigSQL.DB_LOGIN_LOGIN;
		DatabaseFactoryLogin._password = ConfigSQL.DB_PASSWORD_LOGIN;
	}

	public DatabaseFactoryLogin() throws SQLException {
		try {
			(this._source = new ComboPooledDataSource()).setDriverClass(DatabaseFactoryLogin._driver);
			this._source.setJdbcUrl(DatabaseFactoryLogin._url);
			this._source.setUser(DatabaseFactoryLogin._user);
			this._source.setPassword(DatabaseFactoryLogin._password);

			// 優化連接池設定 - 與主資料庫保持一致
			this._source.setInitialPoolSize(3);
			this._source.setMinPoolSize(3);
			this._source.setMaxPoolSize(20); // 登入資料庫連接數較少
			this._source.setAcquireIncrement(2);
			this._source.setMaxIdleTime(1800); // 30分鐘
			this._source.setMaxConnectionAge(3600); // 1小時
			this._source.setTestConnectionOnCheckin(true);
			this._source.setTestConnectionOnCheckout(false);
			this._source.setIdleConnectionTestPeriod(300); // 5分鐘
			this._source.setPreferredTestQuery("SELECT 1");
			this._source.setMaxStatements(100);
			this._source.setMaxStatementsPerConnection(25);
			this._source.setAcquireRetryAttempts(3);
			this._source.setAcquireRetryDelay(1000);
			this._source.setCheckoutTimeout(30000); // 30秒
			this._source.setUnreturnedConnectionTimeout(300); // 5分鐘

			// 測試連接
			Connection testConn = this._source.getConnection();
			testConn.close();
			DatabaseFactoryLogin._log.info("登入資料庫連接池初始化成功");
		} catch (SQLException e) {
			DatabaseFactoryLogin._log.fatal("資料庫連接錯誤!", e);
			throw e;
		} catch (Exception e2) {
			DatabaseFactoryLogin._log.fatal("資料庫初始化錯誤!", e2);
			throw new SQLException("資料庫初始化失敗", e2);
		}
	}

	public void shutdown() {
		try {
			this._source.close();
		} catch (Exception e) {
			DatabaseFactoryLogin._log.error(e.getLocalizedMessage(), e);
		}
		try {
			this._source = null;
		} catch (Exception e) {
			DatabaseFactoryLogin._log.error(e.getLocalizedMessage(), e);
		}
	}

	public static DatabaseFactoryLogin get() throws SQLException {
		if (DatabaseFactoryLogin._instance == null) {
			DatabaseFactoryLogin._instance = new DatabaseFactoryLogin();
		}
		return DatabaseFactoryLogin._instance;
	}

	public Connection getConnection() {
		Connection con = null;
		while (con == null) {
			try {
				con = this._source.getConnection();
			} catch (SQLException e) {
				DatabaseFactoryLogin._log.error(e.getLocalizedMessage(), e);
			}
		}
		return con;
	}
}
