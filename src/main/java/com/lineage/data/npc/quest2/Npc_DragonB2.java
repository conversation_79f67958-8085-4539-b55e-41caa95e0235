package com.lineage.data.npc.quest2;

import com.lineage.server.thread.GeneralThreadPool;
import java.util.Iterator;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.utils.L1SpawnUtil;
import java.util.Random;
import java.util.TimerTask;
import com.lineage.server.templates.L1QuestUser;
import com.lineage.server.model.L1Teleport;
import com.lineage.server.model.L1Location;
import com.lineage.server.world.WorldQuest;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.HashMap;
import org.apache.commons.logging.LogFactory;
import java.util.Map;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.NpcExecutor;

public class Npc_DragonB2 extends NpcExecutor {
	private static final Log _log;
	public static final Map<Integer, checkDragonTimer2> _timer;

	static {
		_log = LogFactory.getLog(Npc_DragonB2.class);
		_timer = new HashMap();
	}

	public static NpcExecutor get() {
		return new Npc_DragonB2();
	}

	@Override
	public int type() {
		return 3;
	}

	@Override
	public void talk(final L1PcInstance pc, final L1NpcInstance npc) {
		try {
			final L1QuestUser quest = WorldQuest.get().get(pc.get_showId());
			if (quest != null) {
				if (!quest.is_SpawnedDragon()) {
					final checkDragonTimer2 timer = new checkDragonTimer2(npc.getMapId(), quest);
					timer.begin();
					quest.set_SpawnedDragon(true);
				}
				final L1Location loc = new L1Location(32990, 32842, npc.getMapId()).randomLocation(5, false);
				L1Teleport.teleport(pc, loc.getX(), loc.getY(), npc.getMapId(), 4, true);
			}
		} catch (Exception e) {
			Npc_DragonB2._log.error(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public void action(final L1PcInstance pc, final L1NpcInstance npc, final String cmd, final long amount) {
	}

	private class checkDragonTimer2 extends TimerTask {
		private final int mapId;
		private final L1QuestUser quest;

		public checkDragonTimer2(final int mapId, final L1QuestUser quest) {
			this.mapId = mapId;
			this.quest = quest;
		}

		@Override
		public void run() {
			this.cancel();
			try {
				this.sendServerMessage(1657);
				Thread.sleep(2000L);
				this.sendServerMessage(1658);
				Thread.sleep(2000L);
				this.sendServerMessage(1659);
				Thread.sleep(2000L);
				this.sendServerMessage(1660);
				final L1Location loc = new L1Location(32958, 32835, this.mapId).randomLocation(5, true);
				L1SpawnUtil.spawn(71026, loc, new Random().nextInt(8), this.quest.get_id());
			} catch (Exception ex) {
			} finally {
				Npc_DragonB2._timer.remove(Integer.valueOf(this.quest.get_id()));
			}
		}

		private final void sendServerMessage(final int msgid) {
			if (!this.quest.pcList().isEmpty()) {
				final Iterator<L1PcInstance> iterator = this.quest.pcList().iterator();
				while (iterator.hasNext()) {
					final L1PcInstance pc = iterator.next();
					pc.sendPackets(new S_ServerMessage(msgid));
				}
			}
		}

		public final void begin() {
			GeneralThreadPool.get().schedule(this, 30000L);
		}
	}
}
