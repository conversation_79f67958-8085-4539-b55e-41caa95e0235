package com.lineage.data.npc.mob;

import com.lineage.data.cmd.CreateNewItem;
import com.lineage.server.utils.CheckUtil;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.L1Character;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.NpcExecutor;

public class CKEW50_Gladiator extends NpcExecutor {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(CKEW50_Gladiator.class);
	}

	public static NpcExecutor get() {
		return new CKEW50_Gladiator();
	}

	@Override
	public int type() {
		return 8;
	}

	@Override
	public L1PcInstance death(final L1Character lastAttacker, final L1NpcInstance npc) {
		try {
			final L1PcInstance pc = CheckUtil.checkAtkPc(lastAttacker);
			if (pc != null) {
				if (pc.hasSkillEffect(4007)) {
					return pc;
				}
				if (pc.getInventory().checkItem(49166)) {
					return pc;
				}
				CreateNewItem.getQuestItem(pc, npc, 49166, 1L);
			}
			return pc;
		} catch (Exception e) {
			CKEW50_Gladiator._log.error(e.getLocalizedMessage(), e);
			return null;
		}
	}
}
