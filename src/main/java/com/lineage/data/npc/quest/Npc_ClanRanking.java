package com.lineage.data.npc.quest;

import java.util.Iterator;
import java.util.List;
import com.lineage.server.model.L1Clan;
import com.lineage.server.serverpackets.S_Message_YN;
import com.lineage.server.world.World;
import com.lineage.server.model.L1War;
import com.lineage.server.world.WorldWar;
import com.lineage.server.serverpackets.S_CloseList;
import com.lineage.server.world.WorldClan;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.timecontroller.event.ranking.RankingClanTimer;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.NpcExecutor;

public class Npc_ClanRanking extends NpcExecutor {
	private Npc_ClanRanking() {
	}

	public static NpcExecutor get() {
		return new Npc_ClanRanking();
	}

	@Override
	public int type() {
		return 3;
	}

	@Override
	public void talk(final L1PcInstance pc, final L1NpcInstance npc) {
		final String[] userName = RankingClanTimer.userName();
		final String[] out = new String[20];
		int i = 0;
		while (i < userName.length) {
			if (!userName[i].equals(" ")) {
				out[i] = String.valueOf(userName[i].replace(",", "(")) + ")";
				out[i + 10] = "對" + out[i] + "宣戰";
			}
			++i;
		}
		pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "y_c_1", out));
	}

	@Override
	public void action(final L1PcInstance pc, final L1NpcInstance npc, final String cmd, final long amount) {
		if (pc.getClanid() == 0) {
			pc.sendPackets(new S_ServerMessage(1064));
			return;
		}
		final L1Clan clan = pc.getClan();
		if (clan == null) {
			pc.sendPackets(new S_ServerMessage(1064));
			return;
		}
		if (!pc.isCrown()) {
			pc.sendPackets(new S_ServerMessage(478));
			return;
		}
		if (clan.getLeaderId() != pc.getId()) {
			pc.sendPackets(new S_ServerMessage(518));
			return;
		}
		final String[] clanNameList = RankingClanTimer.userName();
		int i = -1;
		if (cmd.matches("[0-9]+")) {
			i = Integer.valueOf(cmd).intValue();
		}
		if (i != -1) {
			final String tg = clanNameList[i];
			if (tg.equals(" ")) {
				return;
			}
			final String[] set = tg.split(",");
			final String tgClanName = set[0];
			final L1Clan clanX = WorldClan.get().getClan(tgClanName);
			if (clanX.getCastleId() != 0) {
				pc.sendPackets(new S_ServerMessage(166, "不能對傭有城堡的血盟宣戰!"));
				pc.sendPackets(new S_CloseList(pc.getId()));
				return;
			}
			if (clanX.getClanId() == pc.getClanid()) {
				return;
			}
			final List<L1War> warList = WorldWar.get().getWarList();
			final String clanName = clan.getClanName();
			final Iterator<L1War> iterator = warList.iterator();
			while (iterator.hasNext()) {
				final L1War war = iterator.next();
				if (!war.checkClanInSameWar(clanName, tgClanName)) {
					return;
				}
				if (war.checkClanInWar(tgClanName)) {
					pc.sendPackets(new S_ServerMessage(236, tgClanName));
					return;
				}
			}
			final L1PcInstance enemyLeader = World.get().getPlayer(clanX.getLeaderName());
			if (enemyLeader == null) {
				pc.sendPackets(new S_ServerMessage(218, tgClanName));
				return;
			}
			enemyLeader.setTempID(pc.getId());
			enemyLeader.sendPackets(new S_Message_YN(217, clanName, pc.getName()));
			pc.sendPackets(new S_CloseList(pc.getId()));
		}
	}
}
