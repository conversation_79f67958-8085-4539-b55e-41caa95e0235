package com.lineage.data.npc.quest;

import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.NpcExecutor;

public class Npc_Quest extends NpcExecutor {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(Npc_Quest.class);
	}

	public static NpcExecutor get() {
		return new Npc_Quest();
	}

	@Override
	public int type() {
		return 3;
	}

	@Override
	public void talk(final L1PcInstance pc, final L1NpcInstance npc) {
		try {
			pc.isWindows();
		} catch (Exception e) {
			Npc_Quest._log.error(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public void action(final L1PcInstance pc, final L1NpcInstance npc, final String cmd, final long amount) {
		pc.getAction().action(cmd, 0L);
	}
}
