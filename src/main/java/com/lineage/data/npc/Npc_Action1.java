package com.lineage.data.npc;

import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.world.World;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.serverpackets.S_CloseList;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import java.util.Date;
import java.text.SimpleDateFormat;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.NpcExecutor;

public class Npc_Action1 extends NpcExecutor {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(Npc_Action1.class);
	}

	public static NpcExecutor get() {
		return new Npc_Action1();
	}

	@Override
	public int type() {
		return 3;
	}

	@Override
	public void talk(final L1PcInstance pc, final L1NpcInstance npc) {
		try {
			final String nowDate = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
			final String[] info = { nowDate };
			pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "y_action_1", info));
		} catch (Exception e) {
			Npc_Action1._log.error(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public void action(final L1PcInstance pc, final L1NpcInstance npc, final String s, final long amount) {
		if (s.equalsIgnoreCase("y_action0")) {
			this.getScore(pc, 40308, 100000, 83054, 10);
		} else if (s.equalsIgnoreCase("y_action1")) {
			this.getScore(pc, 49138, 10, 83054, 10);
		} else if (s.equalsIgnoreCase("y_action2")) {
			this.getScore(pc, 44102, 10, 83054, 100);
		} else if (s.equalsIgnoreCase("y_action3")) {
			this.getScore(pc, 80280, 1, 83054, 1000);
		}
	}

	private void getScore(final L1PcInstance pc, final int toitemid, final int toitemcount, final int needitemid,
			final int needcount) {
		try {
			final L1ItemInstance toitem = ItemTable.get().createItem(toitemid);
			final L1ItemInstance needitem = pc.getInventory().checkItemX(needitemid, needcount);
			if (needitem == null) {
				pc.sendPackets(new S_ServerMessage(" \\fR材料不足!"));
				pc.sendPackets(new S_CloseList(pc.getId()));
				return;
			}
			if (toitem != null) {
				toitem.setCount(toitemcount);
				if (pc.getInventory().checkAddItem(toitem, toitemcount) == 0) {
					pc.getInventory().storeItem(toitem);
					pc.sendPackets(new S_ServerMessage(403, toitem.getLogName()));
					pc.getInventory().removeItem(needitem, needcount);
					this.toGmMsg2(pc, toitem);
				}
			}
			pc.sendPackets(new S_CloseList(pc.getId()));
		} catch (Exception e) {
			Npc_Action1._log.error(e.getLocalizedMessage(), e);
		}
	}

	private void toGmMsg2(final L1PcInstance pc, final L1ItemInstance element) {
		try {
			final Collection<L1PcInstance> allPc = World.get().getAllPlayers();
			final Iterator<L1PcInstance> iterator = allPc.iterator();
			while (iterator.hasNext()) {
				final L1PcInstance tgpc = iterator.next();
				if (tgpc.isGm()) {
					final StringBuilder topc = new StringBuilder();
					topc.append("人物:" + pc.getName() + " 兌換道具:" + element.getLogName());
					tgpc.sendPackets(new S_ServerMessage(166, topc.toString()));
				}
			}
		} catch (Exception e) {
			Npc_Action1._log.error(e.getLocalizedMessage(), e);
		}
	}
}
