package com.lineage.data.item_etcitem.teleport;

import com.lineage.server.model.map.L1Map;
import com.lineage.server.model.L1Teleport;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.map.L1WorldMap;
import com.lineage.server.datatables.ItemTeleportTable.TeleportList;
import com.lineage.server.timecontroller.server.ServerUseMapTimer;
import com.lineage.server.serverpackets.S_Paralysis;
import com.lineage.server.thread.GeneralThreadPool;
import com.lineage.server.model.skill.L1BuffUtil;
import java.util.Calendar;
import com.lineage.server.datatables.ItemTeleportTable;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.ItemExecutor;

public class SOR_UserSet extends ItemExecutor {
	private int type1;
	private static final Log _log;

	static {
		_log = LogFactory.getLog(SOR_UserSet.class);
	}

	public static ItemExecutor get() {
		return new SOR_UserSet();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		if (pc.hasSkillEffect(230)) {
			pc.sendPackets(new S_ServerMessage(1413));
			return;
		}
		if (pc.hasSkillEffect(4017)) {
			pc.sendPackets(new S_ServerMessage(1413));
			return;
		}
		if (this.type1 > 0 && pc.getMapId() != this.type1) {
			pc.sendPackets(new S_ServerMessage(1413));
			return;
		}
		final TeleportList list = ItemTeleportTable.get().getLoc(item.getItemId());
		if (list != null) {
			final int week = list.getWeek();
			final int starttime = list.getStarttime();
			final int unitytime = list.getUnitytime();
			final Calendar date = Calendar.getInstance();
			int nowWeek = date.get(7) - 1;
			final int nowHour = date.get(11);
			if (nowWeek == 0) {
				nowWeek += 7;
			}
			if (week > 0 && starttime < 0 && unitytime < 0) {
				if (week != nowWeek) {
					pc.sendPackets(new S_ServerMessage(166, "使用時間限制:星期【" + week + " 】"));
					return;
				}
			} else if (week > 0 && starttime >= 0 && unitytime >= 0) {
				if (week != nowWeek || nowHour < starttime || nowHour >= unitytime) {
					pc.sendPackets(new S_ServerMessage(166,
							"使用時間限制:星期【 " + week + " 】時間為【 " + starttime + " 】點，至【 " + unitytime + "】點"));
					return;
				}
			} else if (week < 0 && starttime >= 0 && unitytime >= 0 && (nowHour < starttime || nowHour >= unitytime)) {
				pc.sendPackets(new S_ServerMessage(166, "使用時間限制:時間為【 " + starttime + " 】點，至【 " + unitytime + "】點"));
				return;
			}
			final int locX = list.getLocX();
			final int locY = list.getLocY();
			final short mapId = list.getMapId();
			if (pc.getMap().isEscapable()) {
				if (pc.isActived()) {
					pc.setActived(false);
					pc.killSkillEffectTimer(8853);
					pc.sendPackets(new S_ServerMessage("掛機中請勿使用手動卷軸。"));
					pc.sendPackets(new S_ServerMessage("自動狩獵已停止。"));
					if (pc.get_fwgj() > 0) {
						pc.setlslocx(0);
						pc.setlslocy(0);
						pc.set_fwgj(0);
					}
				}
				pc.getInventory().removeItem(item, 1L);
				L1BuffUtil.cancelAbsoluteBarrier(pc);
				final TeleportRunnable runnable = new TeleportRunnable(pc, locX, locY, mapId);
				GeneralThreadPool.get().schedule(runnable, 0L);
			} else {
				pc.sendPackets(new S_ServerMessage(276));
				pc.sendPackets(new S_Paralysis(7, false));
			}
			try {
				Thread.sleep(1500L);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			final int time = list.getTime();
			if (time > 0) {
				pc.get_other().set_usemap(mapId);
				ServerUseMapTimer.put(pc, time);
				pc.sendPackets(new S_ServerMessage("使用時間限制:" + time + "秒"));
			}
		}
	}

	@Override
	public void set_set(final String[] set) {
		try {
			this.type1 = Integer.parseInt(set[1]);
		} catch (Exception ex) {
		}
	}

	private class TeleportRunnable implements Runnable {
		private final L1PcInstance _pc;
		private int _locX;
		private int _locY;
		private int _mapid;

		public TeleportRunnable(final L1PcInstance pc, final int x, final int y, final int mapid) {
			this._locX = 0;
			this._locY = 0;
			this._mapid = 0;
			this._pc = pc;
			this._locX = x;
			this._locY = y;
			this._mapid = mapid;
		}

		@Override
		public void run() {
			try {
				final L1Map map = L1WorldMap.get().getMap((short) this._mapid);
				final int r = 10;
				int tryCount = 0;
				int newX = this._locX;
				int newY = this._locY;
				do {
					++tryCount;
					newX = this._locX + (int) (Math.random() * r) - (int) (Math.random() * r);
					newY = this._locY + (int) (Math.random() * r) - (int) (Math.random() * r);
					if (map.isPassable(newX, newY, this._pc)) {
						break;
					}
					Thread.sleep(1L);
				} while (tryCount < 5);
				if (tryCount >= 5) {
					L1Teleport.teleport(this._pc, this._locX, this._locY, (short) this._mapid, this._pc.getHeading(),
							true);
				} else {
					L1Teleport.teleport(this._pc, newX, newY, (short) this._mapid, this._pc.getHeading(), true);
				}
			} catch (InterruptedException e) {
				SOR_UserSet._log.error(e.getLocalizedMessage(), e);
			}
		}
	}
}
