package com.lineage.data.item_etcitem.magicreel;

import com.lineage.server.model.skill.L1SkillUse;
import com.lineage.server.model.skill.L1BuffUtil;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class MagicReel_Short extends ItemExecutor {
	private int _skillid;
	private int _consume;

	public MagicReel_Short() {
		this._skillid = 0;
		this._consume = 1;
	}

	public static ItemExecutor get() {
		return new MagicReel_Short();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		if (pc == null) {
			return;
		}
		if (item == null) {
			return;
		}
		if (pc.isInvisble() || pc.isInvisDelay()) {
			pc.sendPackets(new S_ServerMessage(281));
			return;
		}
		final int targetID = data[0];
		final int spellsc_x = data[1];
		final int spellsc_y = data[2];
		if (targetID == pc.getId() || targetID == 0) {
			pc.sendPackets(new S_ServerMessage(281));
			return;
		}
		pc.getInventory().removeItem(item, this._consume);
		L1BuffUtil.cancelAbsoluteBarrier(pc);
		final L1SkillUse l1skilluse = new L1SkillUse();
		l1skilluse.handleCommands(pc, this._skillid, targetID, spellsc_x, spellsc_y, 0, 2);
	}

	@Override
	public void set_set(final String[] set) {
		try {
			this._skillid = Integer.parseInt(set[1]);
		} catch (Exception ex) {
		}
		try {
			this._consume = Integer.parseInt(set[2]);
		} catch (Exception ex2) {
		}
	}
}
