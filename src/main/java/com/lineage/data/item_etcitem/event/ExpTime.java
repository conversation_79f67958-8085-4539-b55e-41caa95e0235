package com.lineage.data.item_etcitem.event;

import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.skill.L1BuffUtil;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class ExpTime extends ItemExecutor {
	public static ItemExecutor get() {
		return new ExpTime();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		if (item == null) {
			return;
		}
		if (pc == null) {
			return;
		}
		if (L1BuffUtil.cancelExpSkill(pc)) {
			pc.sendPackets(new S_ServerMessage("\\fX目前沒有第一段經驗藥水的效果。"));
		}
		if (L1BuffUtil.cancelExpSkill_2(pc)) {
			pc.sendPackets(new S_ServerMessage("\\fY目前沒有第二段神力藥水的效果。"));
		}
	}
}
