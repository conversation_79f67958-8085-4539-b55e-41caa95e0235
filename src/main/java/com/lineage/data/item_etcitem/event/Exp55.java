package com.lineage.data.item_etcitem.event;

import com.lineage.server.serverpackets.S_PacketBoxCooking;
import com.lineage.server.serverpackets.S_SkillSound;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.skill.L1BuffUtil;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Exp55 extends ItemExecutor {
	public static ItemExecutor get() {
		return new Exp55();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		if (item == null) {
			return;
		}
		if (pc == null) {
			return;
		}
		if (L1BuffUtil.cancelExpSkill(pc)) {
			final int time = 600;
			pc.setSkillEffect(6676, time * 1000);
			pc.getInventory().removeItem(item, 1L);
			pc.sendPackets(new S_ServerMessage("第一段經驗值提升550%(600秒)"));
			pc.sendPackets(new S_SkillSound(pc.getId(), 750));
			pc.sendPackets(new S_PacketBoxCooking(pc, 32, time));
		}
	}
}
