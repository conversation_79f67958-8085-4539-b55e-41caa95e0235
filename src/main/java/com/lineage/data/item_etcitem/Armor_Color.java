package com.lineage.data.item_etcitem;

import com.lineage.server.serverpackets.S_ItemStatus;
import com.lineage.william.drop_type_armor_clean;
import com.lineage.william.drop_type_weapon_clean;
import com.lineage.william.drop_type_armor_item;
import com.lineage.william.drop_type_weapon_item;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Armor_Color extends ItemExecutor {
	public static ItemExecutor get() {
		return new Armor_Color();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int targObjId = data[0];
		final L1ItemInstance tgItem = pc.getInventory().getItem(targObjId);
		if (tgItem == null) {
			return;
		}
		if (!tgItem.getItem().isdropcolor()) {
			pc.sendPackets(new S_SystemMessage("似乎沒發生什麼事情。"));
			return;
		}
		if (tgItem.getItemArmorType() == 0) {
			if (tgItem.getItem().getType2() == 1) {
				drop_type_weapon_item.forIntensifyArmor(pc, tgItem);
				pc.getInventory().removeItem(item, 1L);
			} else if (tgItem.getItem().getType2() == 2) {
				drop_type_armor_item.forIntensifyArmor(pc, tgItem);
				pc.getInventory().removeItem(item, 1L);
			} else {
				pc.sendPackets(new S_SystemMessage("似乎沒發生什麼事情。"));
			}
		} else if (tgItem.getItemArmorType() > 0) {
			if (tgItem.getItem().getType2() == 1) {
				drop_type_weapon_clean.forIntensifyArmor(pc, tgItem);
				pc.getInventory().removeItem(item, 1L);
			} else if (tgItem.getItem().getType2() == 2) {
				drop_type_armor_clean.forIntensifyArmor(pc, tgItem);
				pc.getInventory().removeItem(item, 1L);
			} else {
				pc.sendPackets(new S_SystemMessage("似乎沒發生什麼事情。"));
			}
		}
		pc.sendPackets(new S_ItemStatus(tgItem));
		pc.getInventory().updateItem(tgItem, 4);
		pc.getInventory().saveItem(tgItem, 4);
	}
}
