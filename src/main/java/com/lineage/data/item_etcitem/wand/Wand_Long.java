package com.lineage.data.item_etcitem.wand;

import com.lineage.data.executor.ItemExecutor;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.skill.L1BuffUtil;
import com.lineage.server.model.skill.L1SkillUse;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.templates.L1Item;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class Wand_Long extends ItemExecutor {
    private static final Log _log;

    static {
        _log = LogFactory.getLog(Wand_Long.class);
    }

    private boolean _isCrown;
    private boolean _isKnight;
    private boolean _isElf;
    private boolean _isWizard;
    private boolean _isDarkelf;
    private boolean _isDragonKnight;
    private boolean _isIllusionist;
    private int _skillid;
    private int _needItem;
    private int _needItemCount;

    public Wand_Long() {
        _skillid = 0;
    }

    public static ItemExecutor get() {
        return new Wand_Long();
    }

    @Override
    public void execute(int[] data, L1PcInstance pc, L1ItemInstance item) {
        if (pc == null) {
            return;
        }
        if (item == null) {
            return;
        }
        if (pc.isInvisble() || pc.isInvisDelay()) {
            pc.sendPackets(new S_ServerMessage(281));
            return;
        }
        if (pc.hasSkillEffect(9732)) {
            return;
        }
        int targetID = data[0];
        int spellsc_x = data[1];
        int spellsc_y = data[2];
        if (targetID == pc.getId() || targetID == 0) {
            pc.sendPackets(new S_ServerMessage(281));
            return;
        }
        L1Item _needItemmsg = ItemTable.get().getTemplate(_needItem);
        if (check(pc)) {
            //Kevin 新增使用需要扣除介質
            /*if (_needItem > 0) {
                if (!pc.getInventory().consumeItem(_needItem, _needItemCount)) {
                    pc.sendPackets(new S_SystemMessage("【" + _needItemmsg.getName() + "】不足，需要【" + _needItemCount + "】個才能使用"));
                    return;
                } else {
                    pc.sendPackets(new S_ServerMessage("使用特殊卷軸，扣除【" + _needItemmsg.getName() + "】：" + _needItemCount));

                }
            }*/
            L1BuffUtil.cancelAbsoluteBarrier(pc);
            L1SkillUse l1skilluse = new L1SkillUse();
            l1skilluse.handleCommands(pc, _skillid, targetID, spellsc_x, spellsc_y, 0, 0);
            pc.setSkillEffect(9732, 30);
        } else {
            pc.sendPackets(new S_ServerMessage(264));
        }
    }

    private boolean check(L1PcInstance pc) {
        try {
            if (pc.isCrown() && _isCrown) {
                return true;
            }
            if (pc.isKnight() && _isKnight) {
                return true;
            }
            if (pc.isElf() && _isElf) {
                return true;
            }
            if (pc.isWizard() && _isWizard) {
                return true;
            }
            if (pc.isDarkelf() && _isDarkelf) {
                return true;
            }
            if (pc.isDragonKnight() && _isDragonKnight) {
                return true;
            }
            if (pc.isIllusionist() && _isIllusionist) {
                return true;
            }
        } catch (Exception e) {
            Wand_Long._log.error(e.getLocalizedMessage(), e);
        }
        return false;
    }

    private void set_use_type(int use_type) {
        try {
            if (use_type >= 64) {
                use_type -= 64;
                _isIllusionist = true;
            }
            if (use_type >= 32) {
                use_type -= 32;
                _isDragonKnight = true;
            }
            if (use_type >= 16) {
                use_type -= 16;
                _isDarkelf = true;
            }
            if (use_type >= 8) {
                use_type -= 8;
                _isWizard = true;
            }
            if (use_type >= 4) {
                use_type -= 4;
                _isElf = true;
            }
            if (use_type >= 2) {
                use_type -= 2;
                _isKnight = true;
            }
            if (use_type >= 1) {
                --use_type;
                _isCrown = true;
            }
            if (use_type > 0) {
                Wand_Long._log.error("Wand_Long2 可執行職業設定錯誤:餘數大於0");
            }
        } catch (Exception e) {
            Wand_Long._log.error(e.getLocalizedMessage(), e);
        }
    }

    @Override
    public void set_set(String[] set) {
        try {
            _skillid = Integer.parseInt(set[1]);
        } catch (Exception ex) {
        }
        try {
            int user_type = Integer.parseInt(set[2]);
            set_use_type(user_type);
        } catch (Exception ex2) {
        }
        /*try {
            _needItem = Integer.parseInt(set[3]);
            _needItemCount = Integer.parseInt(set[4]);
        } catch (Exception ex3) {

        }*/
    }
}
