package com.lineage.data.item_etcitem.wand;

import java.util.Iterator;
import java.util.ArrayList;
import com.lineage.server.serverpackets.S_SkillSound;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1MonsterInstance;
import com.lineage.server.model.L1Object;
import com.lineage.server.world.World;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_DoActionGFX;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Firestorm_Magic_Wand extends ItemExecutor {
	public static ItemExecutor get() {
		return new Firestorm_Magic_Wand();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		if (item.getChargeCount() == 0) {
			pc.getInventory().deleteItem(item);
			return;
		}
		if (pc.getMapId() != 1936) {
			L1ItemInstance[] itemlist = pc.getInventory().findItemsId(92142);
			int i = 0;
			while (i < itemlist.length) {
				pc.getInventory().removeItem(itemlist[i]);
				++i;
			}
			itemlist = pc.getInventory().findItemsId(92143);
			i = 0;
			while (i < itemlist.length) {
				pc.getInventory().removeItem(itemlist[i]);
				++i;
			}
			return;
		}
		pc.sendPacketsX8(new S_DoActionGFX(pc.getId(), 17));
		final int useCount = item.getChargeCount() - 1;
		if (useCount > 0) {
			item.setChargeCount(useCount);
			pc.getInventory().updateItem(item, 128);
		} else {
			pc.getInventory().deleteItem(item);
		}
		final ArrayList<L1Object> list = World.get().getVisibleObjects(pc, 5);
		if (list == null) {
			return;
		}
		final Iterator<L1Object> iterator = list.iterator();
		while (iterator.hasNext()) {
			final L1Object object = iterator.next();
			if (object instanceof L1MonsterInstance) {
				final L1MonsterInstance dota = (L1MonsterInstance) object;
				if ((dota.getNpcId() < 95131 || dota.getNpcId() > 95143) && dota.getNpcId() != 95145) {
					continue;
				}
				dota.broadcastPacketAll(new S_DoActionGFX(dota.getId(), 2));
				dota.receiveDamage(pc, 250);
			}
		}
		pc.sendPacketsAll(new S_SkillSound(pc.getId(), 1819));
	}
}
