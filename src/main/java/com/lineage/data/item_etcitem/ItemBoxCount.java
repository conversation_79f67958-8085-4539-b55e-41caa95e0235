package com.lineage.data.item_etcitem;

import com.lineage.william.ItemBox;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class ItemBoxCount extends ItemExecutor {
	private ItemBoxCount() {
	}

	public static ItemExecutor get() {
		return new ItemBoxCount();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		if (item == null) {
			return;
		}
		if (pc == null) {
			return;
		}
		ItemBox.trueOutburst(pc, item);
	}
}
