package com.lineage.data.item_etcitem.shop;

import com.lineage.server.serverpackets.S_Message_YN;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.ItemExecutor;

public class UserName extends ItemExecutor {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(UserName.class);
	}

	public static ItemExecutor get() {
		return new UserName();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		if (item == null) {
			return;
		}
		if (pc == null) {
			return;
		}
		if (pc.isGhost()) {
			return;
		}
		if (pc.isDead()) {
			return;
		}
		if (pc.isTeleport()) {
			return;
		}
		if (pc.getLawful() < 32767) {
			pc.sendPackets(new S_ServerMessage("\\fT正義值必須為32767才可以使用"));
			return;
		}
		if (pc.isPrivateShop()) {
			pc.sendPackets(new S_ServerMessage("\\fT請先結束商店村模式!"));
			return;
		}
		final Object[] petList = pc.getPetList().values().toArray();
		if (petList.length > 0) {
			pc.sendPackets(new S_ServerMessage("\\fT請先回收寵物!"));
			return;
		}
		if (pc.getHierarchs() != null) {
			pc.sendPackets(new S_ServerMessage("\\fT請先回收祭司!"));
			return;
		}
		if (!pc.getDolls().isEmpty()) {
			pc.sendPackets(new S_ServerMessage("\\fT請先回收魔法娃娃!"));
			return;
		}
		if (pc.getParty() != null) {
			pc.sendPackets(new S_ServerMessage("\\fT請先退出隊伍!"));
			return;
		}
		if (pc.getClanid() != 0) {
			pc.sendPackets(new S_ServerMessage("\\fT請先退出血盟!"));
			return;
		}
		try {
			pc.sendPackets(new S_Message_YN(325));
			pc.rename(true);
		} catch (Exception e) {
			UserName._log.error(e.getLocalizedMessage(), e);
		}
	}
}
