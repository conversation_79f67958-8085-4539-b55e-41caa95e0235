package com.lineage.data.item_etcitem;

import com.lineage.config.ConfigOther;
import com.lineage.data.executor.ItemExecutor;
import com.lineage.data.item_etcitem.allcall.AllCall;
import com.lineage.server.datatables.MapsTable;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1CastleLocation;
import com.lineage.server.model.L1Party;
import com.lineage.server.serverpackets.S_PacketBoxGree;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.S_SkillSound;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.world.World;

import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class AllCall_party extends AllCall {
    private int type = 0;

    // 丹丹 <PERSON> classname 設置為0
    // 畫面上有，不同血盟成員，可以使用穿雲箭
    // 畫面上有，沒有血盟的人，可以使用穿雲箭
    private AllCall_party() {
    }

    public static ItemExecutor get() {
        return new AllCall_party();
    }

    @Override
    public void execute(int[] data, L1PcInstance pc, L1ItemInstance item) {
        L1Party party = pc.getParty();
        if (!pc.isInParty()) {
            pc.sendPackets(new S_ServerMessage("尚未有組隊無法使用此物品"));
            return;
        }
        if (L1CastleLocation.checkInAllWarArea(pc.getX(), pc.getY(), pc.getMapId())) {
            pc.sendPackets(new S_ServerMessage("旗子內禁止使用"));
            return;
        }
        if (!pc.getMap().isPartyPc()) {
            pc.sendPackets(new S_ServerMessage("所在地圖無法進行傳送"));
            return;
        }
        //Kevin 新增以下技能狀態下無法使用穿雲箭
        if (checkPcHasSkillEffect(pc)) {
            return;
        }

        ConcurrentHashMap<Integer, L1PcInstance> pcs = party.partyUsers();
        if (pcs.isEmpty()) {
            return;
        }
        if (pcs.size() <= 0) {
            return;
        }
        if (!pc.isInParty()) {
            return;
        }
        if (type == 1) {
            // 丹丹 Kevin Etcitem classname 設置為1
            // 畫面上有，不同血盟成員，無法使用穿雲箭
            // 畫面上有，沒有血盟的人，無法使用穿雲箭
            List<L1PcInstance> players = World.get().getRecognizePlayer(pc);
            for (L1PcInstance otherPlayer : players) {

                if (otherPlayer.getClanid() != pc.getClanid()) {
                    pc.sendPackets(new S_ServerMessage("有其他血盟成員，無法使用穿雲箭"));
                    pc.sendPackets(new S_ServerMessage("請到沒有人的地方使用"));
                    return;
                }

            }
        }
        if (type == 2) {
            // 丹丹 Kevin Etcitem classname 設置為2
            // 畫面上有，不同血盟成員，無法使用穿雲箭
            // 畫面上有，沒有血盟的人，可以使用穿雲箭
            List<L1PcInstance> players = World.get().getRecognizePlayer(pc);
            for (L1PcInstance otherPlayer : players) {
                if (otherPlayer.getClanid() != 0) { //Kevin 沒有血盟的成員不受此限制
                    if (otherPlayer.getClanid() != pc.getClanid()) {
                        pc.sendPackets(new S_ServerMessage("有其他血盟成員，無法使用穿雲箭"));
                        pc.sendPackets(new S_ServerMessage("請到沒有人的地方使用"));
                        return;
                    }
                }
            }
        }
        if (!pc.getInventory().checkItem(ConfigOther.Call_party_itemid, ConfigOther.Call_party_count)) {
            pc.sendPacketsAll(new S_SystemMessage(ConfigOther.clanmsg5));
            return;
        }
        pc.getInventory().consumeItem(ConfigOther.Call_party_itemid, ConfigOther.Call_party_count);
        pc.sendPackets(new S_SkillSound(pc.getId(), 2047));

        Iterator<L1PcInstance> iterator = pcs.values().iterator();
        while (iterator.hasNext()) {
            L1PcInstance pc2 = iterator.next();
            if (pc.isInParty() && pc.getParty().isMember(pc2)) {
                if (pc2.getId() == pc.getId()) {
                    continue;
                }
                if (checkPcHasSkillEffect(pc2)) {
                    continue;
                }

                pc2.setcallclanal(pc.getId());
                teleport_AllCall(pc2, pc, 2);
            }
        }
        World.get().broadcastPacketToAll(new S_SystemMessage(
                String.format(ConfigOther.partymsg, pc.getName(), MapsTable.get().getMapName(pc.getMapId()),item.getItem().getName())));
        World.get().broadcastPacketToAll(new S_PacketBoxGree(2,
                String.format(ConfigOther.partymsg, pc.getName(), MapsTable.get().getMapName(pc.getMapId()),item.getItem().getName())));
    }

    @Override
    public void set_set(String[] set) {
        try {
            type = Integer.parseInt(set[1]);
        } catch (Exception ex) {
        }
    }
}
