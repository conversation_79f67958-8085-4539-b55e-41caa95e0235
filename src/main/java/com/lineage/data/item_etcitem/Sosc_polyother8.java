package com.lineage.data.item_etcitem;

import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ShowPolyList;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Sosc_polyother8 extends ItemExecutor {
	public static ItemExecutor get() {
		return new Sosc_polyother8();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		pc.sendPackets(new S_ShowPolyList(pc.getId(), "venike_poly8"));
		if (!pc.isItemPoly()) {
			pc.setSummonMonster(false);
			pc.setItemPoly(true);
			pc.setPolyScroll(item);
		}
	}
}
