package com.lineage.data.item_etcitem.poweritem;

import com.lineage.server.serverpackets.S_ItemName;
import com.lineage.server.serverpackets.S_ItemStatus;
import com.lineage.server.datatables.lock.CharItemPowerReading;
import com.lineage.server.templates.L1ItemPower_name;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Reset_Hole1 extends ItemExecutor {
	private Reset_Hole1() {
	}

	public static ItemExecutor get() {
		return new Reset_Hole1();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int targObjId = data[0];
		final L1ItemInstance tgItem = pc.getInventory().getItem(targObjId);
		if (tgItem == null) {
			return;
		}
		if (tgItem.isEquipped()) {
			pc.sendPackets(new S_ServerMessage("\\fR你必須先解除物品裝備!"));
			return;
		}
		pc.getInventory().removeItem(item, 1L);
		L1ItemPower_name power = null;
		boolean update = false;
		int count = 0;
		switch (tgItem.getItem().getUseType()) {
		case 1: {
			count = 1;
			if (tgItem.get_power_name() != null) {
				power = tgItem.get_power_name();
				update = true;
				break;
			}
			power = new L1ItemPower_name();
			break;
		}
		case 2:
		case 18:
		case 19:
		case 20:
		case 21:
		case 22:
		case 25: {
			count = 1;
			if (tgItem.get_power_name() != null) {
				power = tgItem.get_power_name();
				update = true;
				break;
			}
			power = new L1ItemPower_name();
			break;
		}
		}
		if (power != null) {
			power.set_item_obj_id(tgItem.getId());
			power.set_hole_count(count);
			power.set_hole_1(0);
			tgItem.set_power_name(power);
			if (update) {
				CharItemPowerReading.get().updateItem(tgItem.getId(), tgItem.get_power_name());
				pc.sendPackets(new S_ItemStatus(tgItem));
				pc.sendPackets(new S_ItemName(tgItem));
			} else {
				pc.sendPackets(new S_ItemStatus(tgItem));
				pc.sendPackets(new S_ItemName(tgItem));
				CharItemPowerReading.get().storeItem(tgItem.getId(), tgItem.get_power_name());
			}
			pc.sendPackets(new S_ItemName(tgItem));
		}
	}
}
