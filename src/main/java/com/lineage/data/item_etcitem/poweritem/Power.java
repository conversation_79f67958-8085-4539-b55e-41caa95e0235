package com.lineage.data.item_etcitem.poweritem;

import com.lineage.data.event.PowerItemSet;
import com.lineage.data.executor.ItemExecutor;
import com.lineage.server.datatables.PowerItemTable;
import com.lineage.server.datatables.lock.CharItemPowerReading;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_ItemName;
import com.lineage.server.serverpackets.S_ItemStatus;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.templates.L1ItemPower_name;
import com.lineage.server.utils.log.PlayerLogUtil;
import com.lineage.william.L1PowerItem;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Timestamp;
import java.util.Random;

public class Power extends ItemExecutor {
	private static final Log _log;
	private static final Random _random;

	static {
		_log = LogFactory.getLog(Power.class);
		_random = new Random();
	}

	public static ItemExecutor get() {
		return new Power();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		try {
			final int targObjId = data[0];
			final L1ItemInstance tgItem = pc.getInventory().getItem(targObjId);
			if (tgItem == null) {
				return;
			}
			if (tgItem.get_power_name() == null) {
				pc.sendPackets(new S_ServerMessage("\\fT這個物品沒有凹槽!"));
				return;
			}
			if (tgItem.isEquipped()) {
				pc.sendPackets(new S_ServerMessage("\\fR你必須先解除物品裝備!"));
				return;
			}
			int random = PowerItemSet.HOLER;
			if (pc.isGm()) {
				random = 1000;
			}
			final L1ItemPower_name power = tgItem.get_power_name();
			final int use_type = tgItem.getItem().getUseType();
			final L1PowerItem power_item = PowerItemTable.get().getItem(item.getItemId());
			if (power_item != null) {
				boolean isErr = false;
				if (power_item.type.equalsIgnoreCase("Weapon")) {
					if (use_type == 1) {
						isErr = true;
					}
				} else if (power_item.type.equalsIgnoreCase("Armor")) {
					switch (use_type) {
					case 2:
					case 18:
					case 19:
					case 20:
					case 21:
					case 22:
					case 25: {
						isErr = true;
						break;
					}
					}
				} else if (power_item.type.equalsIgnoreCase("All")) {
					isErr = true;
				}
				if (!isErr) {
					pc.sendPackets(new S_ServerMessage(79));
					return;
				}
			}
			int powercount = 0;
			int i = 0;
			while (i < power.get_hole_count()) {
				if (item.getItemId() == power.get_hole(i)) {
					++powercount;
				}
				++i;
			}
			if (powercount >= power_item.powercount) {
				pc.sendPackets(new S_ServerMessage(
						"\\fT同一道具允許置入" + item.getItem().getName() + "(" + power_item.powercount + ")個!"));
				return;
			}
			if (power.get_hole_1() == 0 && power.get_hole_count() >= 1) {
				pc.getInventory().removeItem(item, 1L);
				if (Power._random.nextInt(1000) > random) {
					pc.sendPackets(new S_ServerMessage("\\fT凹槽置入魔法物品失敗!"));
					return;
				}
				power.set_hole_1(item.getItemId());
				pc.sendPackets(new S_ItemStatus(tgItem));
				pc.sendPackets(new S_ItemName(tgItem));
				CharItemPowerReading.get().updateItem(tgItem.getId(), tgItem.get_power_name());
				打洞水晶放入成功("玩家 :" + pc.getName() + "||" + item.getName() + "成功放入.第1洞 ,時間:"
						+ new Timestamp(System.currentTimeMillis()) + ")");
			} else if (power.get_hole_2() == 0 && power.get_hole_count() >= 2) {
				pc.getInventory().removeItem(item, 1L);
				if (Power._random.nextInt(1000) > random) {
					pc.sendPackets(new S_ServerMessage("\\fT凹槽置入魔法物品失敗!"));
					return;
				}
				power.set_hole_2(item.getItemId());
				pc.sendPackets(new S_ItemStatus(tgItem));
				pc.sendPackets(new S_ItemName(tgItem));
				CharItemPowerReading.get().updateItem(tgItem.getId(), tgItem.get_power_name());
				打洞水晶放入成功("玩家 :" + pc.getName() + "||" + item.getName() + "成功放入.第2洞 ,時間:"
						+ new Timestamp(System.currentTimeMillis()) + ")");
			} else if (power.get_hole_3() == 0 && power.get_hole_count() >= 3) {
				pc.getInventory().removeItem(item, 1L);
				if (Power._random.nextInt(1000) > random) {
					pc.sendPackets(new S_ServerMessage("\\fT凹槽置入魔法物品失敗!"));
					return;
				}
				power.set_hole_3(item.getItemId());
				pc.sendPackets(new S_ItemStatus(tgItem));
				pc.sendPackets(new S_ItemName(tgItem));
				CharItemPowerReading.get().updateItem(tgItem.getId(), tgItem.get_power_name());
				打洞水晶放入成功("玩家 :" + pc.getName() + "||" + item.getName() + "成功放入.第3洞 ,時間:"
						+ new Timestamp(System.currentTimeMillis()) + ")");
			} else if (power.get_hole_4() == 0 && power.get_hole_count() >= 4) {
				pc.getInventory().removeItem(item, 1L);
				if (Power._random.nextInt(1000) > random) {
					pc.sendPackets(new S_ServerMessage("\\fT凹槽置入魔法物品失敗!"));
					return;
				}
				power.set_hole_4(item.getItemId());
				pc.sendPackets(new S_ItemStatus(tgItem));
				pc.sendPackets(new S_ItemName(tgItem));
				CharItemPowerReading.get().updateItem(tgItem.getId(), tgItem.get_power_name());
				打洞水晶放入成功("玩家 :" + pc.getName() + "||" + item.getName() + "成功放入.第4洞 ,時間:"
						+ new Timestamp(System.currentTimeMillis()) + ")");
			} else if (power.get_hole_5() == 0 && power.get_hole_count() >= 5) {
				pc.getInventory().removeItem(item, 1L);
				if (Power._random.nextInt(1000) > random) {
					pc.sendPackets(new S_ServerMessage("\\fT凹槽置入魔法物品失敗!"));
					return;
				}
				power.set_hole_5(item.getItemId());
				pc.sendPackets(new S_ItemStatus(tgItem));
				pc.sendPackets(new S_ItemName(tgItem));
				CharItemPowerReading.get().updateItem(tgItem.getId(), tgItem.get_power_name());
				打洞水晶放入成功("玩家 :" + pc.getName() + "||" + item.getName() + "成功放入.第5洞 ,時間:"
						+ new Timestamp(System.currentTimeMillis()) + ")");
			} else {
				pc.sendPackets(new S_ServerMessage("\\fT這個物品沒有足夠凹槽!"));
			}
		} catch (Exception e) {
			Power._log.error(e.getLocalizedMessage(), e);
		}
	}

	public static void 打洞水晶放入成功(final String info) {
		PlayerLogUtil.writeLog("打洞水晶放入成功", info);
//		try {
//			final BufferedWriter out = new BufferedWriter(new FileWriter("./玩家紀錄/打洞水晶放入成功.txt", true));
//			out.write(String.valueOf(info) + "\r\n");
//			out.close();
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
	}
}
