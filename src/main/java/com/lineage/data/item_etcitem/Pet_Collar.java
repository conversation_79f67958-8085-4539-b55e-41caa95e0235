package com.lineage.data.item_etcitem;

import com.lineage.server.templates.L1Npc;
import com.lineage.server.templates.L1Pet;
import com.lineage.server.datatables.NpcTable;
import com.lineage.server.datatables.lock.PetReading;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PetInstance;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Pet_Collar extends ItemExecutor {
	public static ItemExecutor get() {
		return new Pet_Collar();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		if (pc.getInventory().checkItem(41160)) {
			if (this.withdrawPet(pc, item.getId())) {
				pc.getInventory().consumeItem(41160, 1L);
			}
		} else {
			pc.sendPackets(new S_ServerMessage(79));
		}
	}

	private boolean withdrawPet(final L1PcInstance pc, final int itemObjectId) {
		if (!pc.getMap().isTakePets()) {
			pc.sendPackets(new S_ServerMessage(563));
			return false;
		}
		int petCost = 0;
		int petCount = 0;
		int divisor = 6;
		final Object[] petList = pc.getPetList().values().toArray();
		final Object[] array;
		final int length = (array = petList).length;
		int i = 0;
		while (i < length) {
			final Object pet = array[i];
			if (pet instanceof L1PetInstance && ((L1PetInstance) pet).getItemObjId() == itemObjectId) {
				return false;
			}
			petCost += ((L1NpcInstance) pet).getPetcost();
			++i;
		}
		int charisma = pc.getCha();
		if (pc.isCrown()) {
			charisma += 6;
		} else if (pc.isElf()) {
			charisma += 12;
		} else if (pc.isWizard()) {
			charisma += 6;
		} else if (pc.isDarkelf()) {
			charisma += 6;
		} else if (pc.isDragonKnight()) {
			charisma += 6;
		} else if (pc.isIllusionist()) {
			charisma += 6;
		}
		final L1Pet l1pet = PetReading.get().getTemplate(itemObjectId);
		if (l1pet != null) {
			final int npcId = l1pet.get_npcid();
			charisma -= petCost;
			if (npcId == 45313 || npcId == 45710 || npcId == 45711 || npcId == 45712) {
				divisor = 12;
			} else {
				divisor = 6;
			}
			petCount = charisma / divisor;
			if (petCount <= 0) {
				pc.sendPackets(new S_ServerMessage(489));
				return false;
			}
			final L1Npc npcTemp = NpcTable.get().getTemplate(l1pet.get_npcid());
			final L1PetInstance pet2 = new L1PetInstance(npcTemp, pc, l1pet);
			pet2.setPetcost(divisor);
		}
		return true;
	}
}
