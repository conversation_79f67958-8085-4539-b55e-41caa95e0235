package com.lineage.data.item_etcitem.furniture;

import java.util.Iterator;
import com.lineage.server.datatables.lock.FurnitureSpawnReading;
import com.lineage.server.utils.L1SpawnUtil;
import com.lineage.server.model.Instance.L1FurnitureInstance;
import com.lineage.server.model.L1Object;
import com.lineage.server.world.World;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.L1HouseLocation;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Harpy extends ItemExecutor {
	public static ItemExecutor get() {
		return new Harpy();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int itemObjectId = item.getId();
		if (!L1HouseLocation.isInHouse(pc.getX(), pc.getY(), pc.getMapId())) {
			pc.sendPackets(new S_ServerMessage(563));
			return;
		}
		boolean isAppear = true;
		L1FurnitureInstance furniture = null;
		final Iterator<L1Object> iterator = World.get().getObject().iterator();
		while (iterator.hasNext()) {
			final L1Object l1object = iterator.next();
			if (l1object instanceof L1FurnitureInstance) {
				furniture = (L1FurnitureInstance) l1object;
				if (furniture.getItemObjId() == itemObjectId) {
					isAppear = false;
					break;
				}
				continue;
			}
		}
		if (pc.getHeading() != 0 && pc.getHeading() != 2) {
			pc.sendPackets(new S_ServerMessage(79));
			return;
		}
		final int npcId = 80124;
		if (isAppear) {
			L1SpawnUtil.spawn(pc, 80124, itemObjectId);
		} else {
			furniture.deleteMe();
			FurnitureSpawnReading.get().deleteFurniture(furniture);
		}
	}
}
