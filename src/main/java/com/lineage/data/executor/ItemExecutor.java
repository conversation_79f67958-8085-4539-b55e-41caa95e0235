package com.lineage.data.executor;

import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.utils.BinaryOutputStream;

public abstract class ItemExecutor {
    private String[] as;

    public abstract void execute(int[] p0, L1PcInstance p1, L1ItemInstance p2);

    public String[] get_set() {
        return as;
    }

    public void set_set(String[] set) {
        as = set;
    }

    public BinaryOutputStream itemStatus(L1ItemInstance item) {
        return null;
    }
}
