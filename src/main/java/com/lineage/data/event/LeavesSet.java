package com.lineage.data.event;

import com.lineage.server.timecontroller.event.LeavesTime;
import com.lineage.server.templates.L1Event;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.EventExecutor;

public class LeavesSet extends EventExecutor {
	private static final Log _log;
	public static boolean START;
	public static int TIME;
	public static int EXP;
	public static int MAXEXP;

	public static int MIN_LEVEL;

	static {
		_log = LogFactory.getLog(LeavesSet.class);
		START = false;
		TIME = 0;
		EXP = 0;
		MAXEXP = 400000;
	}

	private LeavesSet() {
	}

	public static EventExecutor get() {
		return new LeavesSet();
	}

	@Override
	public void execute(final L1Event event) {
		try {
			LeavesSet.START = true;
			final String[] set = event.get_eventother().split(",");
			try {
				LeavesSet.TIME = Integer.parseInt(set[0]);
			} catch (Exception e2) {
				LeavesSet.TIME = 15;
				LeavesSet._log.error("未設定時間(使用預設15分鐘)");
			}
			try {
				LeavesSet.EXP = Integer.parseInt(set[1]);
			} catch (Exception e2) {
				LeavesSet.EXP = 4000;
				LeavesSet._log.error("未設定增加的經驗質(使用預設4000)");
			}
			try {
				LeavesSet.MIN_LEVEL = Integer.parseInt(set[2]);
			} catch (Exception e2) {
				LeavesSet.MIN_LEVEL = 45;
				LeavesSet._log.error("未設定等級最少值(使用預設45)");
			}
			LeavesSet.MAXEXP = LeavesSet.EXP * 200;
			final LeavesTime leavesTime = new LeavesTime();
			leavesTime.start();
		} catch (Exception e) {
			LeavesSet._log.error(e.getLocalizedMessage(), e);
		}
	}
}
