package com.lineage.william;

import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import com.lineage.DatabaseFactory;
import com.lineage.server.serverpackets.S_MPUpdate;
import com.lineage.server.serverpackets.S_HPUpdate;
import com.lineage.server.serverpackets.S_OwnCharStatus2;
import com.lineage.server.serverpackets.S_OwnCharStatus;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SPMR;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.Server;
import java.util.ArrayList;

public class drop_type_count {
	private static ArrayList<ArrayList<Object>> aData;
	private static boolean BUILD_DATA;
	public static final String TOKEN = ",";

	static {
		aData = new ArrayList();
		BUILD_DATA = false;
	}

	public static void main(final String[] a) {
		try {
			while (true) {
				Server.main(null);
			}
		} catch (Exception ex) {
		}
	}

	public static void forIntensifyArmor(final L1PcInstance pc, final L1ItemInstance item) {
		ArrayList<Object> aTempData = null;
		if (!drop_type_count.BUILD_DATA) {
			drop_type_count.BUILD_DATA = true;
			getData();
		}
		int i = 0;
		while (i < drop_type_count.aData.size()) {
			aTempData = drop_type_count.aData.get(i);
			if (pc.getArmorCount1() == ((Integer) aTempData.get(0)).intValue()) {
				if (((Integer) aTempData.get(1)).intValue() != 0) {
					pc.addStr(((Integer) aTempData.get(1)).intValue());
				}
				if (((Integer) aTempData.get(2)).intValue() != 0) {
					pc.addDex(((Integer) aTempData.get(2)).intValue());
				}
				if (((Integer) aTempData.get(3)).intValue() != 0) {
					pc.addInt(((Integer) aTempData.get(3)).intValue());
				}
				if (((Integer) aTempData.get(4)).intValue() != 0) {
					pc.addCon(((Integer) aTempData.get(4)).intValue());
				}
				if (((Integer) aTempData.get(5)).intValue() != 0) {
					pc.addWis(((Integer) aTempData.get(5)).intValue());
				}
				if (((Integer) aTempData.get(6)).intValue() != 0) {
					pc.addCha(((Integer) aTempData.get(6)).intValue());
				}
				if (((Integer) aTempData.get(7)).intValue() != 0) {
					pc.addSp(((Integer) aTempData.get(7)).intValue());
				}
				if (((Integer) aTempData.get(8)).intValue() != 0) {
					pc.addMaxHp(((Integer) aTempData.get(8)).intValue());
					pc.setCurrentHp(pc.getCurrentHp() + ((Integer) aTempData.get(8)).intValue());
				}
				if (((Integer) aTempData.get(9)).intValue() != 0) {
					pc.addMaxMp(((Integer) aTempData.get(9)).intValue());
					pc.setCurrentMp(pc.getCurrentMp() + ((Integer) aTempData.get(9)).intValue());
				}
				if (((Integer) aTempData.get(10)).intValue() != 0) {
					pc.addother_ReductionDmg(((Integer) aTempData.get(10)).intValue());
				}
				if (((Integer) aTempData.get(11)).intValue() != 0) {
					pc.addMr(((Integer) aTempData.get(11)).intValue());
				}
				if (((Integer) aTempData.get(12)).intValue() != 0) {
					pc.addAc(((Integer) aTempData.get(12)).intValue());
				}
				if (((Integer) aTempData.get(13)).intValue() != 0) {
					pc.addDmgup(((Integer) aTempData.get(13)).intValue());
				}
				if (((Integer) aTempData.get(14)).intValue() != 0) {
					pc.addBowDmgup(((Integer) aTempData.get(14)).intValue());
				}
				pc.sendPackets(new S_SPMR(pc));
				pc.sendPackets(new S_OwnCharStatus(pc));
				pc.sendPackets(new S_OwnCharStatus2(pc));
				pc.sendPackets(new S_HPUpdate(pc.getCurrentHp(), pc.getMaxHp()));
				pc.sendPackets(new S_MPUpdate(pc.getCurrentMp(), pc.getMaxMp()));
			}
			++i;
		}
	}

	public static void forIntensifyArmor1(final L1PcInstance pc, final L1ItemInstance item) {
		ArrayList<Object> aTempData = null;
		if (!drop_type_count.BUILD_DATA) {
			drop_type_count.BUILD_DATA = true;
			getData();
		}
		int i = 0;
		while (i < drop_type_count.aData.size()) {
			aTempData = drop_type_count.aData.get(i);
			if (pc.getArmorCount1() == ((Integer) aTempData.get(0)).intValue()) {
				if (((Integer) aTempData.get(1)).intValue() != 0) {
					pc.addStr(-((Integer) aTempData.get(1)).intValue());
				}
				if (((Integer) aTempData.get(2)).intValue() != 0) {
					pc.addDex(-((Integer) aTempData.get(2)).intValue());
				}
				if (((Integer) aTempData.get(3)).intValue() != 0) {
					pc.addInt(-((Integer) aTempData.get(3)).intValue());
				}
				if (((Integer) aTempData.get(4)).intValue() != 0) {
					pc.addCon(-((Integer) aTempData.get(4)).intValue());
				}
				if (((Integer) aTempData.get(5)).intValue() != 0) {
					pc.addWis(-((Integer) aTempData.get(5)).intValue());
				}
				if (((Integer) aTempData.get(6)).intValue() != 0) {
					pc.addCha(-((Integer) aTempData.get(6)).intValue());
				}
				if (((Integer) aTempData.get(7)).intValue() != 0) {
					pc.addSp(-((Integer) aTempData.get(7)).intValue());
				}
				if (((Integer) aTempData.get(8)).intValue() != 0) {
					pc.addMaxHp(-((Integer) aTempData.get(8)).intValue());
					pc.setCurrentHp(pc.getCurrentHp() - ((Integer) aTempData.get(8)).intValue());
				}
				if (((Integer) aTempData.get(9)).intValue() != 0) {
					pc.addMaxMp(-((Integer) aTempData.get(9)).intValue());
					pc.setCurrentMp(pc.getCurrentMp() - ((Integer) aTempData.get(9)).intValue());
				}
				if (((Integer) aTempData.get(10)).intValue() != 0) {
					pc.addother_ReductionDmg(-((Integer) aTempData.get(10)).intValue());
				}
				if (((Integer) aTempData.get(11)).intValue() != 0) {
					pc.addMr(-((Integer) aTempData.get(11)).intValue());
				}
				if (((Integer) aTempData.get(12)).intValue() != 0) {
					pc.addAc(-((Integer) aTempData.get(12)).intValue());
				}
				if (((Integer) aTempData.get(13)).intValue() != 0) {
					pc.addDmgup(-((Integer) aTempData.get(13)).intValue());
				}
				if (((Integer) aTempData.get(14)).intValue() != 0) {
					pc.addBowDmgup(-((Integer) aTempData.get(14)).intValue());
				}
				pc.sendPackets(new S_SPMR(pc));
				pc.sendPackets(new S_OwnCharStatus(pc));
				pc.sendPackets(new S_OwnCharStatus2(pc));
				pc.sendPackets(new S_HPUpdate(pc.getCurrentHp(), pc.getMaxHp()));
				pc.sendPackets(new S_MPUpdate(pc.getCurrentMp(), pc.getMaxMp()));
			}
			++i;
		}
	}

	private static void getData() {
		Connection conn = null;
		PreparedStatement pstmt = null;
		ResultSet rs = null;
		try {
			conn = DatabaseFactory.get().getConnection();
			pstmt = conn.prepareStatement("SELECT * FROM william_droptype_count");
			rs = pstmt.executeQuery();
			ArrayList<Object> aReturn = null;
			if (rs != null) {
				while (rs.next()) {
					aReturn = new ArrayList();
					aReturn.add(0, new Integer(rs.getInt("type_count")));
					aReturn.add(1, new Integer(rs.getInt("str")));
					aReturn.add(2, new Integer(rs.getInt("dex")));
					aReturn.add(3, new Integer(rs.getInt("int")));
					aReturn.add(4, new Integer(rs.getInt("con")));
					aReturn.add(5, new Integer(rs.getInt("wis")));
					aReturn.add(6, new Integer(rs.getInt("cha")));
					aReturn.add(7, new Integer(rs.getInt("sp")));
					aReturn.add(8, new Integer(rs.getInt("hp")));
					aReturn.add(9, new Integer(rs.getInt("mp")));
					aReturn.add(10, new Integer(rs.getInt("ReductionDmg")));
					aReturn.add(11, new Integer(rs.getInt("Mr")));
					aReturn.add(12, new Integer(rs.getInt("Ac")));
					aReturn.add(13, new Integer(rs.getInt("attack")));
					aReturn.add(14, new Integer(rs.getInt("bowattack")));
					drop_type_count.aData.add(aReturn);
				}
			}
		} catch (SQLException ex) {
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(pstmt);
			SQLUtil.close(conn);
		}
	}
}
