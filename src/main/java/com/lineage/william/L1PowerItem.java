package com.lineage.william;

public class L1PowerItem {
	public int itemid;
	public String powername;
	public String type;
	public int powercount;
	public int probability_unequi;
	public boolean unequipment;
	public int probability_polyid;
	public int polyid;
	public int polyid_time;
	public int probability;
	public int skill_id;
	public int target_to;
	public int addMaxHP;
	public int addMaxMP;
	public int add_hpr;
	public int add_mpr;
	public int add_str;
	public int add_con;
	public int add_dex;
	public int add_int;
	public int add_wis;
	public int add_cha;
	public int add_sp;
	public int add_ac;
	public int m_def;
	public int hit_modifier;
	public int dmg_modifier;
	public int bow_hit_modifier;
	public int bow_dmg_modifier;
	public int double_dmg_chance;
	public int addDamageReductionByArmor;
	public int gif;

	public L1PowerItem() {
		this.itemid = 0;
		this.powercount = 0;
		this.probability_unequi = 0;
		this.unequipment = false;
		this.probability_polyid = 0;
		this.polyid = 0;
		this.polyid_time = 0;
		this.probability = 0;
		this.skill_id = 0;
		this.target_to = 0;
		this.addMaxHP = 0;
		this.addMaxMP = 0;
		this.add_hpr = 0;
		this.add_mpr = 0;
		this.add_str = 0;
		this.add_con = 0;
		this.add_dex = 0;
		this.add_int = 0;
		this.add_wis = 0;
		this.add_cha = 0;
		this.add_sp = 0;
		this.add_ac = 0;
		this.m_def = 0;
		this.hit_modifier = 0;
		this.dmg_modifier = 0;
		this.bow_hit_modifier = 0;
		this.bow_dmg_modifier = 0;
		this.double_dmg_chance = 0;
		this.addDamageReductionByArmor = 0;
		this.gif = 0;
	}
}
