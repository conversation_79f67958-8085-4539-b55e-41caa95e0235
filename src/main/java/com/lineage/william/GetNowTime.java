package com.lineage.william;

import java.util.Calendar;

public class GetNowTime {
	public static int GetNowDay() {
		final Calendar rightNow = Calendar.getInstance();
		final int nowDay = rightNow.get(5);
		return nowDay;
	}

	public static int GetNowHour() {
		final Calendar rightNow = Calendar.getInstance();
		final int nowHour = rightNow.get(11);
		return nowHour;
	}

	public static int GetNowMinute() {
		final Calendar rightNow = Calendar.getInstance();
		final int nowMinute = rightNow.get(12);
		return nowMinute;
	}

	public static int GetNowMonth() {
		final Calendar rightNow = Calendar.getInstance();
		final int nowMonth = rightNow.get(2);
		return nowMonth;
	}

	public static int GetNowSecond() {
		final Calendar rightNow = Calendar.getInstance();
		final int nowSecond = rightNow.get(13);
		return nowSecond;
	}

	public static int GetNowYear() {
		final Calendar rightNow = Calendar.getInstance();
		final int nowYear = rightNow.get(1);
		return nowYear;
	}

	public static int GetNowDayWeek() {
		final Calendar rightNow = Calendar.getInstance();
		final int nowDayWeek = rightNow.get(7);
		return nowDayWeek;
	}
}
