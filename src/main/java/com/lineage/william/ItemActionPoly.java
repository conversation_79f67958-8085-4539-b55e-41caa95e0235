package com.lineage.william;

import java.util.StringTokenizer;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Connection;
import com.lineage.DatabaseFactory;
import com.lineage.server.templates.L1Item;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_CloseList;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.L1PolyMorph;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.ArrayList;

public class ItemActionPoly {
	private static ArrayList<ArrayList<Object>> aData;
	private static boolean NO_GET_DATA;
	public static final String TOKEN = ",";

	static {
		aData = new ArrayList();
		NO_GET_DATA = false;
	}

	public static boolean forNpcQuest(final String s, final L1PcInstance pc) {
		ArrayList aTempData = null;
		if (!ItemActionPoly.NO_GET_DATA) {
			ItemActionPoly.NO_GET_DATA = true;
			getData();
		}
		int i = 0;
		while (i < ItemActionPoly.aData.size()) {
			aTempData = ItemActionPoly.aData.get(i);
			if (((String) aTempData.get(0)).equals(s) && pc.getLevel() >= ((Integer) aTempData.get(1)).intValue()) {
				if (!pc.getInventory().checkItem(((Integer) aTempData.get(3)).intValue(),
						((Integer) aTempData.get(4)).intValue())) {
					final L1Item temp = ItemTable.get().getTemplate(((Integer) aTempData.get(3)).intValue());
					pc.sendPackets(new S_SystemMessage(String.valueOf(temp.getNameId()) + "不足( "
							+ ((Integer) aTempData.get(4)).intValue() + ")，無法使用。"));
					return false;
				}
				pc.getInventory().consumeItem(((Integer) aTempData.get(3)).intValue(),
						((Integer) aTempData.get(4)).intValue());
				L1PolyMorph.doPoly(pc, ((Integer) aTempData.get(2)).intValue(), 1800, 1);
				pc.sendPackets(new S_CloseList(pc.getId()));
			}
			++i;
		}
		return false;
	}

	private static void getData() {
		Connection con = null;
		try {
			con = DatabaseFactory.get().getConnection();
			final Statement stat = con.createStatement();
			final ResultSet rset = stat.executeQuery("SELECT * FROM w_自訂變形卷軸");
			ArrayList<Object> aReturn = null;
			String sTemp = null;
			if (rset != null) {
				while (rset.next()) {
					aReturn = new ArrayList();
					sTemp = rset.getString("action");
					aReturn.add(0, sTemp);
					aReturn.add(1, Integer.valueOf(rset.getInt("checkLevel_min")));
					aReturn.add(2, new Integer(rset.getInt("checkPoly")));
					aReturn.add(3, new Integer(rset.getInt("扣除道具編號")));
					aReturn.add(4, new Integer(rset.getInt("扣除道具數量")));
					ItemActionPoly.aData.add(aReturn);
				}
			}
			if (con != null && !con.isClosed()) {
				con.close();
			}
		} catch (Exception ex) {
		}
	}

	private static Object getArray(final String s, final String sToken, final int iType) {
		final StringTokenizer st = new StringTokenizer(s, sToken);
		final int iSize = st.countTokens();
		String sTemp = null;
		if (iType == 1) {
			final int[] iReturn = new int[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				iReturn[i] = Integer.parseInt(sTemp);
				++i;
			}
			return iReturn;
		}
		if (iType == 2) {
			final String[] sReturn = new String[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				sReturn[i] = sTemp;
				++i;
			}
			return sReturn;
		}
		if (iType == 3) {
			String sReturn2 = null;
			int i = 0;
			while (i < iSize) {
				sTemp = (sReturn2 = st.nextToken());
				++i;
			}
			return sReturn2;
		}
		return null;
	}
}
