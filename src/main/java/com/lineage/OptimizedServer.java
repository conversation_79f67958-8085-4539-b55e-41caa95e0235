package com.lineage;

import com.eric.gui.J_Main;
import com.lineage.commons.system.LanSecurityManager;
import com.lineage.config.*;
import com.lineage.echo.PacketWc;
import com.lineage.list.Announcements;
import com.lineage.server.GameServer;
import com.lineage.server.utils.DBClearAllUtil;
import com.lineage.server.utils.PerformanceMonitor;
import org.apache.log4j.PropertyConfigurator;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.LogManager;

/**
 * 優化版伺服器啟動類
 * 包含效能監控和優化設定
 */
public class OptimizedServer {
    
    public static void main(String[] args) throws Exception {
        System.out.println("【Welcome to Lineage - Optimized Edition】");
        System.out.println("版本: Ver_2100381916_Optimized");
        System.out.println("優化功能: 效能監控、記憶體優化、連接池優化");
        System.out.println("========================================");
        
        // 設定系統屬性
        setupSystemProperties();
        
        // 備份日誌文件
        backupLogFiles();
        
        boolean error = false;
        
        // 載入日誌配置
        try {
            InputStream is = new BufferedInputStream(new FileInputStream("./config/logging.properties"));
            LogManager.getLogManager().readConfiguration(is);
            is.close();
        } catch (IOException e3) {
            System.out.println("檔案遺失: ./config/logging.properties");
            error = true;
        }
        
        // 載入 Log4j 配置 (優先使用優化版)
        try {
            File optimizedLog4j = new File("./config/log4j-optimized.properties");
            if (optimizedLog4j.exists()) {
                PropertyConfigurator.configure("./config/log4j-optimized.properties");
                System.out.println("使用優化的 Log4j 配置");
            } else {
                PropertyConfigurator.configure("./config/log4j.properties");
                System.out.println("使用標準 Log4j 配置");
            }
        } catch (Exception e) {
            System.out.println("檔案遺失: ./config/log4j.properties");
            System.exit(0);
        }
        
        // 載入所有配置
        System.out.println("載入配置文件...");
        try {
            loadAllConfigs();
        } catch (Exception e) {
            System.out.println("CONFIG 資料加載異常!" + e);
            error = true;
        }
        
        System.out.println("-------------------------------------------------- ");
        System.out.println("Lineage【優化版 Ver_2100381916】 ");
        System.out.println("-------------------------------------------------- ");
        System.out.println("初始化中...");
        
        if (error) {
            System.exit(0);
        }
        
        // 初始化資料庫
        System.out.println("初始化資料庫連接...");
        DatabaseFactoryLogin.setDatabaseSettings();
        DatabaseFactory.setDatabaseSettings();
        
        DatabaseFactoryLogin.get();
        DatabaseFactory.get();
        
        // 載入遊戲配置
        System.out.println("載入遊戲配置...");
        ConfigBoxs.get();
        ConfigKill.get();
        ConfigDrop.get();
        ConfigDescs.get();
        ConfigQuest.load();
        ConfigServer.loadDB();
        
        // 檢查是否需要清空資料庫
        if (ConfigServer.DBClearAll) {
            System.out.println("警告: DBClearAll 設定為 true，將清空資料庫！");
            DBClearAllUtil.start();
        }
        
        // 啟動效能監控
        System.out.println("啟動效能監控...");
        PerformanceMonitor.getInstance().startMonitoring(5); // 每5分鐘監控一次
        
        // 其他初始化
        if (Config.NEWS) {
            Announcements.get();
        }
        
        if (Config.GUI) {
            J_Main.getInstance().setVisible(true);
        }
        
        // 設定安全管理器
        LanSecurityManager securityManager = new LanSecurityManager();
        System.setSecurityManager(securityManager);
        
        // 檢查作業系統
        String osname = System.getProperties().getProperty("os.name");
        if (osname.lastIndexOf("Linux") != -1) {
            Config.ISUBUNTU = true;
        }
        
        // 啟動遊戲伺服器
        System.out.println("啟動遊戲伺服器...");
        GameServer.getInstance().initialize();
        
        // 註冊關閉鉤子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("伺服器正在關閉...");
            PerformanceMonitor.getInstance().shutdown();
            System.out.println("伺服器已關閉");
        }));
        
        System.out.println("伺服器啟動完成！");
        System.out.println("效能監控已啟用，每5分鐘輸出一次報告");
    }
    
    /**
     * 設定系統屬性
     */
    private static void setupSystemProperties() {
        // 網路優化
        System.setProperty("java.net.preferIPv4Stack", "true");
        System.setProperty("java.awt.headless", "true");
        
        // 字符編碼
        System.setProperty("file.encoding", "UTF-8");
        
        // NIO 優化
        System.setProperty("sun.nio.ch.bugLevel", "");
        
        // 垃圾回收優化提示
        System.setProperty("java.util.concurrent.ForkJoinPool.common.parallelism", 
            String.valueOf(Runtime.getRuntime().availableProcessors()));
    }
    
    /**
     * 備份日誌文件
     */
    private static void backupLogFiles() {
        CompressFile bean = new CompressFile();
        try {
            File file = new File("./back");
            if (!file.exists()) {
                file.mkdir();
            }
            String nowDate = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss").format(new Date());
            bean.zip("./loginfo", "./back/" + nowDate + ".zip");
            
            // 清理舊日誌
            File loginfofile = new File("./loginfo");
            if (loginfofile.exists()) {
                String[] loginfofileList = loginfofile.list();
                if (loginfofileList != null) {
                    for (String fileName : loginfofileList) {
                        File readfile = new File("./loginfo/" + fileName);
                        if (readfile.exists() && !readfile.isDirectory()) {
                            readfile.delete();
                        }
                    }
                }
            }
        } catch (IOException e2) {
            System.out.println("資料夾不存在: ./back 已經自動建立!");
        }
    }
    
    /**
     * 載入所有配置
     */
    private static void loadAllConfigs() throws Exception {
        Config.load();
        ConfigAlt.load();
        ConfigCharSetting.load();
        ConfigOther.load();
        Config_Pc_Damage.load();
        ConfigRate.load();
        ConfigSQL.load();
        ConfigRecord.load();
        ConfigWho.load();
        ConfigBad.load();
        Configtype.load();
        ConfigAi.load();
        Configtf.load();
        ConfigIpCheck.load();
        ConfigClan.load();
        ConfigGuaji.load();
        ConfigPrinceSkill.load();
        ConfigKnightSkill.load();
        ConfigElfSkill.load();
        ConfigWizardSkill.load();
        ConfigDarkElfSkill.load();
        ConfigDragonKnightSkill.load();
        ConfigIllusionstSkill.load();
    }
}
