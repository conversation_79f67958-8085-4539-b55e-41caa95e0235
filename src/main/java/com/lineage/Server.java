package com.lineage;

import com.eric.gui.J_Main;
import com.lineage.commons.system.LanSecurityManager;
import com.lineage.config.*;
import com.lineage.echo.PacketWc;
import com.lineage.list.Announcements;
import com.lineage.server.GameServer;
import com.lineage.server.utils.DBClearAllUtil;
import org.apache.log4j.PropertyConfigurator;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.LogManager;

public class Server {
    public static void main(String[] args) throws Exception {
        System.out.println("【Welcome to Lineage】");
        CompressFile bean = new CompressFile();
        try {
            File file = new File("./back");
            if (!file.exists()) {
                file.mkdir();
            }
            String nowDate = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss").format(new Date());
            bean.zip("./loginfo", "./back/" + nowDate + ".zip");
            File loginfofile = new File("./loginfo");
            String[] loginfofileList = loginfofile.list();
            String[] array;
            int length = (array = loginfofileList).length;
            int i = 0;
            while (i < length) {
                String fileName = array[i];
                File readfile = new File("./loginfo/" + fileName);
                if (readfile.exists() && !readfile.isDirectory()) {
                    readfile.delete();
                }
                ++i;
            }
        } catch (IOException e2) {
            System.out.println("資料夾不存在: ./back 已經自動建立!");
        }
        boolean error = false;
        try {
            InputStream is = new BufferedInputStream(new FileInputStream("./config/logging.properties"));
            LogManager.getLogManager().readConfiguration(is);
            is.close();
        } catch (IOException e3) {
            System.out.println("檔案遺失: ./config/logging.properties");
            error = true;
        }
        try {
            PropertyConfigurator.configure("./config/log4j.properties");
        } catch (Exception e) {
            System.out.println("檔案遺失: ./config/log4j.properties");
            System.exit(0);
        }
        /**
         * Kevin 新增限制日期內可以執行核心
         */
//        if (PacketWc.checkExpireDate(2023, 1, 1)) {
//            System.out.println("核心過期導致無法使用");
//            System.exit(0);
//        }
        try {
            Config.load();
            ConfigAlt.load();
            ConfigCharSetting.load();
            ConfigOther.load();
            Config_Pc_Damage.load();
            ConfigRate.load();
            ConfigSQL.load();
            ConfigRecord.load();
            ConfigWho.load();
            ConfigBad.load();
            Configtype.load();
            ConfigAi.load();
            Configtf.load();
            ConfigIpCheck.load();
            ConfigClan.load();
            ConfigGuaji.load();
            ConfigPrinceSkill.load();
            ConfigKnightSkill.load();
            ConfigElfSkill.load();
            ConfigWizardSkill.load();
            ConfigDarkElfSkill.load();
            ConfigDragonKnightSkill.load();
            ConfigIllusionstSkill.load();
        } catch (Exception e) {
            System.out.println("CONFIG 資料加載異常!" + e);
            error = true;
        }
        System.out.println("-------------------------------------------------- ");
        System.out.println("Lineage【版本Ver_2100381916】 ");
        System.out.println("-------------------------------------------------- ");
        System.out.println("讀取設定中...");
        Thread.sleep(2000L);
        if (error) {
            System.exit(0);
        }
        DatabaseFactoryLogin.setDatabaseSettings();
        DatabaseFactory.setDatabaseSettings();

        DatabaseFactoryLogin.get();
        DatabaseFactory.get();

        ConfigBoxs.get();
        ConfigKill.get();
        ConfigDrop.get();
        ConfigDescs.get();
        ConfigQuest.load();
        ConfigServer.loadDB();
        if (ConfigServer.DBClearAll) {
            DBClearAllUtil.start();
        }
        if (Config.NEWS) {
            Announcements.get();
        }
        if (Config.GUI) {
            J_Main.getInstance().setVisible(true);
        }
        LanSecurityManager securityManager = new LanSecurityManager();
        System.setSecurityManager(securityManager);
        String osname = System.getProperties().getProperty("os.name");
        if (osname.lastIndexOf("Linux") != -1) {
            Config.ISUBUNTU = true;
        }
        GameServer.getInstance().initialize();
    }
}
