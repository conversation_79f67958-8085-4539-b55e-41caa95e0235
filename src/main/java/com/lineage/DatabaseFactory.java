package com.lineage;

import java.sql.Connection;
import java.sql.SQLException;
import com.lineage.config.ConfigSQL;
import org.apache.commons.logging.LogFactory;
import com.mchange.v2.c3p0.ComboPooledDataSource;
import org.apache.commons.logging.Log;

public class DatabaseFactory {
	private static final Log _log;
	private static DatabaseFactory _instance;
	public ComboPooledDataSource _source; // 改為 public 以支持監控
	private static String _driver;
	private static String _url;
	private static String _user;
	private static String _password;

	static {
		_log = LogFactory.getLog(DatabaseFactory.class);
	}

	public static void setDatabaseSettings() {
		DatabaseFactory._driver = ConfigSQL.DB_DRIVER;
		DatabaseFactory._url = String.valueOf(ConfigSQL.DB_URL1) + ConfigSQL.DB_URL2 + ConfigSQL.DB_URL3;
		DatabaseFactory._user = ConfigSQL.DB_LOGIN;
		DatabaseFactory._password = ConfigSQL.DB_PASSWORD;
	}

	public DatabaseFactory() throws SQLException {
		try {
			(this._source = new ComboPooledDataSource()).setDriverClass(DatabaseFactory._driver);
			this._source.setJdbcUrl(DatabaseFactory._url);
			this._source.setUser(DatabaseFactory._user);
			this._source.setPassword(DatabaseFactory._password);

			// 優化連接池設定
			this._source.setInitialPoolSize(5);
			this._source.setMinPoolSize(5);
			this._source.setMaxPoolSize(50);
			this._source.setAcquireIncrement(3);
			this._source.setMaxIdleTime(1800); // 30分鐘
			this._source.setMaxConnectionAge(3600); // 1小時
			this._source.setTestConnectionOnCheckin(true);
			this._source.setTestConnectionOnCheckout(false);
			this._source.setIdleConnectionTestPeriod(300); // 5分鐘
			this._source.setPreferredTestQuery("SELECT 1");
			this._source.setMaxStatements(200);
			this._source.setMaxStatementsPerConnection(50);
			this._source.setAcquireRetryAttempts(3);
			this._source.setAcquireRetryDelay(1000);
			this._source.setCheckoutTimeout(30000); // 30秒
			this._source.setUnreturnedConnectionTimeout(300); // 5分鐘

			// 測試連接
			Connection testConn = this._source.getConnection();
			testConn.close();
			DatabaseFactory._log.info("資料庫連接池初始化成功");
		} catch (SQLException e) {
			DatabaseFactory._log.fatal("資料庫連接錯誤!", e);
			throw e;
		} catch (Exception e2) {
			DatabaseFactory._log.fatal("資料庫初始化錯誤!", e2);
			throw new SQLException("資料庫初始化失敗", e2);
		}
	}

	public void shutdown() {
		try {
			this._source.close();
		} catch (Exception e) {
			DatabaseFactory._log.error(e.getLocalizedMessage(), e);
		}
		try {
			this._source = null;
		} catch (Exception e) {
			DatabaseFactory._log.error(e.getLocalizedMessage(), e);
		}
	}

	public static DatabaseFactory get() throws SQLException {
		if (DatabaseFactory._instance == null) {
			DatabaseFactory._instance = new DatabaseFactory();
		}
		return DatabaseFactory._instance;
	}

	public Connection getConnection() {
		Connection con = null;
		while (con == null) {
			try {
				con = this._source.getConnection();
			} catch (SQLException e) {
				DatabaseFactory._log.error(e.getLocalizedMessage(), e);
			}
		}
		return con;
	}
}
