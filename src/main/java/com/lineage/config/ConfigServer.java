package com.lineage.config;

import com.lineage.DatabaseFactoryLogin;
import com.lineage.server.utils.SQLUtil;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public final class ConfigServer {
	private static final Log _log = LogFactory.getLog(ConfigServer.class);
	public static boolean DBClearAll;

	public static void loadDB() {
		Connection cn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		int size = 0;
		int i = 0;
		try {
			cn = DatabaseFactoryLogin.get().getConnection();
			ps = cn.prepareStatement("SELECT * FROM `config_server`");
			rs = ps.executeQuery();
			while (rs.next()) {
				String id = rs.getString("id");
				String set = rs.getString("設置");
				int set_int = 0;
				boolean set_boolean = false;
				if ((set.equalsIgnoreCase("false")) || (set.equalsIgnoreCase("true"))) {
					// 正確解析字串為布林值
					set_boolean = set.equalsIgnoreCase("true");
				} else {
					try {
						set_int = rs.getInt("設置");
					} catch (Exception localException) {
					}
				}
				if (id.equalsIgnoreCase("DBClearAll")) {
					DBClearAll = set_boolean;
					i++;
					// 添加調試輸出
					System.out.println("DEBUG: DBClearAll 設定讀取 - 資料庫值: '" + set + "', 解析結果: " + set_boolean);
				}
			}
		} catch (SQLException e) {
			_log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(ps);
			SQLUtil.close(cn);
		}
		_log.info("Config->Server設置: 0筆 / 處理: " + i + "筆");
	}
}