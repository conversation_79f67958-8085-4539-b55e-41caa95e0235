package com.lineage.config;

import com.lineage.server.utils.PerformanceTimer;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Properties;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public final class ConfigIllusionstSkill {
	private static final Log _log = LogFactory.getLog(ConfigPrinceSkill.class);
	public static int ILLUSION_AVATAR_DAMAGE;
	public static int JOY_OF_PAIN_PC;
	public static int JOY_OF_PAIN_NPC;
	public static int JOY_OF_PAIN_DMG;
	public static int BONE_BREAK_1;
	public static int BONE_BREAK_2;
	public static int BONE_BREAK_3;
	public static double BONE_BREAK_INT;
	public static double BONE_BREAK_MR;
	public static int PHAN_TASM_1;
	public static int PHAN_TASM_2;
	public static int PHAN_TASM_3;
	public static double PHANTASM_INT;
	public static double PHANTASM_MR;
	public static int MIRROR;
	public static int KIRINGKU_DOWN_MR1;
	public static int KIRINGKU_DOWN_MR2;
	public static int KIRINGKU_DOWN_MR3;
	public static int KIRINGKU_DOWN_MR4;
	private static final String CONFIG_FILE = "./config/幻術師_技能設定表.properties";

	public static void load() throws ConfigErrorException {
		PerformanceTimer timer = new PerformanceTimer();
		Properties set = new Properties();
		try {
			InputStream is = new FileInputStream(new File("./config/幻術師_技能設定表.properties"));
			set.load(is);
			is.close();
			ILLUSION_AVATAR_DAMAGE = Integer.parseInt(set.getProperty("ILLUSION_AVATAR_DAMAGE", "1"));
			JOY_OF_PAIN_PC = Integer.parseInt(set.getProperty("JOY_OF_PAIN_PC", "1"));
			JOY_OF_PAIN_NPC = Integer.parseInt(set.getProperty("JOY_OF_PAIN_NPC", "1"));
			JOY_OF_PAIN_DMG = Integer.parseInt(set.getProperty("JOY_OF_PAIN_DMG", "100"));
			BONE_BREAK_1 = Integer.parseInt(set.getProperty("BONE_BREAK_1", "5"));
			BONE_BREAK_2 = Integer.parseInt(set.getProperty("BONE_BREAK_2", "10"));
			BONE_BREAK_3 = Integer.parseInt(set.getProperty("BONE_BREAK_3", "15"));
			BONE_BREAK_INT = Double.parseDouble(set.getProperty("BONE_BREAK_INT", "0"));
			BONE_BREAK_MR = Double.parseDouble(set.getProperty("BONE_BREAK_MR", "0"));
			PHAN_TASM_1 = Integer.parseInt(set.getProperty("PHAN_TASM_1", "5"));
			PHAN_TASM_2 = Integer.parseInt(set.getProperty("PHAN_TASM_2", "10"));
			PHAN_TASM_3 = Integer.parseInt(set.getProperty("PHAN_TASM_3", "15"));
			PHANTASM_INT = Double.parseDouble(set.getProperty("PHANTASM_INT", "0"));
			PHANTASM_MR = Double.parseDouble(set.getProperty("PHANTASM_MR", "0"));
			MIRROR = Integer.parseInt(set.getProperty("MIRROR", "5"));
			KIRINGKU_DOWN_MR1 = Integer.parseInt(set.getProperty("KIRINGKU_DOWN_MR1", "100"));
			KIRINGKU_DOWN_MR2 = Integer.parseInt(set.getProperty("KIRINGKU_DOWN_MR2", "100"));
			KIRINGKU_DOWN_MR3 = Integer.parseInt(set.getProperty("KIRINGKU_DOWN_MR3", "100"));
			KIRINGKU_DOWN_MR4 = Integer.parseInt(set.getProperty("KIRINGKU_DOWN_MR4", "100"));
		} catch (Exception e) {
			throw new ConfigErrorException("設置檔案遺失: ./config/幻術師_技能設定表.properties");
		} finally {
			set.clear();
			_log.info("Config/幻術師_技能設定表讀取完成 (" + timer.get() + "ms)");
		}
	}
}