#!/bin/bash

# 修復配置文件腳本
# 解決配置文件相關的啟動問題

echo "=========================================="
echo "配置文件檢查和修復工具"
echo "路徑: /home/<USER>/381a"
echo "=========================================="

cd /home/<USER>/381a

# 1. 檢查和創建配置目錄
echo "=== 1. 檢查配置目錄 ==="
if [ ! -d "config" ]; then
    mkdir -p config
    echo "✅ 創建 config 目錄"
else
    echo "✅ config 目錄已存在"
fi

# 2. 檢查 sql.properties
echo
echo "=== 2. 檢查 sql.properties ==="
if [ ! -f "config/sql.properties" ]; then
    echo "❌ sql.properties 不存在，創建基本配置..."
    
    cat > config/sql.properties << 'EOF'
#-------------------------------------------------------------
# SQL config - MariaDB Configuration
#-------------------------------------------------------------

# 登入資料庫驅動
Driver_LOGIN = org.mariadb.jdbc.Driver
Driver = org.mariadb.jdbc.Driver

# 資料庫連接 URL
URL1_LOGIN = ************************/
URL2_LOGIN = lineage
URL3_LOGIN = ?useUnicode=true&characterEncoding=utf8mb4&serverTimezone=Asia/Taipei&useSSL=false&allowPublicKeyRetrieval=true

# 登入資料庫帳號密碼
Login_LOGIN = root
Password_LOGIN = 

# 主資料庫連接
URL1 = ************************/
URL2 = lineage
URL3 = ?useUnicode=true&characterEncoding=utf8mb4&serverTimezone=Asia/Taipei&useSSL=false&allowPublicKeyRetrieval=true

# 主資料庫帳號密碼
Login = root
Password = 
EOF
    
    echo "✅ 創建基本 sql.properties"
    echo "⚠️  請編輯 config/sql.properties 設置正確的資料庫密碼"
else
    echo "✅ sql.properties 已存在"
fi

# 3. 檢查 c3p0-config.xml
echo
echo "=== 3. 檢查 c3p0-config.xml ==="
if [ ! -f "config/c3p0-config.xml" ]; then
    echo "❌ c3p0-config.xml 不存在，創建基本配置..."
    
    cat > config/c3p0-config.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<c3p0-config>
    <default-config>
        <!-- 基本連接池配置 -->
        <property name="initialPoolSize">5</property>
        <property name="minPoolSize">5</property>
        <property name="maxPoolSize">50</property>
        <property name="acquireIncrement">3</property>
        
        <!-- 連接超時設置 -->
        <property name="checkoutTimeout">30000</property>
        <property name="maxIdleTime">1800</property>
        <property name="maxConnectionAge">3600</property>
        
        <!-- 連接測試 -->
        <property name="testConnectionOnCheckout">true</property>
        <property name="testConnectionOnCheckin">true</property>
        <property name="idleConnectionTestPeriod">300</property>
        <property name="preferredTestQuery">SELECT 1</property>
        
        <!-- 錯誤處理 -->
        <property name="acquireRetryAttempts">3</property>
        <property name="acquireRetryDelay">1000</property>
        <property name="breakAfterAcquireFailure">false</property>
        
        <!-- 調試設置 -->
        <property name="debugUnreturnedConnectionStackTraces">true</property>
        <property name="unreturnedConnectionTimeout">300</property>
    </default-config>
</c3p0-config>
EOF
    
    echo "✅ 創建基本 c3p0-config.xml"
else
    echo "✅ c3p0-config.xml 已存在"
fi

# 4. 檢查 log4j 配置
echo
echo "=== 4. 檢查 log4j 配置 ==="
if [ ! -f "config/log4j.properties" ]; then
    echo "❌ log4j.properties 不存在，創建基本配置..."
    
    cat > config/log4j.properties << 'EOF'
# Log4j Configuration for Lineage Server

# Root logger
log4j.rootLogger=INFO, CONSOLE, FILE

# Console appender
log4j.appender.CONSOLE=org.apache.log4j.ConsoleAppender
log4j.appender.CONSOLE.layout=org.apache.log4j.PatternLayout
log4j.appender.CONSOLE.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} [%t] %-5p %c{1} - %m%n

# File appender
log4j.appender.FILE=org.apache.log4j.RollingFileAppender
log4j.appender.FILE.File=logs/server.log
log4j.appender.FILE.MaxFileSize=10MB
log4j.appender.FILE.MaxBackupIndex=5
log4j.appender.FILE.layout=org.apache.log4j.PatternLayout
log4j.appender.FILE.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} [%t] %-5p %c{1} - %m%n

# Specific loggers
log4j.logger.com.lineage=DEBUG
log4j.logger.com.mchange.v2.c3p0=INFO
log4j.logger.org.mariadb=INFO
EOF
    
    echo "✅ 創建基本 log4j.properties"
else
    echo "✅ log4j.properties 已存在"
fi

# 5. 創建資料庫
echo
echo "=== 5. 檢查資料庫 ==="
if command -v mysql &> /dev/null; then
    if systemctl is-active --quiet mariadb; then
        echo "✅ MariaDB 服務運行中"
        
        # 檢查資料庫是否存在
        DB_EXISTS=$(mysql -u root -e "SHOW DATABASES LIKE 'lineage';" 2>/dev/null | grep lineage)
        if [ -z "$DB_EXISTS" ]; then
            echo "❌ 資料庫 'lineage' 不存在"
            echo "創建基本資料庫..."
            
            mysql -u root -e "CREATE DATABASE IF NOT EXISTS lineage CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null
            if [ $? -eq 0 ]; then
                echo "✅ 資料庫 'lineage' 已創建"
            else
                echo "❌ 創建資料庫失敗 (可能需要 root 密碼)"
                echo "請手動執行: mysql -u root -p"
                echo "然後執行: CREATE DATABASE lineage CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
            fi
        else
            echo "✅ 資料庫 'lineage' 已存在"
        fi
    else
        echo "❌ MariaDB 服務未運行"
        echo "請執行: sudo systemctl start mariadb"
    fi
else
    echo "❌ MySQL/MariaDB 客戶端未安裝"
    echo "請執行: sudo apt install mariadb-client"
fi

# 6. 設置文件權限
echo
echo "=== 6. 設置文件權限 ==="
chmod 644 config/*
echo "✅ 配置文件權限已設置"

# 7. 顯示配置摘要
echo
echo "=== 7. 配置摘要 ==="
echo "配置文件位置:"
ls -la config/

echo
echo "sql.properties 內容預覽:"
head -10 config/sql.properties

echo
echo "=========================================="
echo "配置文件修復完成！"
echo "=========================================="
echo "✅ 已檢查/創建的配置文件:"
echo "   - config/sql.properties"
echo "   - config/c3p0-config.xml"
echo "   - config/log4j.properties"
echo
echo "⚠️  重要提醒:"
echo "1. 請編輯 config/sql.properties 設置正確的資料庫密碼"
echo "2. 確保 MariaDB 服務正在運行"
echo "3. 確保資料庫 'lineage' 存在並有適當的表結構"
echo
echo "下一步:"
echo "1. 編輯配置: nano config/sql.properties"
echo "2. 測試啟動: ./run_jar_simple.sh"
echo "3. 如果仍有問題: ./diagnose_exit_error.sh"
