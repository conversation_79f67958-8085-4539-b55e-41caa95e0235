#!/bin/bash

echo "========================================"
echo "Lineage 381a Server Startup Script"
echo "========================================"

# 設定 ClassPath
CLASSPATH="out/production/Lineage381a"
CLASSPATH="$CLASSPATH:jar/c3p0-*******.jar"
CLASSPATH="$CLASSPATH:jar/commons-logging-1.2.jar"
CLASSPATH="$CLASSPATH:jar/javolution-5.5.1.jar"
CLASSPATH="$CLASSPATH:jar/log4j-1.2.16.jar"
CLASSPATH="$CLASSPATH:jar/mariadb-java-client-3.1.4.jar"
CLASSPATH="$CLASSPATH:jar/mchange-commons-java-0.2.19.jar"

# 顯示 ClassPath (除錯用)
echo "ClassPath: $CLASSPATH"
echo

# 檢查主類是否存在
if [ ! -f "out/production/Lineage381a/com/lineage/Server.class" ]; then
    echo "錯誤: 找不到主類文件 Server.class"
    echo "請確保專案已經編譯完成"
    exit 1
fi

# 檢查配置文件是否存在
if [ ! -f "config/sql.properties" ]; then
    echo "錯誤: 找不到資料庫配置文件 config/sql.properties"
    echo "請確保配置文件存在"
    exit 1
fi

echo "啟動 Lineage 伺服器..."
echo

# 針對 64GB RAM 的高效能優化參數
JVM_OPTS="-Xms16g -Xmx32g"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=100"
JVM_OPTS="$JVM_OPTS -XX:G1HeapRegionSize=32m"
JVM_OPTS="$JVM_OPTS -XX:G1NewSizePercent=20"
JVM_OPTS="$JVM_OPTS -XX:G1MaxNewSizePercent=30"
JVM_OPTS="$JVM_OPTS -XX:G1MixedGCCountTarget=8"
JVM_OPTS="$JVM_OPTS -XX:+UnlockExperimentalVMOptions"
JVM_OPTS="$JVM_OPTS -XX:+UseStringDeduplication"
JVM_OPTS="$JVM_OPTS -XX:+UseLargePages"
JVM_OPTS="$JVM_OPTS -XX:+AlwaysPreTouch"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Taipei"
JVM_OPTS="$JVM_OPTS -Djava.net.preferIPv4Stack=true"

# 創建日誌目錄
mkdir -p logs

echo "JVM 參數: $JVM_OPTS"
echo

# 啟動伺服器
java $JVM_OPTS -cp "$CLASSPATH" com.lineage.Server

echo
echo "伺服器已停止"
