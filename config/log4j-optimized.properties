# ??? Log4j ??
# Root Logger ??
log4j.rootLogger=INFO, CONSOLE, FILE

# Console Appender - ???????
log4j.appender.CONSOLE=org.apache.log4j.ConsoleAppender
log4j.appender.CONSOLE.Target=System.out
log4j.appender.CONSOLE.Threshold=INFO
log4j.appender.CONSOLE.layout=org.apache.log4j.PatternLayout
log4j.appender.CONSOLE.layout.ConversionPattern=%d{HH:mm:ss} [%5p] %c{1} - %m%n

# ?????? - ????
log4j.appender.FILE=org.apache.log4j.RollingFileAppender
log4j.appender.FILE.File=logs/lineage.log
log4j.appender.FILE.MaxFileSize=10MB
log4j.appender.FILE.MaxBackupIndex=5
log4j.appender.FILE.Threshold=DEBUG
log4j.appender.FILE.layout=org.apache.log4j.PatternLayout
log4j.appender.FILE.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} [%5p] %c{1}:%L - %m%n

# ??????
log4j.appender.ERROR=org.apache.log4j.RollingFileAppender
log4j.appender.ERROR.File=logs/error.log
log4j.appender.ERROR.MaxFileSize=5MB
log4j.appender.ERROR.MaxBackupIndex=3
log4j.appender.ERROR.Threshold=ERROR
log4j.appender.ERROR.layout=org.apache.log4j.PatternLayout
log4j.appender.ERROR.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} [%5p] %c{1}:%L - %m%n

# ??????
log4j.appender.PERF=org.apache.log4j.RollingFileAppender
log4j.appender.PERF.File=logs/performance.log
log4j.appender.PERF.MaxFileSize=5MB
log4j.appender.PERF.MaxBackupIndex=2
log4j.appender.PERF.Threshold=INFO
log4j.appender.PERF.layout=org.apache.log4j.PatternLayout
log4j.appender.PERF.layout.ConversionPattern=%d{HH:mm:ss} - %m%n

# ???????
log4j.appender.DB=org.apache.log4j.RollingFileAppender
log4j.appender.DB.File=logs/database.log
log4j.appender.DB.MaxFileSize=5MB
log4j.appender.DB.MaxBackupIndex=2
log4j.appender.DB.Threshold=WARN
log4j.appender.DB.layout=org.apache.log4j.PatternLayout
log4j.appender.DB.layout.ConversionPattern=%d{HH:mm:ss} [%5p] %c{1} - %m%n

# ???????????
# ?????
log4j.logger.com.lineage.DatabaseFactory=INFO, DB
log4j.logger.com.lineage.DatabaseFactoryLogin=INFO, DB
log4j.logger.com.mchange=WARN, DB
log4j.logger.com.lineage.server.storage=INFO, DB

# ????
log4j.logger.com.lineage.server.utils.PerformanceTimer=INFO, PERF
log4j.logger.com.lineage.server.thread=INFO, PERF

# ???? - ??????
log4j.logger.com.lineage.server.model=WARN
log4j.logger.com.lineage.server.serverpackets=WARN
log4j.logger.com.lineage.server.clientpackets=WARN

# ????
log4j.logger.com.lineage.server.network=INFO

# ????
log4j.logger.com.lineage.server.utils.DBClearAllUtil=ERROR, ERROR

# ??????
log4j.additivity.com.lineage.DatabaseFactory=false
log4j.additivity.com.lineage.DatabaseFactoryLogin=false
log4j.additivity.com.mchange=false
log4j.additivity.com.lineage.server.utils.PerformanceTimer=false
log4j.additivity.com.lineage.server.utils.DBClearAllUtil=false
