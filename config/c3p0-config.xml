<c3p0-config>
  <default-config>
    <!-- 基本連接設定 -->
    <property name="autoCommitOnClose">false</property>
    <property name="initialPoolSize">5</property>
    <property name="minPoolSize">5</property>
    <property name="maxPoolSize">50</property>
    <property name="acquireIncrement">3</property>

    <!-- 連接獲取設定 -->
    <property name="acquireRetryAttempts">3</property>
    <property name="acquireRetryDelay">1000</property>
    <property name="checkoutTimeout">30000</property>
    <property name="breakAfterAcquireFailure">false</property>

    <!-- 連接測試設定 -->
    <property name="testConnectionOnCheckin">true</property>
    <property name="testConnectionOnCheckout">false</property>
    <property name="idleConnectionTestPeriod">300</property>
    <property name="preferredTestQuery">SELECT 1</property>

    <!-- 連接生命週期管理 -->
    <property name="maxIdleTime">1800</property>
    <property name="maxConnectionAge">3600</property>
    <property name="maxIdleTimeExcessConnections">900</property>

    <!-- 語句快取 -->
    <property name="maxStatements">200</property>
    <property name="maxStatementsPerConnection">50</property>
    <property name="statementCacheNumDeferredCloseThreads">1</property>

    <!-- 效能優化 -->
    <property name="numHelperThreads">3</property>
    <property name="unreturnedConnectionTimeout">300</property>
    <property name="debugUnreturnedConnectionStackTraces">false</property>
  </default-config>
</c3p0-config>
