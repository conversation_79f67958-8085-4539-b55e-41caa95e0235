import com.lineage.DatabaseFactoryLogin;
import com.lineage.config.ConfigSQL;
import com.lineage.config.ConfigServer;

/**
 * 測試 ConfigServer.DBClearAll 設定讀取
 */
public class TestConfigServer {
    
    public static void main(String[] args) {
        try {
            System.out.println("=== ConfigServer DBClearAll 測試 ===");
            
            // 載入 SQL 配置
            ConfigSQL.load();
            
            // 設定資料庫連接
            DatabaseFactoryLogin.setDatabaseSettings();
            DatabaseFactoryLogin.get();
            
            // 載入伺服器配置
            System.out.println("載入 ConfigServer 設定...");
            ConfigServer.loadDB();
            
            // 顯示結果
            System.out.println("ConfigServer.DBClearAll = " + ConfigServer.DBClearAll);
            
            if (ConfigServer.DBClearAll) {
                System.out.println("⚠️  警告: DBClearAll 設定為 true - 資料庫將被清空！");
            } else {
                System.out.println("✅ DBClearAll 設定為 false - 資料庫不會被清空");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 測試失敗: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 測試完成 ===");
    }
}
