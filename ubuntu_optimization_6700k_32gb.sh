#!/bin/bash

# Ubuntu 24.02 系統優化腳本
# 針對 i7-6700K + 32GB RAM + PCIe SSD + 雙2.5G網卡

echo "=========================================="
echo "Ubuntu 24.02 Lineage 伺服器系統優化"
echo "硬體配置: i7-6700K + 32GB RAM + PCIe SSD"
echo "=========================================="

# 檢查是否為 root 用戶
if [ "$EUID" -ne 0 ]; then
    echo "請使用 sudo 執行此腳本"
    exit 1
fi

# 1. 針對高記憶體的網路參數優化
echo "1. 優化網路參數 (針對雙2.5G網卡)..."
cat >> /etc/sysctl.conf << EOF

# Lineage 伺服器網路優化 - 高效能版本
# 針對雙2.5G網卡和1G外線優化
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.core.rmem_default = 1048576
net.core.wmem_default = 1048576
net.ipv4.tcp_rmem = 8192 1048576 134217728
net.ipv4.tcp_wmem = 8192 1048576 134217728
net.ipv4.tcp_congestion_control = bbr
net.core.netdev_max_backlog = 10000
net.ipv4.tcp_window_scaling = 1
net.ipv4.tcp_timestamps = 1
net.ipv4.tcp_sack = 1
net.ipv4.tcp_no_metrics_save = 1
net.ipv4.tcp_moderate_rcvbuf = 1
net.ipv4.tcp_fin_timeout = 15
net.ipv4.tcp_keepalive_time = 600
net.ipv4.tcp_max_syn_backlog = 16384
net.ipv4.tcp_max_tw_buckets = 10000
net.ipv4.tcp_fastopen = 3
net.ipv4.tcp_mem = 102400 204800 409600
net.ipv4.tcp_max_orphans = 6553600
net.ipv4.ip_local_port_range = 1024 65535

# 針對高記憶體系統的優化
vm.swappiness = 1
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
vm.vfs_cache_pressure = 50
vm.min_free_kbytes = 1048576

# 針對 PCIe SSD 的 I/O 優化
vm.dirty_expire_centisecs = 500
vm.dirty_writeback_centisecs = 100
EOF

# 2. 針對高記憶體的文件描述符優化
echo "2. 優化文件描述符限制 (高記憶體版本)..."
cat >> /etc/security/limits.conf << EOF

# Lineage 伺服器文件描述符優化 - 高效能版本
* soft nofile 1048576
* hard nofile 1048576
* soft nproc 65536
* hard nproc 65536
* soft memlock unlimited
* hard memlock unlimited
EOF

# 3. 針對 i7-6700K 的 CPU 優化
echo "3. CPU 效能優化 (i7-6700K)..."
cat >> /etc/sysctl.conf << EOF

# CPU 效能優化
kernel.sched_migration_cost_ns = 5000000
kernel.sched_autogroup_enabled = 0
kernel.sched_wakeup_granularity_ns = 15000000
kernel.sched_min_granularity_ns = 10000000
kernel.sched_latency_ns = 80000000
EOF

# 4. 大頁面記憶體支援 (針對 32GB RAM)
echo "4. 啟用大頁面記憶體支援..."
echo 'vm.nr_hugepages = 8192' >> /etc/sysctl.conf

# 設置大頁面
echo never > /sys/kernel/mm/transparent_hugepage/enabled
echo never > /sys/kernel/mm/transparent_hugepage/defrag

# 5. 針對 PCIe SSD 的 I/O 調度器優化
echo "5. 優化 I/O 調度器 (PCIe SSD)..."
# 檢測 NVMe 設備並設置調度器
for device in /sys/block/nvme*; do
    if [ -d "$device" ]; then
        device_name=$(basename "$device")
        echo "設置 $device_name 使用 none 調度器"
        echo none > /sys/block/$device_name/queue/scheduler
        echo 1 > /sys/block/$device_name/queue/nomerges
        echo 1024 > /sys/block/$device_name/queue/nr_requests
    fi
done

# 6. 網卡優化 (雙2.5G網卡)
echo "6. 網卡效能優化..."
# 檢測並優化網卡設置
for interface in $(ip link show | grep -E "^[0-9]+:" | grep -v lo | cut -d: -f2 | tr -d ' '); do
    echo "優化網卡: $interface"
    ethtool -G $interface rx 4096 tx 4096 2>/dev/null || true
    ethtool -K $interface gro on gso on tso on 2>/dev/null || true
    ethtool -C $interface rx-usecs 50 tx-usecs 50 2>/dev/null || true
done

# 7. Java 環境優化
echo "7. Java 環境優化..."
if ! command -v java &> /dev/null; then
    echo "安裝 OpenJDK 8..."
    apt update
    apt install -y openjdk-8-jdk
else
    echo "Java 已安裝: $(java -version 2>&1 | head -n 1)"
fi

# 設置 Java 環境變數
cat >> /etc/environment << EOF
JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
JVM_OPTS="-Xms8g -Xmx16g -XX:+UseG1GC -XX:+UseLargePages"
EOF

# 8. 針對高效能的 MariaDB 調優
echo "8. MariaDB 高效能配置..."
if command -v mysql &> /dev/null; then
    echo "MariaDB 優化建議 (32GB RAM):"
    echo "  - innodb_buffer_pool_size = 12G"
    echo "  - max_connections = 1000"
    echo "  - query_cache_size = 512M"
    echo "  - innodb_log_file_size = 512M"
    echo "  - innodb_buffer_pool_instances = 8"
fi

# 9. 防火牆配置
echo "9. 配置防火牆..."
if command -v ufw &> /dev/null; then
    echo "配置 UFW 防火牆..."
    ufw allow 2000/tcp comment "Lineage Game Port"
    ufw allow 2001/tcp comment "Lineage Login Port"
    ufw allow 22/tcp comment "SSH"
    echo "防火牆規則已添加"
fi

# 10. 高效能監控工具
echo "10. 安裝高效能監控工具..."
apt install -y htop iotop nethogs sysstat numactl hwloc

# 11. 針對高記憶體的日誌輪轉
echo "11. 配置日誌輪轉 (高記憶體版本)..."
cat > /etc/logrotate.d/lineage << EOF
/path/to/your/lineage/server/logs/*.log {
    daily
    missingok
    rotate 60
    compress
    delaycompress
    notifempty
    create 644 lineage lineage
    size 100M
    postrotate
        /bin/kill -USR1 \$(cat /path/to/your/lineage/server/lineage.pid 2>/dev/null) 2>/dev/null || true
    endscript
}
EOF

# 12. 建立開機自動優化腳本
echo "12. 建立開機自動優化..."
cat > /etc/systemd/system/lineage-optimization.service << EOF
[Unit]
Description=Lineage Server Hardware Optimization
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'echo never > /sys/kernel/mm/transparent_hugepage/enabled'
ExecStart=/bin/bash -c 'echo never > /sys/kernel/mm/transparent_hugepage/defrag'
ExecStart=/bin/bash -c 'for dev in /sys/block/nvme*; do [ -d "\$dev" ] && echo none > \$dev/queue/scheduler; done'
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

systemctl enable lineage-optimization.service

# 13. 應用系統參數
echo "13. 應用系統參數..."
sysctl -p

echo "=========================================="
echo "高效能系統優化完成！"
echo "=========================================="
echo "硬體配置優化摘要:"
echo "- JVM 記憶體: 8GB-16GB (針對32GB RAM)"
echo "- MariaDB 緩衝: 12GB"
echo "- 連接池: 主資料庫100連接, 登入40連接"
echo "- 網路: 針對雙2.5G網卡優化"
echo "- 儲存: PCIe SSD 最佳化"
echo ""
echo "建議重啟系統以確保所有優化生效"
echo ""
echo "預期效能提升:"
echo "- 支援 500+ 同時在線玩家"
echo "- 查詢響應時間 < 50ms"
echo "- 記憶體使用率 < 60%"
echo "- 網路延遲 < 10ms"
