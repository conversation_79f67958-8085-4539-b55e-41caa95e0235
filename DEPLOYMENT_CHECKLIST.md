# Lineage 381a 部署檢查清單

## 📋 部署前檢查

### 系統環境
- [ ] Ubuntu 24.02 系統
- [ ] 8GB+ 記憶體
- [ ] 50GB+ 可用磁碟空間
- [ ] 網路連接正常

### 軟體依賴
- [ ] OpenJDK 8+ 已安裝
- [ ] MariaDB 10.6+ 已安裝並運行
- [ ] Git 已安裝 (如需版本控制)

### 權限設置
- [ ] 創建 lineage 用戶
- [ ] 設置適當的文件權限
- [ ] sudo 權限可用

## 🔧 系統優化檢查

### 執行優化腳本
- [ ] `chmod +x *.sh` 執行成功
- [ ] `sudo ./ubuntu_optimization.sh` 執行成功
- [ ] 系統重啟完成
- [ ] 網路參數優化生效

### MariaDB 配置
- [ ] 複製 `mariadb_optimization.cnf` 到正確位置
- [ ] MariaDB 重啟成功
- [ ] 資料庫連接測試通過
- [ ] 字符集設置為 utf8mb4

### 資料庫優化
- [ ] 執行 `database_optimization.sql` 成功
- [ ] 索引創建完成
- [ ] 執行 `add_connpool_command.sql` 成功
- [ ] 管理命令註冊成功

## 🚀 伺服器部署檢查

### 編譯檢查
- [ ] Java 源碼編譯成功
- [ ] 所有 .class 文件存在於 `out/production/Lineage381a/`
- [ ] 主類 `com.lineage.Server` 可找到

### 依賴庫檢查
- [ ] `jar/log4j-1.2.16.jar` 存在
- [ ] `jar/commons-logging-1.2.jar` 存在
- [ ] `jar/c3p0-0.9.5.5.jar` 存在
- [ ] `jar/mchange-commons-java-0.2.19.jar` 存在
- [ ] `jar/mariadb-java-client-3.1.4.jar` 存在
- [ ] `jar/javolution-5.5.1.jar` 存在

### 配置文件檢查
- [ ] `config/sql.properties` 配置正確
- [ ] `config/c3p0-config.xml` 存在
- [ ] 資料庫連接參數正確
- [ ] 字符編碼設置正確

## 🎮 功能測試檢查

### 基本啟動測試
- [ ] `./start_server.sh` 執行成功
- [ ] 看到 "Welcome to Lineage" 訊息
- [ ] 連接池初始化成功
- [ ] 資料載入完成
- [ ] 無嚴重錯誤訊息

### 連接池功能測試
- [ ] 主資料庫連接池 (5-50) 初始化成功
- [ ] 登入資料庫連接池 (3-20) 初始化成功
- [ ] 連接測試 (CHECKIN) 通過
- [ ] 監控服務自動啟動

### 管理命令測試
- [ ] `.connpool status` 命令可用
- [ ] `.connpool report` 顯示詳細信息
- [ ] `.connpool health` 執行健康檢查
- [ ] 只有高權限管理員可使用

## 📊 監控系統檢查

### 自動監控
- [ ] ConnectionPoolMonitor 啟動成功
- [ ] ConnectionPoolHealthChecker 啟動成功
- [ ] 監控日誌正常輸出
- [ ] 無監控錯誤訊息

### 手動監控
- [ ] `./monitor_server.sh` 執行成功
- [ ] 系統資源顯示正常
- [ ] Java 進程資源顯示正常
- [ ] 網路連接狀態正常
- [ ] 資料庫狀態正常

### 日誌系統
- [ ] `logs/` 目錄創建成功
- [ ] 伺服器日誌正常輸出
- [ ] GC 日誌正常記錄
- [ ] 錯誤日誌文件存在

## 🔍 效能驗證檢查

### 資源使用
- [ ] CPU 使用率 < 60%
- [ ] 記憶體使用率 < 70%
- [ ] 連接池使用率 < 80%
- [ ] 磁碟 I/O 正常

### 響應時間
- [ ] 資料庫查詢 < 100ms
- [ ] 連接獲取 < 50ms
- [ ] 頁面載入 < 2s
- [ ] 登入響應 < 3s

### 穩定性測試
- [ ] 連續運行 1 小時無問題
- [ ] 記憶體使用穩定
- [ ] 無連接洩漏
- [ ] 無異常重啟

## 🛡️ 安全檢查

### 網路安全
- [ ] 防火牆規則配置正確
- [ ] 只開放必要端口 (2000, 2001)
- [ ] 資料庫不對外開放
- [ ] SSH 安全配置

### 權限安全
- [ ] lineage 用戶權限最小化
- [ ] 配置文件權限正確
- [ ] 日誌文件權限正確
- [ ] 資料庫權限最小化

## 🔄 服務管理檢查

### 系統服務
- [ ] `lineage.service` 文件配置正確
- [ ] 服務安裝成功
- [ ] 服務啟動成功
- [ ] 服務自動啟動設置

### 服務操作
- [ ] `systemctl start lineage` 成功
- [ ] `systemctl stop lineage` 成功
- [ ] `systemctl restart lineage` 成功
- [ ] `systemctl status lineage` 顯示正常

## 📝 文檔檢查

### 說明文件
- [ ] `README_COMPLETE_GUIDE.md` 存在
- [ ] `QUICK_REFERENCE.md` 存在
- [ ] `DEPLOYMENT_CHECKLIST.md` 存在 (本文件)
- [ ] `CONNECTION_POOL_OPTIMIZATION.md` 存在

### 技術文檔
- [ ] 配置參數說明完整
- [ ] 故障排除指南完整
- [ ] 監控指標說明清楚
- [ ] 維護建議詳細

## ✅ 部署完成確認

### 最終檢查
- [ ] 所有上述項目都已完成
- [ ] 伺服器穩定運行
- [ ] 監控系統正常
- [ ] 管理命令可用
- [ ] 效能指標正常

### 交付物品
- [ ] 完整的源碼和配置
- [ ] 所有優化腳本
- [ ] 完整的說明文檔
- [ ] 監控和管理工具
- [ ] 故障排除指南

## 🎉 部署成功！

恭喜！您的 Lineage 381a 伺服器已成功部署並優化。

### 下一步建議
1. 進行壓力測試
2. 監控運行狀況
3. 根據實際使用情況調整參數
4. 定期備份資料庫
5. 保持系統更新

### 技術支援
如遇到問題，請參考：
- `README_COMPLETE_GUIDE.md` - 完整指南
- `QUICK_REFERENCE.md` - 快速參考
- 日誌文件 - 錯誤診斷

---

**部署日期**: ___________  
**部署人員**: ___________  
**檢查人員**: ___________
