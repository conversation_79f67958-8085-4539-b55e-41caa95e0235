#!/bin/bash

# Lineage 381a 診斷腳本
# 專用路徑: /home/<USER>/381a

echo "=========================================="
echo "Lineage 381a 診斷工具"
echo "路徑: /home/<USER>/381a"
echo "=========================================="

# 切換到項目目錄
cd /home/<USER>/381a

echo "當前工作目錄: $(pwd)"
echo "當前用戶: $(whoami)"
echo

# 1. 系統環境檢查
echo "=== 1. 系統環境檢查 ==="
echo "作業系統: $(uname -a)"
echo "Ubuntu 版本: $(lsb_release -d 2>/dev/null | cut -f2 || echo '無法檢測')"
echo "記憶體: $(free -h | grep Mem | awk '{print $2}')"
echo "CPU 核心: $(nproc)"

# 2. Java 環境檢查
echo
echo "=== 2. Java 環境檢查 ==="
if command -v java &> /dev/null; then
    echo "✅ Java 運行環境: $(java -version 2>&1 | head -1)"
else
    echo "❌ Java 運行環境未安裝"
fi

if command -v javac &> /dev/null; then
    echo "✅ Java 編譯器: $(javac -version 2>&1)"
else
    echo "❌ Java 編譯器未安裝"
fi

if [ -n "$JAVA_HOME" ]; then
    echo "✅ JAVA_HOME: $JAVA_HOME"
else
    echo "⚠️  JAVA_HOME 未設置"
fi

# 3. 項目文件檢查
echo
echo "=== 3. 項目文件檢查 ==="
if [ -d "/home/<USER>/381a/src/main/java" ]; then
    java_count=$(find /home/<USER>/381a/src/main/java -name "*.java" | wc -l)
    echo "✅ 源碼目錄存在，包含 $java_count 個 Java 文件"
else
    echo "❌ 源碼目錄不存在"
fi

if [ -f "/home/<USER>/381a/src/main/java/com/lineage/Server.java" ]; then
    echo "✅ 主類源文件存在"
else
    echo "❌ 主類源文件不存在"
fi

if [ -f "/home/<USER>/381a/out/production/Lineage381a/com/lineage/Server.class" ]; then
    compile_time=$(stat -c %y "/home/<USER>/381a/out/production/Lineage381a/com/lineage/Server.class" 2>/dev/null)
    echo "✅ 主類已編譯 (時間: $compile_time)"
else
    echo "❌ 主類未編譯"
fi

# 4. 依賴文件檢查
echo
echo "=== 4. 依賴文件檢查 ==="
required_jars=(
    "jar/log4j-1.2.16.jar"
    "jar/commons-logging-1.2.jar"
    "jar/c3p0-0.9.5.5.jar"
    "jar/mchange-commons-java-0.2.19.jar"
    "jar/mariadb-java-client-3.1.4.jar"
    "jar/javolution-5.5.1.jar"
)

missing_count=0
for jar in "${required_jars[@]}"; do
    if [ -f "/home/<USER>/381a/$jar" ]; then
        size=$(du -h "/home/<USER>/381a/$jar" | cut -f1)
        echo "✅ $(basename $jar) ($size)"
    else
        echo "❌ $(basename $jar) 缺失"
        missing_count=$((missing_count + 1))
    fi
done

echo "依賴文件狀態: $((${#required_jars[@]} - missing_count))/${#required_jars[@]} 完整"

# 5. 配置文件檢查
echo
echo "=== 5. 配置文件檢查 ==="
if [ -f "/home/<USER>/381a/config/sql.properties" ]; then
    echo "✅ 資料庫配置文件存在"
    
    if grep -q "mariadb" /home/<USER>/381a/config/sql.properties; then
        echo "✅ MariaDB 驅動配置正確"
    else
        echo "⚠️  未檢測到 MariaDB 配置"
    fi
else
    echo "❌ 資料庫配置文件不存在"
fi

if [ -f "/home/<USER>/381a/config/c3p0-config.xml" ]; then
    echo "✅ 連接池配置文件存在"
else
    echo "❌ 連接池配置文件不存在"
fi

# 6. 腳本權限檢查
echo
echo "=== 6. 腳本權限檢查 ==="
scripts=("start_schung_381a.sh" "setup_schung_381a.sh" "diagnose_schung_381a.sh")

for script in "${scripts[@]}"; do
    if [ -f "/home/<USER>/381a/$script" ]; then
        if [ -x "/home/<USER>/381a/$script" ]; then
            echo "✅ $script (可執行)"
        else
            echo "⚠️  $script (不可執行)"
        fi
    else
        echo "❌ $script (不存在)"
    fi
done

# 7. 資料庫檢查
echo
echo "=== 7. 資料庫檢查 ==="
if command -v mysql &> /dev/null; then
    echo "✅ MySQL/MariaDB 客戶端已安裝"
else
    echo "❌ MySQL/MariaDB 客戶端未安裝"
fi

if systemctl is-active --quiet mariadb 2>/dev/null; then
    echo "✅ MariaDB 服務運行中"
elif systemctl is-active --quiet mysql 2>/dev/null; then
    echo "✅ MySQL 服務運行中"
else
    echo "❌ 資料庫服務未運行"
fi

# 8. 網路端口檢查
echo
echo "=== 8. 網路端口檢查 ==="
if netstat -tlnp 2>/dev/null | grep -q ":2000"; then
    echo "⚠️  端口 2000 已被占用"
    netstat -tlnp 2>/dev/null | grep ":2000"
else
    echo "✅ 端口 2000 可用"
fi

if netstat -tlnp 2>/dev/null | grep -q ":2001"; then
    echo "⚠️  端口 2001 已被占用"
    netstat -tlnp 2>/dev/null | grep ":2001"
else
    echo "✅ 端口 2001 可用"
fi

# 9. 記憶體檢查
echo
echo "=== 9. 記憶體檢查 ==="
total_mem=$(free -g | grep Mem | awk '{print $2}')
available_mem=$(free -g | grep Mem | awk '{print $7}')

echo "總記憶體: ${total_mem}GB"
echo "可用記憶體: ${available_mem}GB"

if [ $total_mem -ge 60 ]; then
    echo "✅ 記憶體充足，適合 64GB 配置"
elif [ $total_mem -ge 30 ]; then
    echo "⚠️  記憶體適中，建議 32GB 配置"
else
    echo "❌ 記憶體不足，需要調整配置"
fi

# 10. 日誌目錄檢查
echo
echo "=== 10. 日誌目錄檢查 ==="
if [ -d "/home/<USER>/381a/logs" ]; then
    log_count=$(ls -1 /home/<USER>/381a/logs/ 2>/dev/null | wc -l)
    echo "✅ 日誌目錄存在，包含 $log_count 個文件"
else
    echo "⚠️  日誌目錄不存在"
fi

# 11. 建議解決方案
echo
echo "=== 11. 建議解決方案 ==="

if [ $missing_count -gt 0 ]; then
    echo "🔧 缺少依賴文件:"
    echo "   請確保所有 JAR 文件都在 /home/<USER>/381a/jar/ 目錄中"
fi

if [ ! -f "/home/<USER>/381a/out/production/Lineage381a/com/lineage/Server.class" ]; then
    echo "🔧 需要編譯項目:"
    echo "   cd /home/<USER>/381a"
    echo "   ./setup_schung_381a.sh"
fi

if ! command -v java &> /dev/null; then
    echo "🔧 需要安裝 Java:"
    echo "   sudo apt update"
    echo "   sudo apt install openjdk-8-jdk"
fi

if ! systemctl is-active --quiet mariadb 2>/dev/null; then
    echo "🔧 需要啟動 MariaDB:"
    echo "   sudo systemctl start mariadb"
fi

echo
echo "=========================================="
echo "診斷完成！"
echo "=========================================="
echo "📁 項目路徑: /home/<USER>/381a"
echo "🚀 快速修復: ./setup_schung_381a.sh"
echo "▶️  啟動伺服器: ./start_schung_381a.sh"
