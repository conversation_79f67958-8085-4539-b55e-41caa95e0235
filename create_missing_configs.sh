#!/bin/bash

# 創建缺失的配置文件
# 解決 Lineage 381a 配置文件缺失問題

echo "=========================================="
echo "創建 Lineage 381a 缺失的配置文件"
echo "解決配置文件和編碼問題"
echo "=========================================="

cd /home/<USER>/381a

# 1. 創建完整的配置目錄結構
echo "=== 1. 創建配置目錄結構 ==="
mkdir -p config/powerKz
mkdir -p config/rates
mkdir -p config/server
mkdir -p config/npc
mkdir -p config/item
mkdir -p config/skill

echo "✅ 配置目錄結構已創建"

# 2. 創建各職業傷害設定表.properties
echo
echo "=== 2. 創建各職業傷害設定表.properties ==="
cat > config/powerKz/各職業傷害設定表.properties << 'EOF'
# Lineage 381a 各職業傷害設定表
# 編碼: UTF-8

# 王族 (Royal)
royal.damage.multiplier=1.0
royal.magic.multiplier=1.0
royal.defense.multiplier=1.0

# 騎士 (Knight)
knight.damage.multiplier=1.2
knight.magic.multiplier=0.8
knight.defense.multiplier=1.3

# 妖精 (Elf)
elf.damage.multiplier=0.9
elf.magic.multiplier=1.4
elf.defense.multiplier=0.8

# 法師 (Wizard)
wizard.damage.multiplier=0.7
wizard.magic.multiplier=1.6
wizard.defense.multiplier=0.6

# 黑妖 (Dark Elf)
darkelf.damage.multiplier=1.1
darkelf.magic.multiplier=1.2
darkelf.defense.multiplier=0.9

# 龍騎士 (Dragon Knight)
dragonknight.damage.multiplier=1.3
dragonknight.magic.multiplier=0.9
dragonknight.defense.multiplier=1.4

# 幻術師 (Illusionist)
illusionist.damage.multiplier=0.8
illusionist.magic.multiplier=1.5
illusionist.defense.multiplier=0.7

# 戰士 (Warrior)
warrior.damage.multiplier=1.4
warrior.magic.multiplier=0.6
warrior.defense.multiplier=1.5
EOF

echo "✅ 各職業傷害設定表.properties 已創建"

# 3. 創建其他可能缺失的配置文件
echo
echo "=== 3. 創建其他配置文件 ==="

# 創建伺服器基本配置
cat > config/server/server.properties << 'EOF'
# Lineage 381a 伺服器配置
# 編碼: UTF-8

# 伺服器基本設置
server.name=Lineage381a
server.port=2000
login.port=2001
max.players=1000

# 經驗值設置
exp.rate=1.0
sp.rate=1.0
adena.rate=1.0
drop.rate=1.0

# PK 設置
pk.enabled=true
pk.count.for.chaotic=5

# 安全區域設置
safe.zone.enabled=true
EOF

# 創建速率配置
cat > config/rates/rates.properties << 'EOF'
# Lineage 381a 速率配置
# 編碼: UTF-8

# 基本速率
base.exp.rate=1.0
base.sp.rate=1.0
base.adena.rate=1.0
base.drop.rate=1.0

# 職業經驗加成
royal.exp.bonus=0.0
knight.exp.bonus=0.0
elf.exp.bonus=0.0
wizard.exp.bonus=0.0
darkelf.exp.bonus=0.0
dragonknight.exp.bonus=0.0
illusionist.exp.bonus=0.0
warrior.exp.bonus=0.0
EOF

# 創建 NPC 配置
cat > config/npc/npc.properties << 'EOF'
# Lineage 381a NPC 配置
# 編碼: UTF-8

# NPC 基本設置
npc.respawn.enabled=true
npc.respawn.time=300000

# NPC AI 設置
npc.ai.enabled=true
npc.ai.update.interval=1000

# 商店 NPC 設置
shop.npc.enabled=true
shop.discount.rate=0.0
EOF

echo "✅ 其他配置文件已創建"

# 4. 修復 sql.properties 編碼問題
echo
echo "=== 4. 修復 sql.properties 編碼問題 ==="
if [ -f "config/sql.properties" ]; then
    # 備份原文件
    cp config/sql.properties config/sql.properties.backup
    
    # 檢查文件編碼
    file_encoding=$(file -i config/sql.properties | grep -o 'charset=[^;]*' | cut -d= -f2)
    echo "原始編碼: $file_encoding"
    
    if [ "$file_encoding" != "utf-8" ] && [ "$file_encoding" != "us-ascii" ]; then
        echo "轉換編碼到 UTF-8..."
        iconv -f GBK -t UTF-8 config/sql.properties > config/sql.properties.utf8
        mv config/sql.properties.utf8 config/sql.properties
        echo "✅ sql.properties 編碼已轉換為 UTF-8"
    else
        echo "✅ sql.properties 編碼正常"
    fi
else
    echo "創建新的 sql.properties..."
    cat > config/sql.properties << 'EOF'
#-------------------------------------------------------------
# SQL config - MariaDB Configuration
# 編碼: UTF-8
#-------------------------------------------------------------

# 登入資料庫驅動
Driver_LOGIN = org.mariadb.jdbc.Driver
Driver = org.mariadb.jdbc.Driver

# 資料庫連接 URL
URL1_LOGIN = ************************/
URL2_LOGIN = lineage
URL3_LOGIN = ?useUnicode=true&characterEncoding=utf8mb4&serverTimezone=Asia/Taipei&useSSL=false&allowPublicKeyRetrieval=true

# 登入資料庫帳號密碼
Login_LOGIN = root
Password_LOGIN = 

# 主資料庫連接
URL1 = ************************/
URL2 = lineage
URL3 = ?useUnicode=true&characterEncoding=utf8mb4&serverTimezone=Asia/Taipei&useSSL=false&allowPublicKeyRetrieval=true

# 主資料庫帳號密碼
Login = root
Password = 
EOF
    echo "✅ 新的 sql.properties 已創建"
fi

# 5. 設置正確的文件編碼和權限
echo
echo "=== 5. 設置文件編碼和權限 ==="
find config -name "*.properties" -exec chmod 644 {} \;
echo "✅ 配置文件權限已設置"

# 確保所有配置文件都是 UTF-8 編碼
find config -name "*.properties" -exec file -i {} \; | grep -v utf-8 | while read line; do
    file_path=$(echo $line | cut -d: -f1)
    echo "轉換文件編碼: $file_path"
    iconv -f GBK -t UTF-8 "$file_path" > "${file_path}.utf8"
    mv "${file_path}.utf8" "$file_path"
done

echo "✅ 所有配置文件編碼已檢查"

# 6. 創建配置文件清單
echo
echo "=== 6. 配置文件清單 ==="
echo "已創建的配置文件:"
find config -name "*.properties" -type f | sort

echo
echo "配置目錄結構:"
tree config/ 2>/dev/null || find config -type d | sort

# 7. 檢查配置文件內容
echo
echo "=== 7. 檢查關鍵配置文件 ==="
echo "各職業傷害設定表.properties 內容預覽:"
head -5 config/powerKz/各職業傷害設定表.properties

echo
echo "sql.properties 內容預覽:"
head -10 config/sql.properties

echo
echo "=========================================="
echo "配置文件創建完成！"
echo "=========================================="
echo "✅ 已解決的問題:"
echo "   - 創建缺失的 powerKz/各職業傷害設定表.properties"
echo "   - 修復 sql.properties 編碼問題"
echo "   - 創建完整的配置目錄結構"
echo "   - 設置正確的文件權限"
echo
echo "⚠️  重要提醒:"
echo "1. 請編輯 config/sql.properties 設置資料庫密碼"
echo "2. 根據需要調整各職業傷害設定"
echo "3. 確保資料庫 'lineage' 存在並有正確的表結構"
echo
echo "下一步:"
echo "1. 編輯資料庫配置: nano config/sql.properties"
echo "2. 測試啟動: ./test_minimal_start.sh"
echo "3. 如果需要更多配置文件，請告知具體錯誤信息"
