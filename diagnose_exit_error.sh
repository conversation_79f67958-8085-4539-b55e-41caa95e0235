#!/bin/bash

# 診斷伺服器退出錯誤腳本
# 分析退出碼 1 的原因

echo "=========================================="
echo "Lineage 381a 錯誤診斷工具"
echo "分析退出碼 1 的原因"
echo "=========================================="

cd /home/<USER>/381a

# 設置 Java 環境
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 尋找 JAR 文件
JAR_FILES=(
    "Lineage381a.jar"
    "Lineage381a_Ubuntu.jar"
    "lineage381a.jar"
    "server.jar"
)

JAR_FILE=""
for jar in "${JAR_FILES[@]}"; do
    if [ -f "$jar" ]; then
        JAR_FILE="$jar"
        break
    fi
done

if [ -z "$JAR_FILE" ]; then
    echo "❌ 找不到 JAR 文件"
    exit 1
fi

echo "✅ 使用 JAR 文件: $JAR_FILE"

# 1. 檢查 JAR 文件完整性
echo
echo "=== 1. JAR 文件完整性檢查 ==="
jar tf "$JAR_FILE" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ JAR 文件結構正常"
else
    echo "❌ JAR 文件損壞"
    exit 1
fi

# 檢查 JAR 文件大小
JAR_SIZE=$(du -h "$JAR_FILE" | cut -f1)
echo "JAR 文件大小: $JAR_SIZE"

# 2. 檢查 MANIFEST.MF
echo
echo "=== 2. MANIFEST.MF 檢查 ==="
jar xf "$JAR_FILE" META-INF/MANIFEST.MF 2>/dev/null
if [ -f "META-INF/MANIFEST.MF" ]; then
    echo "MANIFEST.MF 內容:"
    cat META-INF/MANIFEST.MF
    echo
    
    if grep -q "Main-Class.*com.lineage.Server" META-INF/MANIFEST.MF; then
        echo "✅ Main-Class 設置正確"
    else
        echo "❌ Main-Class 設置錯誤"
    fi
    rm -rf META-INF
else
    echo "❌ MANIFEST.MF 不存在"
fi

# 3. 檢查主類
echo
echo "=== 3. 主類檢查 ==="
if jar tf "$JAR_FILE" | grep -q "com/lineage/Server.class"; then
    echo "✅ 主類 Server.class 存在"
else
    echo "❌ 主類 Server.class 不存在"
    echo "JAR 中的主要類文件:"
    jar tf "$JAR_FILE" | grep "\.class$" | grep -E "(Server|Main)" | head -5
fi

# 4. 檢查依賴庫
echo
echo "=== 4. 依賴庫檢查 ==="
dependencies=(
    "org/apache/log4j/Logger.class:log4j"
    "org/apache/commons/logging/Log.class:commons-logging"
    "com/mchange/v2/c3p0/ComboPooledDataSource.class:c3p0"
    "org/mariadb/jdbc/Driver.class:mariadb-jdbc"
    "javolution/util/FastMap.class:javolution"
)

for dep_info in "${dependencies[@]}"; do
    dep_class=$(echo $dep_info | cut -d: -f1)
    dep_name=$(echo $dep_info | cut -d: -f2)
    
    if jar tf "$JAR_FILE" | grep -q "$dep_class"; then
        echo "✅ $dep_name"
    else
        echo "❌ $dep_name (缺失)"
    fi
done

# 5. 檢查配置文件
echo
echo "=== 5. 配置文件檢查 ==="
config_files=(
    "config/sql.properties"
    "config/c3p0-config.xml"
    "sql.properties"
    "c3p0-config.xml"
)

config_found=false
for config in "${config_files[@]}"; do
    if jar tf "$JAR_FILE" | grep -q "$config"; then
        echo "✅ $config (在 JAR 中)"
        config_found=true
    fi
done

# 檢查外部配置文件
if [ -f "config/sql.properties" ]; then
    echo "✅ config/sql.properties (外部文件)"
    config_found=true
fi

if [ "$config_found" = false ]; then
    echo "❌ 找不到配置文件"
fi

# 6. 檢查資料庫連接
echo
echo "=== 6. 資料庫連接檢查 ==="
if systemctl is-active --quiet mariadb 2>/dev/null; then
    echo "✅ MariaDB 服務運行中"
    
    # 嘗試連接資料庫
    if command -v mysql &> /dev/null; then
        echo "測試資料庫連接..."
        mysql -u root -e "SELECT 1;" 2>/dev/null
        if [ $? -eq 0 ]; then
            echo "✅ 資料庫連接正常"
        else
            echo "❌ 資料庫連接失敗 (可能需要密碼)"
        fi
    fi
else
    echo "❌ MariaDB 服務未運行"
    echo "請執行: sudo systemctl start mariadb"
fi

# 7. 檢查端口占用
echo
echo "=== 7. 端口檢查 ==="
if netstat -tlnp 2>/dev/null | grep -q ":2000"; then
    echo "❌ 端口 2000 已被占用"
    netstat -tlnp 2>/dev/null | grep ":2000"
else
    echo "✅ 端口 2000 可用"
fi

if netstat -tlnp 2>/dev/null | grep -q ":2001"; then
    echo "❌ 端口 2001 已被占用"
    netstat -tlnp 2>/dev/null | grep ":2001"
else
    echo "✅ 端口 2001 可用"
fi

# 8. 記憶體檢查
echo
echo "=== 8. 記憶體檢查 ==="
TOTAL_MEM=$(free -g | grep Mem | awk '{print $2}')
AVAILABLE_MEM=$(free -g | grep Mem | awk '{print $7}')
USED_MEM=$(free -g | grep Mem | awk '{print $3}')

echo "總記憶體: ${TOTAL_MEM}GB"
echo "已使用: ${USED_MEM}GB"
echo "可用記憶體: ${AVAILABLE_MEM}GB"

if [ $AVAILABLE_MEM -lt 4 ]; then
    echo "❌ 可用記憶體不足 (< 4GB)"
else
    echo "✅ 記憶體充足"
fi

# 9. 詳細啟動測試
echo
echo "=== 9. 詳細啟動測試 ==="
echo "使用詳細輸出模式啟動伺服器..."
echo "這將顯示具體的錯誤信息"
echo

# 創建日誌目錄
mkdir -p logs

# 使用最小記憶體配置進行測試
JVM_OPTS="-Xms1g -Xmx2g"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Taipei"
JVM_OPTS="$JVM_OPTS -verbose:class"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCDetails"

echo "測試啟動命令:"
echo "java $JVM_OPTS -jar $JAR_FILE"
echo
echo "開始測試 (10秒超時)..."
echo "----------------------------------------"

# 使用 timeout 限制執行時間，並捕獲輸出
timeout 10s java $JVM_OPTS -jar "$JAR_FILE" 2>&1 | tee logs/startup_test.log

exit_code=$?
echo "----------------------------------------"
echo "測試退出碼: $exit_code"

# 分析退出碼
case $exit_code in
    0)
        echo "✅ 正常退出"
        ;;
    1)
        echo "❌ 一般錯誤 (通常是 Java 異常)"
        ;;
    124)
        echo "⏰ 超時退出 (這是正常的，因為我們設置了 10 秒超時)"
        ;;
    *)
        echo "❌ 其他錯誤 (退出碼: $exit_code)"
        ;;
esac

echo
echo "=== 10. 錯誤分析和建議 ==="

# 檢查日誌文件
if [ -f "logs/startup_test.log" ]; then
    echo "檢查啟動日誌中的錯誤..."
    
    if grep -q "ClassNotFoundException" logs/startup_test.log; then
        echo "❌ 發現 ClassNotFoundException - 缺少依賴類"
    fi
    
    if grep -q "NoClassDefFoundError" logs/startup_test.log; then
        echo "❌ 發現 NoClassDefFoundError - 類定義錯誤"
    fi
    
    if grep -q "SQLException" logs/startup_test.log; then
        echo "❌ 發現 SQLException - 資料庫連接問題"
    fi
    
    if grep -q "BindException" logs/startup_test.log; then
        echo "❌ 發現 BindException - 端口被占用"
    fi
    
    if grep -q "OutOfMemoryError" logs/startup_test.log; then
        echo "❌ 發現 OutOfMemoryError - 記憶體不足"
    fi
    
    echo
    echo "最後幾行日誌:"
    tail -10 logs/startup_test.log
fi

echo
echo "=========================================="
echo "診斷完成"
echo "=========================================="
echo "請根據上述檢查結果解決相應問題"
echo "詳細日誌保存在: logs/startup_test.log"
