#!/bin/bash

# Ubuntu 24.02 系統優化腳本
# 針對 Lineage 伺服器進行系統級優化

echo "=========================================="
echo "Ubuntu 24.02 Lineage 伺服器系統優化"
echo "=========================================="

# 檢查是否為 root 用戶
if [ "$EUID" -ne 0 ]; then
    echo "請使用 sudo 執行此腳本"
    exit 1
fi

# 1. 網路參數優化
echo "1. 優化網路參數..."
cat >> /etc/sysctl.conf << EOF

# Lineage 伺服器網路優化
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.core.rmem_default = 262144
net.core.wmem_default = 262144
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_window_scaling = 1
net.ipv4.tcp_timestamps = 1
net.ipv4.tcp_sack = 1
net.ipv4.tcp_no_metrics_save = 1
net.ipv4.tcp_moderate_rcvbuf = 1
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.tcp_max_tw_buckets = 5000
net.ipv4.tcp_fastopen = 3
net.ipv4.tcp_mem = 25600 51200 102400
net.ipv4.tcp_max_orphans = 3276800
net.ipv4.ip_local_port_range = 1024 65535
EOF

# 2. 文件描述符限制優化
echo "2. 優化文件描述符限制..."
cat >> /etc/security/limits.conf << EOF

# Lineage 伺服器文件描述符優化
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
EOF

# 3. 創建 lineage 用戶 (如果不存在)
echo "3. 檢查 lineage 用戶..."
if ! id "lineage" &>/dev/null; then
    echo "創建 lineage 用戶..."
    useradd -m -s /bin/bash lineage
    echo "lineage 用戶已創建"
else
    echo "lineage 用戶已存在"
fi

# 4. Java 環境檢查和優化
echo "4. 檢查 Java 環境..."
if ! command -v java &> /dev/null; then
    echo "安裝 OpenJDK 8..."
    apt update
    apt install -y openjdk-8-jdk
else
    echo "Java 已安裝: $(java -version 2>&1 | head -n 1)"
fi

# 5. MariaDB 優化建議
echo "5. MariaDB 優化建議..."
if command -v mysql &> /dev/null; then
    echo "檢測到 MariaDB/MySQL，建議優化配置："
    echo "  - innodb_buffer_pool_size = 70% of RAM"
    echo "  - max_connections = 500"
    echo "  - query_cache_size = 256M"
    echo "  - innodb_log_file_size = 256M"
fi

# 6. 防火牆配置
echo "6. 配置防火牆..."
if command -v ufw &> /dev/null; then
    echo "配置 UFW 防火牆..."
    ufw allow 2000/tcp comment "Lineage Game Port"
    ufw allow 2001/tcp comment "Lineage Login Port"
    echo "防火牆規則已添加"
fi

# 7. 系統監控工具安裝
echo "7. 安裝系統監控工具..."
apt install -y htop iotop nethogs sysstat

# 8. 創建日誌輪轉配置
echo "8. 配置日誌輪轉..."
cat > /etc/logrotate.d/lineage << EOF
/path/to/your/lineage/server/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 lineage lineage
    postrotate
        /bin/kill -USR1 \$(cat /path/to/your/lineage/server/lineage.pid 2>/dev/null) 2>/dev/null || true
    endscript
}
EOF

# 9. 應用系統參數
echo "9. 應用系統參數..."
sysctl -p

echo "=========================================="
echo "系統優化完成！"
echo "=========================================="
echo "建議重啟系統以確保所有優化生效"
echo ""
echo "下一步："
echo "1. 重啟系統: sudo reboot"
echo "2. 設置伺服器路徑: 編輯 lineage.service 文件"
echo "3. 安裝服務: sudo cp lineage.service /etc/systemd/system/"
echo "4. 啟用服務: sudo systemctl enable lineage"
echo "5. 啟動服務: sudo systemctl start lineage"
