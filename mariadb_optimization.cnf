# MariaDB 優化配置文件
# 適用於 Ubuntu 24.02 + Lineage 381a
# 將此文件複製到 /etc/mysql/mariadb.conf.d/99-lineage.cnf

[mysqld]
# 基本設置
bind-address = 127.0.0.1
port = 3306
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid
datadir = /var/lib/mysql
tmpdir = /tmp

# 字符集設置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4'

# 連接設置 (針對 64GB RAM 超高效能硬體)
max_connections = 2000
max_connect_errors = 1000
connect_timeout = 60
wait_timeout = 28800
interactive_timeout = 28800
net_read_timeout = 30
net_write_timeout = 60

# 緩衝區設置 (針對 64GB RAM 優化)
# 設置為系統記憶體的 37.5% (24GB)，為 JVM 保留 32GB
innodb_buffer_pool_size = 24G
innodb_buffer_pool_instances = 16
innodb_log_buffer_size = 64M

# 查詢快取 (64GB RAM 版本)
query_cache_type = 1
query_cache_size = 1G
query_cache_limit = 4M

# 排序和分組
sort_buffer_size = 2M
read_buffer_size = 1M
read_rnd_buffer_size = 2M
join_buffer_size = 2M
tmp_table_size = 256M
max_heap_table_size = 256M

# InnoDB 設置
innodb_file_per_table = 1
innodb_flush_log_at_trx_commit = 2
innodb_log_file_size = 256M
innodb_log_files_in_group = 2
innodb_flush_method = O_DIRECT
innodb_lock_wait_timeout = 50
innodb_rollback_on_timeout = 1
innodb_thread_concurrency = 0
innodb_io_capacity = 2000
innodb_io_capacity_max = 4000

# 二進制日誌 (可選，用於備份)
# log-bin = mysql-bin
# binlog_format = ROW
# expire_logs_days = 7
# max_binlog_size = 100M

# 慢查詢日誌
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1

# 錯誤日誌
log_error = /var/log/mysql/error.log

# 一般查詢日誌 (調試時開啟)
# general_log = 1
# general_log_file = /var/log/mysql/general.log

# 表快取
table_open_cache = 4000
table_definition_cache = 2000

# 線程設置
thread_cache_size = 50
thread_stack = 256K

# MyISAM 設置 (如果使用 MyISAM 表)
key_buffer_size = 256M
myisam_sort_buffer_size = 64M
myisam_max_sort_file_size = 2G
myisam_repair_threads = 1

# 安全設置
local_infile = 0
skip_name_resolve = 1

[mysql]
default-character-set = utf8mb4

[mysqldump]
quick
quote-names
max_allowed_packet = 64M

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/run/mysqld/mysqld.sock
