#!/bin/bash

# Lineage 381a Ubuntu JAR 啟動腳本
# 針對 i7-6700K + 64GB RAM 優化
# 路徑: /home/<USER>/381a

echo "=========================================="
echo "Lineage 381a Server - JAR 版本"
echo "硬體: i7-6700K + 64GB RAM"
echo "路徑: /home/<USER>/381a"
echo "=========================================="

# 切換到正確目錄
cd /home/<USER>/381a

echo "當前目錄: $(pwd)"

# 設置 Java 環境
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 檢查 Java 環境
if ! command -v java &> /dev/null; then
    echo "❌ Java 未安裝"
    echo "請執行: sudo apt install openjdk-8-jdk"
    exit 1
fi

echo "✅ Java 版本:"
java -version
echo

# 尋找 JAR 文件
JAR_FILES=(
    "Lineage381a.jar"
    "Lineage381a_Ubuntu.jar"
    "lineage381a.jar"
    "server.jar"
)

JAR_FILE=""
for jar in "${JAR_FILES[@]}"; do
    if [ -f "$jar" ]; then
        JAR_FILE="$jar"
        break
    fi
done

# 如果沒找到，列出所有 JAR 文件
if [ -z "$JAR_FILE" ]; then
    echo "❌ 找不到 JAR 文件"
    echo "當前目錄中的 JAR 文件:"
    find . -name "*.jar" -type f 2>/dev/null
    echo
    echo "請確保以下任一 JAR 文件存在:"
    for jar in "${JAR_FILES[@]}"; do
        echo "  - $jar"
    done
    echo
    echo "或者將您的 JAR 文件重命名為 Lineage381a.jar"
    exit 1
fi

echo "✅ 找到 JAR 文件: $JAR_FILE"

# 檢查 JAR 文件大小
JAR_SIZE=$(du -h "$JAR_FILE" | cut -f1)
echo "✅ JAR 文件大小: $JAR_SIZE"

# 檢查 JAR 文件中的 MANIFEST
echo "檢查 JAR 文件內容..."
if jar tf "$JAR_FILE" | grep -q "META-INF/MANIFEST.MF"; then
    echo "✅ MANIFEST.MF 存在"
    
    # 檢查 Main-Class
    MAIN_CLASS=$(jar xf "$JAR_FILE" META-INF/MANIFEST.MF && grep "Main-Class" META-INF/MANIFEST.MF | cut -d: -f2 | tr -d ' \r\n' 2>/dev/null)
    if [ -n "$MAIN_CLASS" ]; then
        echo "✅ Main-Class: $MAIN_CLASS"
    else
        echo "⚠️  Main-Class 未設置或無法讀取"
    fi
    
    # 清理臨時文件
    rm -rf META-INF 2>/dev/null
else
    echo "❌ MANIFEST.MF 不存在"
fi

# 檢查是否包含主類
if jar tf "$JAR_FILE" | grep -q "com/lineage/Server.class"; then
    echo "✅ 主類 Server.class 存在於 JAR 中"
else
    echo "❌ 主類 Server.class 不存在於 JAR 中"
    echo "請檢查 JAR 構建配置"
fi

# 創建日誌目錄
mkdir -p /home/<USER>/381a/logs
echo "✅ 日誌目錄已創建"

# 設置 JVM 參數 (針對 64GB RAM 優化)
JVM_OPTS="-Xms16g -Xmx32g"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=100"
JVM_OPTS="$JVM_OPTS -XX:G1HeapRegionSize=32m"
JVM_OPTS="$JVM_OPTS -XX:G1NewSizePercent=20"
JVM_OPTS="$JVM_OPTS -XX:G1MaxNewSizePercent=30"
JVM_OPTS="$JVM_OPTS -XX:+UnlockExperimentalVMOptions"
JVM_OPTS="$JVM_OPTS -XX:+UseStringDeduplication"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Taipei"
JVM_OPTS="$JVM_OPTS -Djava.net.preferIPv4Stack=true"
JVM_OPTS="$JVM_OPTS -Xloggc:/home/<USER>/381a/logs/gc.log"
JVM_OPTS="$JVM_OPTS -XX:+UseGCLogFileRotation"
JVM_OPTS="$JVM_OPTS -XX:NumberOfGCLogFiles=5"
JVM_OPTS="$JVM_OPTS -XX:GCLogFileSize=10M"

echo "JVM 記憶體配置: 16GB-32GB (針對 64GB RAM 優化)"
echo "日誌目錄: /home/<USER>/381a/logs"
echo

# 檢查可用記憶體
AVAILABLE_MEM=$(free -g | grep Mem | awk '{print $7}')
if [ $AVAILABLE_MEM -lt 35 ]; then
    echo "⚠️  警告: 可用記憶體只有 ${AVAILABLE_MEM}GB"
    echo "⚠️  建議至少有 35GB 可用記憶體來運行 32GB JVM"
    read -p "是否繼續? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "已取消啟動"
        exit 1
    fi
fi

echo "=========================================="
echo "啟動 Lineage 381a 伺服器..."
echo "=========================================="
echo "JAR 文件: $JAR_FILE"
echo "JVM 參數: $JVM_OPTS"
echo "工作目錄: $(pwd)"
echo "=========================================="

# 啟動 JAR 文件
java $JVM_OPTS -jar "$JAR_FILE"

# 檢查退出狀態
exit_code=$?
echo
echo "=========================================="
if [ $exit_code -eq 0 ]; then
    echo "✅ 伺服器正常關閉"
else
    echo "❌ 伺服器異常退出，退出碼: $exit_code"
    echo "請檢查日誌文件:"
    echo "  - /home/<USER>/381a/logs/gc.log"
    echo "  - /home/<USER>/381a/logs/server.log (如果存在)"
    echo
    echo "常見問題解決:"
    echo "1. 檢查 JAR 文件是否包含所有依賴"
    echo "2. 檢查 MANIFEST.MF 中的 Main-Class 設置"
    echo "3. 檢查配置文件是否包含在 JAR 中"
    echo "4. 檢查資料庫連接配置"
fi
echo "=========================================="
