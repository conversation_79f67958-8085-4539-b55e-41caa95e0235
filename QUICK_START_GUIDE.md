# 🚀 Lineage 381a 快速啟動指南

## 硬體配置確認
- ✅ **CPU**: i7-6700K (4核8線程)
- ✅ **記憶體**: 32GB DDR4
- ✅ **儲存**: 1TB PCIe SSD
- ✅ **網路**: 雙2.5G網卡 + 1G外線
- ✅ **系統**: Ubuntu 24.02

## 🎯 一鍵部署流程

### 步驟 1: 驗證配置
```bash
chmod +x verify_configuration.sh
./verify_configuration.sh
```

### 步驟 2: 系統優化
```bash
chmod +x ubuntu_optimization_6700k_32gb.sh
sudo ./ubuntu_optimization_6700k_32gb.sh
sudo reboot
```

### 步驟 3: 資料庫優化
```bash
# 應用 MariaDB 優化配置
sudo cp mariadb_optimization.cnf /etc/mysql/mariadb.conf.d/99-lineage.cnf
sudo systemctl restart mariadb

# 執行資料庫優化腳本
mysql -u root -p < sql/database_optimization.sql
mysql -u root -p < sql/add_connpool_command.sql
```

### 步驟 4: 啟動伺服器
```bash
chmod +x start_server.sh
./start_server.sh
```

### 步驟 5: 監控系統
```bash
chmod +x monitor_server.sh
./monitor_server.sh watch
```

## 📊 優化後的配置摘要

### JVM 配置 (針對 32GB RAM)
```
堆記憶體: 8GB-16GB
垃圾收集器: G1GC
GC 暫停時間: < 100ms
大頁面記憶體: 啟用
```

### 資料庫配置 (MariaDB)
```
緩衝池: 12GB (40% 系統記憶體)
最大連接數: 1000
查詢快取: 512MB
字符編碼: utf8mb4
```

### 連接池配置
```
主資料庫: 10-100 連接
登入資料庫: 5-40 連接
監控間隔: 5分鐘
健康檢查: 10分鐘
```

### 網路優化 (雙2.5G網卡)
```
TCP 接收緩衝: 128MB
TCP 發送緩衝: 128MB
擁塞控制: BBR
最大連接數: 16384
```

## 🎮 預期效能

### 玩家支援能力
- **開服初期**: 200-300 玩家
- **穩定運營**: 500-600 玩家  
- **高峰期**: 最多 800 玩家

### 效能指標
- **資料庫查詢**: < 50ms
- **記憶體使用率**: < 60%
- **CPU 使用率**: < 70%
- **網路延遲**: < 10ms

## 🔧 管理命令

### 遊戲內命令 (GM 200+ 權限)
```
.connpool status     # 連接池狀態
.connpool report     # 詳細報告
.connpool health     # 健康檢查
.connpool monitor    # 監控管理
```

### 系統命令
```bash
# 伺服器監控
./monitor_server.sh status    # 狀態檢查
./monitor_server.sh resources # 資源使用
./monitor_server.sh java      # Java 進程
./monitor_server.sh network   # 網路狀態
./monitor_server.sh database  # 資料庫狀態

# 服務管理
sudo systemctl status lineage   # 服務狀態
sudo systemctl restart lineage  # 重啟服務
sudo journalctl -u lineage -f   # 查看日誌
```

## ⚠️ 重要注意事項

### 記憶體分配
- **JVM**: 16GB (50% 系統記憶體)
- **MariaDB**: 12GB (37.5% 系統記憶體)
- **系統保留**: 4GB (12.5% 系統記憶體)

### CPU 使用
- **正常負載**: 30-50%
- **高負載警告**: > 70%
- **臨界警告**: > 85%

### 網路頻寬
- **內網**: 雙2.5G (充足)
- **外網**: 1G (可能成為瓶頸)
- **建議**: 監控外網使用率

## 🔍 故障排除

### 常見問題
1. **啟動失敗**: 檢查 Java classpath 和依賴
2. **記憶體不足**: 調整 JVM 參數
3. **連接池耗盡**: 檢查連接洩漏
4. **資料庫慢**: 檢查索引和查詢

### 快速診斷
```bash
# 檢查進程
ps aux | grep java

# 檢查記憶體
free -h

# 檢查連接
netstat -tlnp | grep java

# 檢查日誌
tail -f logs/server.log
```

## 📈 效能調優

### 根據玩家數量調整

#### < 200 玩家
```bash
# 降低資源使用
JVM: -Xms4g -Xmx8g
連接池: 主5-30, 登入3-15
```

#### 200-500 玩家 (當前配置)
```bash
# 標準配置
JVM: -Xms8g -Xmx16g
連接池: 主10-100, 登入5-40
```

#### > 500 玩家
```bash
# 提升配置
JVM: -Xms12g -Xmx20g
連接池: 主15-150, 登入8-60
MariaDB: innodb_buffer_pool_size = 16G
```

## 🎯 成功指標

### 部署成功確認
- [ ] 伺服器正常啟動
- [ ] 連接池初始化成功
- [ ] 資料載入完成
- [ ] 監控服務運行
- [ ] 管理命令可用

### 運行穩定確認
- [ ] 記憶體使用穩定
- [ ] CPU 使用正常
- [ ] 無連接洩漏
- [ ] 資料庫響應正常
- [ ] 網路連接穩定

## 📞 技術支援

### 日誌位置
- **伺服器**: `logs/server.log`
- **錯誤**: `logs/error.log`
- **GC**: `logs/gc.log`
- **系統**: `sudo journalctl -u lineage`

### 監控工具
- **系統監控**: `htop`, `iotop`, `nethogs`
- **Java 監控**: `jstat`, `jmap`, `jstack`
- **資料庫監控**: `mysqladmin status`

---

## 🎉 恭喜！

您的 Lineage 381a 伺服器現在已經針對您的高效能硬體進行了全面優化！

**預期效能**: 支援 500-800 同時在線玩家的穩定運行 🚀
