#!/bin/bash

# Lineage 伺服器監控腳本
# 適用於 Ubuntu 24.02

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 獲取伺服器 PID
get_server_pid() {
    pgrep -f "com.lineage.Server"
}

# 檢查伺服器狀態
check_server_status() {
    local pid=$(get_server_pid)
    if [ -n "$pid" ]; then
        echo -e "${GREEN}✓ 伺服器正在運行 (PID: $pid)${NC}"
        return 0
    else
        echo -e "${RED}✗ 伺服器未運行${NC}"
        return 1
    fi
}

# 顯示系統資源使用情況
show_system_resources() {
    echo -e "${BLUE}=== 系統資源使用情況 ===${NC}"
    
    # CPU 使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    echo -e "CPU 使用率: ${YELLOW}${cpu_usage}%${NC}"
    
    # 記憶體使用情況
    local mem_info=$(free -h | grep "Mem:")
    local mem_used=$(echo $mem_info | awk '{print $3}')
    local mem_total=$(echo $mem_info | awk '{print $2}')
    echo -e "記憶體使用: ${YELLOW}${mem_used}/${mem_total}${NC}"
    
    # 磁碟使用情況
    echo -e "磁碟使用情況:"
    df -h | grep -E "^/dev/" | awk '{printf "  %s: %s/%s (%s)\n", $1, $3, $2, $5}'
}

# 顯示 Java 進程資源使用
show_java_resources() {
    local pid=$(get_server_pid)
    if [ -n "$pid" ]; then
        echo -e "${BLUE}=== Java 進程資源使用 ===${NC}"
        
        # CPU 和記憶體使用
        local process_info=$(ps -p $pid -o pid,pcpu,pmem,rss,vsz,etime --no-headers)
        if [ -n "$process_info" ]; then
            local cpu=$(echo $process_info | awk '{print $2}')
            local mem=$(echo $process_info | awk '{print $3}')
            local rss=$(echo $process_info | awk '{print $4}')
            local vsz=$(echo $process_info | awk '{print $5}')
            local etime=$(echo $process_info | awk '{print $6}')
            
            echo -e "PID: ${YELLOW}$pid${NC}"
            echo -e "CPU 使用率: ${YELLOW}${cpu}%${NC}"
            echo -e "記憶體使用率: ${YELLOW}${mem}%${NC}"
            echo -e "實際記憶體: ${YELLOW}$(($rss/1024)) MB${NC}"
            echo -e "虛擬記憶體: ${YELLOW}$(($vsz/1024)) MB${NC}"
            echo -e "運行時間: ${YELLOW}${etime}${NC}"
        fi
        
        # 線程數
        local thread_count=$(ps -p $pid -o nlwp --no-headers)
        echo -e "線程數: ${YELLOW}${thread_count}${NC}"
        
        # 文件描述符使用
        if [ -d "/proc/$pid/fd" ]; then
            local fd_count=$(ls /proc/$pid/fd | wc -l)
            echo -e "文件描述符: ${YELLOW}${fd_count}${NC}"
        fi
    fi
}

# 顯示網路連接情況
show_network_connections() {
    echo -e "${BLUE}=== 網路連接情況 ===${NC}"
    
    # 監聽端口
    echo "監聽端口:"
    netstat -tlnp 2>/dev/null | grep java | while read line; do
        local port=$(echo $line | awk '{print $4}' | cut -d':' -f2)
        echo -e "  Port ${YELLOW}${port}${NC}: LISTENING"
    done
    
    # 連接數統計
    local established=$(netstat -an | grep :2000 | grep ESTABLISHED | wc -l)
    echo -e "已建立連接數: ${YELLOW}${established}${NC}"
}

# 顯示資料庫連接情況
show_database_connections() {
    echo -e "${BLUE}=== 資料庫連接情況 ===${NC}"
    
    # 檢查 MariaDB 是否運行
    if systemctl is-active --quiet mariadb; then
        echo -e "MariaDB 狀態: ${GREEN}運行中${NC}"
        
        # 顯示連接數 (需要資料庫權限)
        local db_connections=$(mysql -e "SHOW STATUS LIKE 'Threads_connected';" 2>/dev/null | tail -n 1 | awk '{print $2}')
        if [ -n "$db_connections" ]; then
            echo -e "資料庫連接數: ${YELLOW}${db_connections}${NC}"
        fi
    else
        echo -e "MariaDB 狀態: ${RED}未運行${NC}"
    fi
}

# 顯示日誌摘要
show_log_summary() {
    echo -e "${BLUE}=== 最近日誌摘要 ===${NC}"
    
    # 檢查是否有日誌文件
    if [ -f "logs/server.log" ]; then
        echo "最近 5 條日誌:"
        tail -n 5 logs/server.log | while read line; do
            echo "  $line"
        done
    elif [ -f "server.log" ]; then
        echo "最近 5 條日誌:"
        tail -n 5 server.log | while read line; do
            echo "  $line"
        done
    else
        echo "未找到日誌文件"
    fi
    
    # 檢查錯誤日誌
    if [ -f "logs/error.log" ]; then
        local error_count=$(wc -l < logs/error.log)
        if [ $error_count -gt 0 ]; then
            echo -e "${RED}發現 ${error_count} 條錯誤日誌${NC}"
        fi
    fi
}

# 主函數
main() {
    clear
    echo -e "${GREEN}=========================================="
    echo -e "    Lineage 381a 伺服器監控面板"
    echo -e "    $(date '+%Y-%m-%d %H:%M:%S')"
    echo -e "==========================================${NC}"
    echo
    
    # 檢查伺服器狀態
    if check_server_status; then
        echo
        show_system_resources
        echo
        show_java_resources
        echo
        show_network_connections
        echo
        show_database_connections
        echo
        show_log_summary
    fi
    
    echo
    echo -e "${BLUE}=========================================="
    echo -e "監控完成 - $(date '+%H:%M:%S')"
    echo -e "==========================================${NC}"
}

# 如果提供了參數，執行對應功能
case "$1" in
    "status")
        check_server_status
        ;;
    "resources")
        show_system_resources
        ;;
    "java")
        show_java_resources
        ;;
    "network")
        show_network_connections
        ;;
    "database")
        show_database_connections
        ;;
    "logs")
        show_log_summary
        ;;
    "watch")
        # 持續監控模式
        while true; do
            main
            sleep 5
        done
        ;;
    *)
        main
        ;;
esac
