#!/bin/bash

# Lineage 381a 一鍵設置腳本
# 專用路徑: /home/<USER>/381a
# 硬體: i7-6700K + 64GB RAM

echo "=========================================="
echo "Lineage 381a 一鍵設置工具"
echo "路徑: /home/<USER>/381a"
echo "硬體: i7-6700K + 64GB RAM"
echo "=========================================="

# 切換到項目目錄
cd /home/<USER>/381a

# 檢查是否在正確目錄
if [ ! -f "src/main/java/com/lineage/Server.java" ]; then
    echo "❌ 錯誤: 不在正確的項目目錄中"
    echo "請確保腳本在 /home/<USER>/381a 目錄中執行"
    echo "當前目錄: $(pwd)"
    exit 1
fi

echo "✅ 確認在正確的項目目錄中"

# 1. 更新系統和安裝 Java
echo
echo "=== 1. 安裝和配置 Java 環境 ==="
if ! command -v java &> /dev/null; then
    echo "正在安裝 Java..."
    sudo apt update
    sudo apt install -y openjdk-8-jdk
else
    echo "✅ Java 已安裝"
fi

# 設置 Java 環境變數
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 添加到用戶的 bashrc
if ! grep -q "JAVA_HOME" /home/<USER>/.bashrc; then
    echo 'export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64' >> /home/<USER>/.bashrc
    echo 'export PATH=$JAVA_HOME/bin:$PATH' >> /home/<USER>/.bashrc
    echo "✅ Java 環境變數已添加到 ~/.bashrc"
fi

echo "Java 版本:"
java -version

# 2. 安裝 MariaDB
echo
echo "=== 2. 安裝和配置 MariaDB ==="
if ! command -v mysql &> /dev/null; then
    echo "正在安裝 MariaDB..."
    sudo apt install -y mariadb-server mariadb-client
    
    # 啟動 MariaDB
    sudo systemctl start mariadb
    sudo systemctl enable mariadb
    
    echo "✅ MariaDB 已安裝並啟動"
    echo "⚠️  請稍後手動執行: sudo mysql_secure_installation"
else
    echo "✅ MariaDB 已安裝"
    
    # 確保 MariaDB 運行
    if ! systemctl is-active --quiet mariadb; then
        sudo systemctl start mariadb
        echo "✅ MariaDB 已啟動"
    fi
fi

# 3. 檢查和設置項目文件權限
echo
echo "=== 3. 設置文件權限 ==="
chmod +x /home/<USER>/381a/*.sh
chmod 644 /home/<USER>/381a/config/* 2>/dev/null || true
chmod 644 /home/<USER>/381a/jar/* 2>/dev/null || true

echo "✅ 文件權限已設置"

# 4. 檢查依賴文件
echo
echo "=== 4. 檢查依賴文件 ==="
required_jars=(
    "jar/log4j-1.2.16.jar"
    "jar/commons-logging-1.2.jar"
    "jar/c3p0-0.9.5.5.jar"
    "jar/mchange-commons-java-0.2.19.jar"
    "jar/mariadb-java-client-3.1.4.jar"
    "jar/javolution-5.5.1.jar"
)

missing_count=0
for jar in "${required_jars[@]}"; do
    if [ -f "/home/<USER>/381a/$jar" ]; then
        echo "✅ $(basename $jar)"
    else
        echo "❌ $(basename $jar) 缺失"
        missing_count=$((missing_count + 1))
    fi
done

if [ $missing_count -gt 0 ]; then
    echo "⚠️  缺少 $missing_count 個依賴文件"
    echo "請確保所有 JAR 文件都在 /home/<USER>/381a/jar/ 目錄中"
else
    echo "✅ 所有依賴文件都存在"
fi

# 5. 編譯項目
echo
echo "=== 5. 編譯項目 ==="
echo "清理舊的編譯文件..."
rm -rf /home/<USER>/381a/out/production/Lineage381a
mkdir -p /home/<USER>/381a/out/production/Lineage381a

if [ $missing_count -eq 0 ]; then
    echo "開始編譯..."
    
    # 構建 classpath
    CLASSPATH=""
    for jar in "${required_jars[@]}"; do
        if [ -z "$CLASSPATH" ]; then
            CLASSPATH="/home/<USER>/381a/$jar"
        else
            CLASSPATH="$CLASSPATH:/home/<USER>/381a/$jar"
        fi
    done
    
    # 編譯
    java_files=$(find /home/<USER>/381a/src/main/java -name "*.java")
    javac -cp "$CLASSPATH" -d /home/<USER>/381a/out/production/Lineage381a -encoding UTF-8 $java_files
    
    if [ $? -eq 0 ]; then
        echo "✅ 編譯成功"
        
        # 複製配置文件
        if [ -d "/home/<USER>/381a/config" ]; then
            cp -r /home/<USER>/381a/config /home/<USER>/381a/out/production/Lineage381a/
            echo "✅ 配置文件已複製"
        fi
        
        # 統計編譯結果
        class_count=$(find /home/<USER>/381a/out/production/Lineage381a -name "*.class" | wc -l)
        echo "✅ 總共編譯了 $class_count 個類文件"
    else
        echo "❌ 編譯失敗"
    fi
else
    echo "⚠️  跳過編譯 (缺少依賴文件)"
fi

# 6. 創建日誌目錄
echo
echo "=== 6. 創建日誌目錄 ==="
mkdir -p /home/<USER>/381a/logs
echo "✅ 日誌目錄已創建: /home/<USER>/381a/logs"

# 7. 應用 MariaDB 優化配置
echo
echo "=== 7. 應用 MariaDB 優化配置 ==="
if [ -f "/home/<USER>/381a/mariadb_optimization.cnf" ]; then
    sudo cp /home/<USER>/381a/mariadb_optimization.cnf /etc/mysql/mariadb.conf.d/99-lineage.cnf
    sudo systemctl restart mariadb
    echo "✅ MariaDB 優化配置已應用"
else
    echo "⚠️  MariaDB 優化配置文件不存在"
fi

# 8. 系統優化 (可選)
echo
echo "=== 8. 系統優化 (可選) ==="
read -p "是否執行 64GB RAM 系統優化? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -f "/home/<USER>/381a/ubuntu_optimization_6700k_64gb.sh" ]; then
        echo "執行系統優化..."
        sudo /home/<USER>/381a/ubuntu_optimization_6700k_64gb.sh
        echo "✅ 系統優化完成"
        echo "⚠️  建議重啟系統以應用所有優化"
    else
        echo "⚠️  系統優化腳本不存在"
    fi
else
    echo "跳過系統優化"
fi

echo
echo "=========================================="
echo "設置完成！"
echo "=========================================="
echo "✅ 設置摘要:"
echo "   - Java 環境已配置"
echo "   - MariaDB 已安裝"
echo "   - 項目已編譯 (如果依賴完整)"
echo "   - 文件權限已設置"
echo "   - 日誌目錄已創建"
echo
echo "🚀 下一步:"
echo "1. 配置資料庫:"
echo "   sudo mysql_secure_installation"
echo "   mysql -u root -p"
echo
echo "2. 啟動伺服器:"
echo "   cd /home/<USER>/381a"
echo "   ./start_schung_381a.sh"
echo
echo "3. 監控系統:"
echo "   ./monitor_server.sh"
echo
echo "📁 重要路徑:"
echo "   項目目錄: /home/<USER>/381a"
echo "   日誌目錄: /home/<USER>/381a/logs"
echo "   配置目錄: /home/<USER>/381a/config"
