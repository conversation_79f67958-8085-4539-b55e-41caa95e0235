#!/bin/bash

# Ubuntu 執行問題診斷腳本
# 針對 Lineage 381a 伺服器

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=========================================="
echo -e "Ubuntu 執行問題診斷工具"
echo -e "==========================================${NC}"

# 1. 系統環境檢查
echo -e "\n${BLUE}=== 1. 系統環境檢查 ===${NC}"
echo -e "作業系統: $(uname -a)"
echo -e "Ubuntu 版本: $(lsb_release -d 2>/dev/null || echo '無法檢測')"
echo -e "記憶體: $(free -h | grep Mem | awk '{print $2}')"
echo -e "CPU: $(nproc) 核心"
echo -e "當前用戶: $(whoami)"
echo -e "當前目錄: $(pwd)"

# 2. Java 環境檢查
echo -e "\n${BLUE}=== 2. Java 環境檢查 ===${NC}"
if command -v java &> /dev/null; then
    echo -e "${GREEN}✓ Java 運行環境已安裝${NC}"
    java -version 2>&1 | head -3
else
    echo -e "${RED}✗ Java 運行環境未安裝${NC}"
    echo -e "${YELLOW}解決方案: sudo apt install openjdk-8-jdk${NC}"
fi

if command -v javac &> /dev/null; then
    echo -e "${GREEN}✓ Java 編譯器已安裝${NC}"
    javac -version 2>&1
else
    echo -e "${RED}✗ Java 編譯器未安裝${NC}"
    echo -e "${YELLOW}解決方案: sudo apt install openjdk-8-jdk${NC}"
fi

# 檢查 JAVA_HOME
if [ -n "$JAVA_HOME" ]; then
    echo -e "${GREEN}✓ JAVA_HOME: $JAVA_HOME${NC}"
else
    echo -e "${YELLOW}⚠ JAVA_HOME 未設置${NC}"
    echo -e "${YELLOW}建議設置: export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64${NC}"
fi

# 3. 項目文件檢查
echo -e "\n${BLUE}=== 3. 項目文件檢查 ===${NC}"

# 檢查項目目錄結構
if [ -d "src/main/java" ]; then
    echo -e "${GREEN}✓ 源碼目錄存在${NC}"
    java_files=$(find src/main/java -name "*.java" 2>/dev/null | wc -l)
    echo -e "${GREEN}✓ 找到 $java_files 個 Java 源文件${NC}"
else
    echo -e "${RED}✗ 源碼目錄不存在${NC}"
    echo -e "${YELLOW}請確保在正確的項目目錄中${NC}"
fi

# 檢查主類源文件
if [ -f "src/main/java/com/lineage/Server.java" ]; then
    echo -e "${GREEN}✓ 主類源文件存在${NC}"
    file_encoding=$(file -i src/main/java/com/lineage/Server.java | grep -o 'charset=[^;]*' | cut -d= -f2)
    echo -e "${GREEN}✓ 文件編碼: $file_encoding${NC}"
else
    echo -e "${RED}✗ 主類源文件不存在${NC}"
fi

# 檢查編譯輸出
if [ -f "out/production/Lineage381a/com/lineage/Server.class" ]; then
    echo -e "${GREEN}✓ 主類已編譯${NC}"
    class_date=$(stat -c %y "out/production/Lineage381a/com/lineage/Server.class" 2>/dev/null)
    echo -e "${GREEN}✓ 編譯時間: $class_date${NC}"
else
    echo -e "${RED}✗ 主類未編譯${NC}"
    echo -e "${YELLOW}需要執行編譯${NC}"
fi

# 4. 依賴文件檢查
echo -e "\n${BLUE}=== 4. 依賴文件檢查 ===${NC}"
required_jars=(
    "jar/log4j-1.2.16.jar"
    "jar/commons-logging-1.2.jar"
    "jar/c3p0-0.9.5.5.jar"
    "jar/mchange-commons-java-0.2.19.jar"
    "jar/mariadb-java-client-3.1.4.jar"
    "jar/javolution-5.5.1.jar"
)

missing_count=0
for jar in "${required_jars[@]}"; do
    if [ -f "$jar" ]; then
        size=$(du -h "$jar" | cut -f1)
        echo -e "${GREEN}✓ $(basename $jar) ($size)${NC}"
    else
        echo -e "${RED}✗ $(basename $jar)${NC}"
        missing_count=$((missing_count + 1))
    fi
done

if [ $missing_count -gt 0 ]; then
    echo -e "${RED}缺少 $missing_count 個依賴文件${NC}"
fi

# 5. 配置文件檢查
echo -e "\n${BLUE}=== 5. 配置文件檢查 ===${NC}"
if [ -f "config/sql.properties" ]; then
    echo -e "${GREEN}✓ 資料庫配置文件存在${NC}"
    
    # 檢查配置內容
    if grep -q "mariadb" config/sql.properties; then
        echo -e "${GREEN}✓ MariaDB 驅動配置正確${NC}"
    else
        echo -e "${YELLOW}⚠ 未檢測到 MariaDB 配置${NC}"
    fi
else
    echo -e "${RED}✗ 資料庫配置文件不存在${NC}"
fi

if [ -f "config/c3p0-config.xml" ]; then
    echo -e "${GREEN}✓ 連接池配置文件存在${NC}"
else
    echo -e "${RED}✗ 連接池配置文件不存在${NC}"
fi

# 6. 腳本權限檢查
echo -e "\n${BLUE}=== 6. 腳本權限檢查 ===${NC}"
scripts=("start_server.sh" "compile_ubuntu.sh" "fix_compilation.sh" "monitor_server.sh")

for script in "${scripts[@]}"; do
    if [ -f "$script" ]; then
        if [ -x "$script" ]; then
            echo -e "${GREEN}✓ $script (可執行)${NC}"
        else
            echo -e "${YELLOW}⚠ $script (不可執行)${NC}"
            echo -e "${YELLOW}  解決方案: chmod +x $script${NC}"
        fi
    else
        echo -e "${RED}✗ $script (不存在)${NC}"
    fi
done

# 7. 資料庫檢查
echo -e "\n${BLUE}=== 7. 資料庫檢查 ===${NC}"
if command -v mysql &> /dev/null; then
    echo -e "${GREEN}✓ MySQL/MariaDB 客戶端已安裝${NC}"
    mysql_version=$(mysql --version 2>/dev/null)
    echo -e "${GREEN}✓ 版本: $mysql_version${NC}"
else
    echo -e "${RED}✗ MySQL/MariaDB 客戶端未安裝${NC}"
    echo -e "${YELLOW}解決方案: sudo apt install mariadb-client${NC}"
fi

if systemctl is-active --quiet mariadb 2>/dev/null; then
    echo -e "${GREEN}✓ MariaDB 服務運行中${NC}"
elif systemctl is-active --quiet mysql 2>/dev/null; then
    echo -e "${GREEN}✓ MySQL 服務運行中${NC}"
else
    echo -e "${RED}✗ 資料庫服務未運行${NC}"
    echo -e "${YELLOW}解決方案: sudo systemctl start mariadb${NC}"
fi

# 8. 網路檢查
echo -e "\n${BLUE}=== 8. 網路檢查 ===${NC}"
if netstat -tlnp 2>/dev/null | grep -q ":2000"; then
    echo -e "${YELLOW}⚠ 端口 2000 已被占用${NC}"
    netstat -tlnp 2>/dev/null | grep ":2000"
else
    echo -e "${GREEN}✓ 端口 2000 可用${NC}"
fi

if netstat -tlnp 2>/dev/null | grep -q ":2001"; then
    echo -e "${YELLOW}⚠ 端口 2001 已被占用${NC}"
    netstat -tlnp 2>/dev/null | grep ":2001"
else
    echo -e "${GREEN}✓ 端口 2001 可用${NC}"
fi

# 9. 記憶體檢查
echo -e "\n${BLUE}=== 9. 記憶體檢查 ===${NC}"
total_mem=$(free -g | grep Mem | awk '{print $2}')
available_mem=$(free -g | grep Mem | awk '{print $7}')

echo -e "${GREEN}✓ 總記憶體: ${total_mem}GB${NC}"
echo -e "${GREEN}✓ 可用記憶體: ${available_mem}GB${NC}"

if [ $total_mem -ge 60 ]; then
    echo -e "${GREEN}✓ 記憶體充足，可使用 64GB 配置${NC}"
elif [ $total_mem -ge 30 ]; then
    echo -e "${YELLOW}⚠ 記憶體適中，建議使用 32GB 配置${NC}"
else
    echo -e "${RED}✗ 記憶體不足，需要調整 JVM 參數${NC}"
fi

# 10. 建議解決方案
echo -e "\n${BLUE}=== 10. 建議解決方案 ===${NC}"

if [ $missing_count -gt 0 ]; then
    echo -e "${RED}1. 缺少依賴文件，請確保所有 JAR 文件都在 jar/ 目錄中${NC}"
fi

if [ ! -f "out/production/Lineage381a/com/lineage/Server.class" ]; then
    echo -e "${YELLOW}2. 需要編譯項目:${NC}"
    echo -e "   ${YELLOW}./compile_ubuntu.sh${NC}"
    echo -e "   ${YELLOW}或 ./fix_compilation.sh${NC}"
fi

if ! command -v java &> /dev/null; then
    echo -e "${RED}3. 需要安裝 Java:${NC}"
    echo -e "   ${RED}sudo apt update${NC}"
    echo -e "   ${RED}sudo apt install openjdk-8-jdk${NC}"
fi

echo -e "\n${BLUE}=========================================="
echo -e "診斷完成！"
echo -e "==========================================${NC}"
echo -e "${GREEN}請根據上述檢查結果解決相應問題${NC}"
