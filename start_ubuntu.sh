#!/bin/bash

# Lineage 381a Ubuntu 專用啟動腳本
# 解決"沒有主要的清單屬性"錯誤

echo "=========================================="
echo "Lineage 381a Ubuntu 啟動腳本"
echo "硬體: i7-6700K + 64GB RAM"
echo "=========================================="

# 設置 Java 環境
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 檢查 Java 是否安裝
if ! command -v java &> /dev/null; then
    echo "錯誤: Java 未安裝"
    echo "請執行: sudo apt install openjdk-8-jdk"
    exit 1
fi

echo "Java 版本:"
java -version
echo

# 檢查編譯後的主類是否存在
if [ ! -f "out/production/Lineage381a/com/lineage/Server.class" ]; then
    echo "錯誤: 找不到編譯後的主類 Server.class"
    echo "請先編譯項目:"
    echo "  1. 執行: ./compile_ubuntu.sh"
    echo "  2. 或執行: ./ubuntu_one_click_fix.sh"
    echo "  3. 或手動編譯:"
    echo "     javac -cp \"jar/*\" -d out/production/Lineage381a -encoding UTF-8 src/main/java/com/lineage/Server.java"
    exit 1
fi

# 檢查依賴 JAR 文件
echo "檢查依賴文件..."
required_jars=(
    "jar/log4j-1.2.16.jar"
    "jar/commons-logging-1.2.jar"
    "jar/c3p0-0.9.5.5.jar"
    "jar/mchange-commons-java-0.2.19.jar"
    "jar/mariadb-java-client-3.1.4.jar"
    "jar/javolution-5.5.1.jar"
)

missing_jars=0
for jar in "${required_jars[@]}"; do
    if [ -f "$jar" ]; then
        echo "✓ $(basename $jar)"
    else
        echo "✗ $(basename $jar) 缺失"
        missing_jars=$((missing_jars + 1))
    fi
done

if [ $missing_jars -gt 0 ]; then
    echo "錯誤: 缺少 $missing_jars 個依賴文件"
    echo "請確保所有 JAR 文件都在 jar/ 目錄中"
    exit 1
fi

# 檢查配置文件
if [ ! -f "config/sql.properties" ]; then
    echo "錯誤: 找不到資料庫配置文件 config/sql.properties"
    exit 1
fi

echo "✓ 所有檢查通過"
echo

# 設置 Classpath (Ubuntu 使用冒號分隔)
CLASSPATH="out/production/Lineage381a"
for jar in "${required_jars[@]}"; do
    CLASSPATH="$CLASSPATH:$jar"
done

# 創建日誌目錄
mkdir -p logs

# 設置 JVM 參數 (針對 64GB RAM 優化)
JVM_OPTS="-Xms16g -Xmx32g"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=100"
JVM_OPTS="$JVM_OPTS -XX:G1HeapRegionSize=32m"
JVM_OPTS="$JVM_OPTS -XX:G1NewSizePercent=20"
JVM_OPTS="$JVM_OPTS -XX:G1MaxNewSizePercent=30"
JVM_OPTS="$JVM_OPTS -XX:+UnlockExperimentalVMOptions"
JVM_OPTS="$JVM_OPTS -XX:+UseStringDeduplication"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Taipei"
JVM_OPTS="$JVM_OPTS -Djava.net.preferIPv4Stack=true"

# 顯示啟動信息
echo "JVM 參數: $JVM_OPTS"
echo "Classpath: $CLASSPATH"
echo "主類: com.lineage.Server"
echo
echo "=========================================="
echo "啟動 Lineage 伺服器..."
echo "=========================================="

# 啟動伺服器 (使用編譯後的類文件，不是 JAR)
java $JVM_OPTS -cp "$CLASSPATH" com.lineage.Server

# 檢查退出狀態
exit_code=$?
echo
echo "=========================================="
if [ $exit_code -eq 0 ]; then
    echo "伺服器正常關閉"
else
    echo "伺服器異常退出，退出碼: $exit_code"
    echo "請檢查日誌文件: logs/server.log"
fi
echo "=========================================="
