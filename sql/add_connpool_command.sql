-- 添加連接池管理命令到資料庫
-- 執行此 SQL 腳本來註冊 .connpool 命令

USE `381`;

-- 插入連接池管理命令
INSERT INTO `commands_指令` (`名稱`, `權限`, `代碼`, `註解`, `system`) 
VALUES ('connpool', 200, 'L1ConnectionPoolCommand', '連接池管理命令 - 監控和管理資料庫連接池', 0)
ON DUPLICATE KEY UPDATE 
    `權限` = 200,
    `代碼` = 'L1ConnectionPoolCommand',
    `註解` = '連接池管理命令 - 監控和管理資料庫連接池',
    `system` = 0;

-- 顯示插入結果
SELECT * FROM `commands_指令` WHERE `名稱` = 'connpool';
