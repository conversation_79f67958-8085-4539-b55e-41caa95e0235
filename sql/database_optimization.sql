-- 資料庫優化 SQL 腳本
-- 針對 Lineage 381a 專案的資料庫效能優化

USE `381`;

-- ===================================
-- 1. 索引優化
-- ===================================

-- 角色表索引優化
ALTER TABLE `characters` 
ADD INDEX `idx_account_name` (`account_name`),
ADD INDEX `idx_online_status` (`OnlineStatus`),
ADD INDEX `idx_level` (`level`),
ADD INDEX `idx_location` (`locx`, `locy`, `mapid`);

-- 物品表索引優化
ALTER TABLE `character_items` 
ADD INDEX `idx_char_id_location` (`char_id`, `item_location`),
ADD INDEX `idx_item_id` (`item_id`),
ADD INDEX `idx_enchant_level` (`enchantlvl`);

-- 技能表索引優化
ALTER TABLE `character_skills` 
ADD INDEX `idx_char_skill` (`char_id`, `skill_id`);

-- 帳號表索引優化
ALTER TABLE `accounts` 
ADD INDEX `idx_login_lower` (`login`),
ADD INDEX `idx_lastactive` (`lastactive`),
ADD INDEX `idx_access_level` (`access_level`);

-- 聊天記錄索引優化
ALTER TABLE `character_chat_log` 
ADD INDEX `idx_char_time` (`char_id`, `chat_time`),
ADD INDEX `idx_chat_type` (`chat_type`);

-- GM 命令記錄索引優化
ALTER TABLE `character_gm_指令` 
ADD INDEX `idx_gm_time` (`GM`, `時間`),
ADD INDEX `idx_command` (`指令`);

-- ===================================
-- 2. 表結構優化
-- ===================================

-- 優化角色表的資料類型
ALTER TABLE `characters` 
MODIFY COLUMN `OnlineStatus` TINYINT(1) DEFAULT 0,
MODIFY COLUMN `level` SMALLINT UNSIGNED DEFAULT 1,
MODIFY COLUMN `exp` BIGINT UNSIGNED DEFAULT 0,
MODIFY COLUMN `maxhp` SMALLINT UNSIGNED DEFAULT 1,
MODIFY COLUMN `curhp` SMALLINT UNSIGNED DEFAULT 1;

-- 優化物品表的資料類型
ALTER TABLE `character_items` 
MODIFY COLUMN `item_location` TINYINT UNSIGNED DEFAULT 0,
MODIFY COLUMN `item_count` INT UNSIGNED DEFAULT 1,
MODIFY COLUMN `enchantlvl` TINYINT DEFAULT 0;

-- ===================================
-- 3. 分區優化 (可選)
-- ===================================

-- 聊天記錄按月分區 (範例)
-- ALTER TABLE `character_chat_log` 
-- PARTITION BY RANGE (YEAR(chat_time) * 100 + MONTH(chat_time)) (
--     PARTITION p202401 VALUES LESS THAN (202402),
--     PARTITION p202402 VALUES LESS THAN (202403),
--     PARTITION p202403 VALUES LESS THAN (202404),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- ===================================
-- 4. 查詢優化視圖
-- ===================================

-- 創建角色基本信息視圖
CREATE OR REPLACE VIEW `v_character_basic` AS
SELECT 
    c.objid,
    c.char_name,
    c.account_name,
    c.level,
    c.class,
    c.OnlineStatus,
    c.locx,
    c.locy,
    c.mapid
FROM `characters` c
WHERE c.objid > 0;

-- 創建在線玩家視圖
CREATE OR REPLACE VIEW `v_online_players` AS
SELECT 
    c.objid,
    c.char_name,
    c.account_name,
    c.level,
    c.class,
    c.locx,
    c.locy,
    c.mapid
FROM `characters` c
WHERE c.OnlineStatus = 1;

-- ===================================
-- 5. 存儲過程優化
-- ===================================

DELIMITER //

-- 角色登入存儲過程
CREATE OR REPLACE PROCEDURE `sp_character_login`(
    IN p_char_id INT,
    IN p_login_time TIMESTAMP
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 更新角色在線狀態
    UPDATE `characters` 
    SET `OnlineStatus` = 1, `last_login` = p_login_time
    WHERE `objid` = p_char_id;
    
    -- 記錄登入日誌
    INSERT INTO `character_login_log` (`char_id`, `login_time`)
    VALUES (p_char_id, p_login_time);
    
    COMMIT;
END //

-- 角色登出存儲過程
CREATE OR REPLACE PROCEDURE `sp_character_logout`(
    IN p_char_id INT,
    IN p_logout_time TIMESTAMP
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 更新角色在線狀態
    UPDATE `characters` 
    SET `OnlineStatus` = 0, `last_logout` = p_logout_time
    WHERE `objid` = p_char_id;
    
    -- 更新登入日誌
    UPDATE `character_login_log` 
    SET `logout_time` = p_logout_time
    WHERE `char_id` = p_char_id AND `logout_time` IS NULL
    ORDER BY `login_time` DESC LIMIT 1;
    
    COMMIT;
END //

DELIMITER ;

-- ===================================
-- 6. 定期維護任務
-- ===================================

-- 創建日誌清理事件 (清理30天前的聊天記錄)
CREATE EVENT IF NOT EXISTS `ev_cleanup_chat_log`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    DELETE FROM `character_chat_log` 
    WHERE `chat_time` < DATE_SUB(NOW(), INTERVAL 30 DAY);
END;

-- 創建統計更新事件
CREATE EVENT IF NOT EXISTS `ev_update_statistics`
ON SCHEDULE EVERY 1 HOUR
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    -- 更新在線玩家統計
    INSERT INTO `server_statistics` (`stat_time`, `online_players`, `total_characters`)
    VALUES (
        NOW(),
        (SELECT COUNT(*) FROM `characters` WHERE `OnlineStatus` = 1),
        (SELECT COUNT(*) FROM `characters`)
    );
END;

-- ===================================
-- 7. 效能監控表
-- ===================================

-- 創建效能監控表
CREATE TABLE IF NOT EXISTS `performance_monitor` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `monitor_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `query_type` VARCHAR(50),
    `execution_time` DECIMAL(10,3),
    `affected_rows` INT,
    `query_hash` VARCHAR(32),
    INDEX `idx_monitor_time` (`monitor_time`),
    INDEX `idx_query_type` (`query_type`)
);

-- 創建慢查詢監控視圖
CREATE OR REPLACE VIEW `v_slow_queries` AS
SELECT 
    query_type,
    AVG(execution_time) as avg_time,
    MAX(execution_time) as max_time,
    COUNT(*) as query_count
FROM `performance_monitor`
WHERE monitor_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY query_type
HAVING avg_time > 1.0
ORDER BY avg_time DESC;

-- ===================================
-- 8. 資料庫配置建議
-- ===================================

-- 顯示當前配置
SELECT 
    'innodb_buffer_pool_size' as setting_name,
    @@innodb_buffer_pool_size as current_value,
    '建議設置為可用記憶體的70-80%' as recommendation
UNION ALL
SELECT 
    'query_cache_size',
    @@query_cache_size,
    '建議設置為128M-256M'
UNION ALL
SELECT 
    'max_connections',
    @@max_connections,
    '建議設置為200-500'
UNION ALL
SELECT 
    'innodb_log_file_size',
    @@innodb_log_file_size,
    '建議設置為256M';

-- ===================================
-- 9. 索引使用情況檢查
-- ===================================

-- 檢查未使用的索引
SELECT 
    s.TABLE_SCHEMA,
    s.TABLE_NAME,
    s.INDEX_NAME,
    s.COLUMN_NAME
FROM information_schema.STATISTICS s
LEFT JOIN information_schema.INDEX_STATISTICS i 
    ON s.TABLE_SCHEMA = i.TABLE_SCHEMA 
    AND s.TABLE_NAME = i.TABLE_NAME 
    AND s.INDEX_NAME = i.INDEX_NAME
WHERE s.TABLE_SCHEMA = '381'
    AND i.INDEX_NAME IS NULL
    AND s.INDEX_NAME != 'PRIMARY';

-- 檢查重複索引
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    GROUP_CONCAT(INDEX_NAME) as duplicate_indexes,
    GROUP_CONCAT(COLUMN_NAME) as columns
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = '381'
GROUP BY TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME
HAVING COUNT(*) > 1;

-- ===================================
-- 10. 完成訊息
-- ===================================

SELECT 'Database optimization completed successfully!' as status;
