#!/bin/bash

# 修復 JAR 文件的 MANIFEST.MF
# 解決 Main-Class 未設置或無法讀取的問題

echo "=========================================="
echo "JAR MANIFEST.MF 修復工具"
echo "路徑: /home/<USER>/381a"
echo "=========================================="

cd /home/<USER>/381a

# 尋找 JAR 文件
JAR_FILES=(
    "Lineage381a.jar"
    "Lineage381a_Ubuntu.jar"
    "lineage381a.jar"
    "server.jar"
)

JAR_FILE=""
for jar in "${JAR_FILES[@]}"; do
    if [ -f "$jar" ]; then
        JAR_FILE="$jar"
        break
    fi
done

if [ -z "$JAR_FILE" ]; then
    echo "❌ 找不到 JAR 文件"
    echo "當前目錄中的 JAR 文件:"
    find . -name "*.jar" -type f 2>/dev/null
    exit 1
fi

echo "✅ 找到 JAR 文件: $JAR_FILE"

# 備份原始 JAR 文件
cp "$JAR_FILE" "${JAR_FILE}.backup"
echo "✅ 已備份原始 JAR 文件為: ${JAR_FILE}.backup"

# 創建臨時目錄
TEMP_DIR="temp_jar_fix"
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

echo "解壓 JAR 文件..."
cd "$TEMP_DIR"
jar xf "../$JAR_FILE"

if [ $? -ne 0 ]; then
    echo "❌ 解壓 JAR 文件失敗"
    cd ..
    rm -rf "$TEMP_DIR"
    exit 1
fi

echo "✅ JAR 文件解壓成功"

# 檢查主類是否存在
if [ -f "com/lineage/Server.class" ]; then
    echo "✅ 主類 Server.class 存在"
else
    echo "❌ 主類 Server.class 不存在"
    echo "JAR 文件內容:"
    find . -name "*.class" | head -10
    cd ..
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 創建正確的 MANIFEST.MF
echo "創建正確的 MANIFEST.MF..."
mkdir -p META-INF

cat > META-INF/MANIFEST.MF << 'EOF'
Manifest-Version: 1.0
Main-Class: com.lineage.Server
Class-Path: .

EOF

echo "✅ 新的 MANIFEST.MF 已創建"

# 顯示新的 MANIFEST.MF 內容
echo "新的 MANIFEST.MF 內容:"
cat META-INF/MANIFEST.MF

# 重新打包 JAR 文件
echo "重新打包 JAR 文件..."
jar cfm "../${JAR_FILE}" META-INF/MANIFEST.MF .

if [ $? -eq 0 ]; then
    echo "✅ JAR 文件重新打包成功"
else
    echo "❌ JAR 文件重新打包失敗"
    cd ..
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 返回上級目錄並清理
cd ..
rm -rf "$TEMP_DIR"

# 驗證修復結果
echo
echo "=== 驗證修復結果 ==="
echo "檢查新的 MANIFEST.MF:"
jar xf "$JAR_FILE" META-INF/MANIFEST.MF
cat META-INF/MANIFEST.MF
rm -rf META-INF

echo
echo "檢查主類是否存在:"
if jar tf "$JAR_FILE" | grep -q "com/lineage/Server.class"; then
    echo "✅ 主類存在於 JAR 中"
else
    echo "❌ 主類不存在於 JAR 中"
fi

echo
echo "=========================================="
echo "JAR 修復完成！"
echo "=========================================="
echo "✅ 原始文件備份: ${JAR_FILE}.backup"
echo "✅ 修復後文件: $JAR_FILE"
echo
echo "下一步:"
echo "1. 測試 JAR 文件: ./test_jar.sh"
echo "2. 啟動伺服器: ./run_jar_ubuntu.sh"
