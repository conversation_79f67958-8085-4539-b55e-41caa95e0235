import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.DatabaseMetaData;

/**
 * MariaDB 連接測試程式
 */
public class TestMariaDBConnection {
    
    public static void main(String[] args) {
        System.out.println("=== MariaDB 連接測試 ===");
        
        // 測試配置
        String driver = "org.mariadb.jdbc.Driver";
        String url = "**********************************************************************************************";
        String username = "root";
        String password = "0913z1007Y";
        
        System.out.println("驅動程式: " + driver);
        System.out.println("連接字串: " + url);
        System.out.println("使用者名稱: " + username);
        System.out.println("密碼: " + password);
        System.out.println();
        
        Connection connection = null;
        
        try {
            // 載入驅動程式
            System.out.print("載入 MariaDB 驅動程式... ");
            Class.forName(driver);
            System.out.println("✅ 成功");
            
            // 嘗試連接
            System.out.print("嘗試連接到 MariaDB... ");
            connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ 連接成功！");
            
            // 獲取資料庫資訊
            DatabaseMetaData metaData = connection.getMetaData();
            System.out.println();
            System.out.println("=== 資料庫資訊 ===");
            System.out.println("資料庫產品名稱: " + metaData.getDatabaseProductName());
            System.out.println("資料庫版本: " + metaData.getDatabaseProductVersion());
            System.out.println("驅動程式名稱: " + metaData.getDriverName());
            System.out.println("驅動程式版本: " + metaData.getDriverVersion());
            System.out.println("JDBC 版本: " + metaData.getJDBCMajorVersion() + "." + metaData.getJDBCMinorVersion());
            
            // 測試簡單查詢
            System.out.println();
            System.out.print("測試簡單查詢... ");
            java.sql.Statement statement = connection.createStatement();
            java.sql.ResultSet resultSet = statement.executeQuery("SELECT 1 as test_value");
            if (resultSet.next()) {
                int testValue = resultSet.getInt("test_value");
                if (testValue == 1) {
                    System.out.println("✅ 查詢測試成功");
                } else {
                    System.out.println("❌ 查詢結果不正確");
                }
            }
            resultSet.close();
            statement.close();
            
        } catch (ClassNotFoundException e) {
            System.out.println("❌ 找不到 MariaDB 驅動程式");
            System.out.println("錯誤: " + e.getMessage());
            System.out.println("請確保 mariadb-java-client-3.1.4.jar 在 classpath 中");
            
        } catch (SQLException e) {
            System.out.println("❌ 資料庫連接失敗");
            System.out.println("錯誤代碼: " + e.getErrorCode());
            System.out.println("SQL 狀態: " + e.getSQLState());
            System.out.println("錯誤訊息: " + e.getMessage());
            System.out.println();
            
            // 提供解決建議
            if (e.getMessage().contains("Access denied")) {
                System.out.println("🔧 解決建議:");
                System.out.println("1. 檢查 MariaDB 是否已安裝並啟動");
                System.out.println("2. 檢查使用者名稱和密碼是否正確");
                System.out.println("3. 執行以下 SQL 命令設定使用者:");
                System.out.println("   ALTER USER 'root'@'localhost' IDENTIFIED BY 'root';");
                System.out.println("   FLUSH PRIVILEGES;");
            } else if (e.getMessage().contains("Connection refused")) {
                System.out.println("🔧 解決建議:");
                System.out.println("1. 檢查 MariaDB 服務是否正在運行");
                System.out.println("2. 檢查連接埠 3306 是否開放");
                System.out.println("3. 檢查防火牆設定");
            } else if (e.getMessage().contains("Unknown database")) {
                System.out.println("🔧 解決建議:");
                System.out.println("1. 創建資料庫: CREATE DATABASE 381;");
                System.out.println("2. 檢查資料庫名稱是否正確");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 未知錯誤");
            System.out.println("錯誤: " + e.getMessage());
            e.printStackTrace();
            
        } finally {
            // 關閉連接
            if (connection != null) {
                try {
                    connection.close();
                    System.out.println();
                    System.out.println("✅ 連接已關閉");
                } catch (SQLException e) {
                    System.out.println("❌ 關閉連接時發生錯誤: " + e.getMessage());
                }
            }
        }
        
        System.out.println();
        System.out.println("=== 測試完成 ===");
    }
}
