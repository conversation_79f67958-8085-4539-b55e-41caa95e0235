# Lineage 381a 硬體優化配置指南

## 🖥️ 目標硬體配置

- **CPU**: Intel i7-6700K (4核8線程, 4.0-4.2GHz)
- **記憶體**: 64GB DDR4
- **儲存**: 1TB PCIe SSD
- **網路**: 雙2.5G網卡 + 1G外部線路
- **系統**: Ubuntu 24.02

## 🎯 針對此硬體的最佳化配置

### 📊 效能預期

基於您的硬體配置，優化後的預期效能：

- **同時在線玩家**: 1000-1500 人
- **資料庫查詢響應**: < 50ms
- **記憶體使用率**: < 60%
- **CPU 使用率**: < 70%
- **網路延遲**: < 10ms

### 🔧 JVM 記憶體配置

```bash
# 針對 32GB RAM 的最佳配置
-Xms8g -Xmx16g                    # 堆記憶體 8-16GB
-XX:+UseG1GC                      # G1 垃圾收集器
-XX:MaxGCPauseMillis=100          # GC 暫停時間 < 100ms
-XX:G1HeapRegionSize=32m          # 大記憶體區域
-XX:+UseLargePages                # 大頁面支援
-XX:+AlwaysPreTouch               # 預先分配記憶體
```

### 🗄️ 資料庫配置 (MariaDB)

```ini
# 針對 32GB RAM + PCIe SSD 優化
innodb_buffer_pool_size = 12G     # 40% 系統記憶體
innodb_buffer_pool_instances = 8  # 8個實例
max_connections = 1000            # 支援更多連接
query_cache_size = 512M           # 大查詢快取
innodb_log_file_size = 512M       # 大日誌文件
innodb_io_capacity = 4000         # PCIe SSD 高 IOPS
innodb_io_capacity_max = 8000     # 最大 IOPS
```

### 🔗 連接池配置

```java
// 主資料庫連接池 (針對高效能硬體)
初始連接數: 10
最小連接數: 10
最大連接數: 100
連接增量: 5

// 登入資料庫連接池
初始連接數: 5
最小連接數: 5
最大連接數: 40
連接增量: 3
```

### 🌐 網路優化 (雙2.5G網卡)

```bash
# TCP 緩衝區優化
net.core.rmem_max = 134217728      # 128MB 接收緩衝
net.core.wmem_max = 134217728      # 128MB 發送緩衝
net.ipv4.tcp_rmem = 8192 1048576 134217728
net.ipv4.tcp_wmem = 8192 1048576 134217728

# 高效能網路設置
net.core.netdev_max_backlog = 10000
net.ipv4.tcp_max_syn_backlog = 16384
net.ipv4.tcp_congestion_control = bbr
```

### 💾 儲存優化 (PCIe SSD)

```bash
# I/O 調度器設置
echo none > /sys/block/nvme0n1/queue/scheduler

# 檔案系統優化
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
vm.dirty_expire_centisecs = 500
vm.dirty_writeback_centisecs = 100
```

## 🚀 部署步驟

### 1. 系統優化
```bash
chmod +x ubuntu_optimization_6700k_32gb.sh
sudo ./ubuntu_optimization_6700k_32gb.sh
sudo reboot
```

### 2. 資料庫配置
```bash
sudo cp mariadb_optimization.cnf /etc/mysql/mariadb.conf.d/99-lineage.cnf
sudo systemctl restart mariadb
```

### 3. 啟動伺服器
```bash
./start_server.sh
```

## 📈 效能監控

### 關鍵指標監控

#### CPU 監控 (i7-6700K)
- **正常使用率**: 30-50%
- **高負載警告**: > 70%
- **臨界警告**: > 85%

#### 記憶體監控 (32GB)
- **JVM 堆記憶體**: 8-16GB
- **MariaDB 緩衝**: 12GB
- **系統可用**: > 4GB
- **使用率警告**: > 80%

#### 儲存監控 (PCIe SSD)
- **IOPS**: 50,000+ 讀取, 30,000+ 寫入
- **延遲**: < 1ms
- **使用率**: < 80%

#### 網路監控 (雙2.5G)
- **頻寬使用**: < 1.5Gbps (留餘量)
- **連接數**: < 800 同時連接
- **延遲**: < 10ms

### 監控命令
```bash
# 完整系統監控
./monitor_server.sh

# 持續監控
./monitor_server.sh watch

# 特定資源監控
./monitor_server.sh resources
./monitor_server.sh java
./monitor_server.sh network
```

## 🔧 調優建議

### 根據負載調整

#### 低負載 (< 100 玩家)
```bash
# JVM 設置
-Xms4g -Xmx8g

# 連接池
主資料庫: 5-30 連接
登入資料庫: 3-15 連接
```

#### 中負載 (100-300 玩家)
```bash
# JVM 設置 (當前配置)
-Xms8g -Xmx16g

# 連接池 (當前配置)
主資料庫: 10-100 連接
登入資料庫: 5-40 連接
```

#### 高負載 (300-800 玩家)
```bash
# JVM 設置
-Xms12g -Xmx20g

# 連接池
主資料庫: 15-150 連接
登入資料庫: 8-60 連接

# 資料庫
innodb_buffer_pool_size = 16G
max_connections = 1500
```

## ⚠️ 注意事項

### 硬體限制
- **CPU**: i7-6700K 為4核8線程，高負載時可能成為瓶頸
- **記憶體**: 32GB 充足，但需合理分配給 JVM 和 MariaDB
- **網路**: 1G 外線可能限制最大玩家數

### 建議升級路徑
如需支援更多玩家，建議升級順序：
1. **網路**: 升級到 10G 外線
2. **CPU**: 升級到更多核心的處理器
3. **記憶體**: 升級到 64GB (如需要)

## 🎯 效能基準測試

### 壓力測試建議
```bash
# 1. 資料庫壓力測試
sysbench --test=oltp --mysql-user=root --mysql-password=xxx \
         --mysql-db=381 --oltp-table-size=1000000 \
         --max-requests=100000 --num-threads=100 run

# 2. 網路壓力測試
iperf3 -c server_ip -t 60 -P 10

# 3. 記憶體壓力測試
stress-ng --vm 4 --vm-bytes 8G --timeout 300s
```

### 預期測試結果
- **資料庫 TPS**: > 5000
- **網路頻寬**: 接近 1Gbps
- **記憶體延遲**: < 100ns
- **SSD IOPS**: > 50,000

## 📞 技術支援

### 效能問題診斷
1. 檢查 CPU 使用率和溫度
2. 監控記憶體使用情況
3. 檢查網路頻寬使用
4. 分析資料庫慢查詢

### 優化建議
根據實際使用情況，可進一步調整：
- JVM 參數微調
- 連接池大小調整
- 資料庫參數優化
- 網路參數調整

---

**此配置專為您的硬體環境優化，預期可支援 500-800 同時在線玩家的穩定運行。**
