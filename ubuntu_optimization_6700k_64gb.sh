#!/bin/bash

# Ubuntu 24.02 系統優化腳本
# 針對 i7-6700K + 64GB RAM + PCIe SSD + 雙2.5G網卡
# 超高效能配置版本

echo "=========================================="
echo "Ubuntu 24.02 Lineage 伺服器系統優化"
echo "硬體配置: i7-6700K + 64GB RAM + PCIe SSD"
echo "超高效能版本 - 支援 1000+ 玩家"
echo "=========================================="

# 檢查是否為 root 用戶
if [ "$EUID" -ne 0 ]; then
    echo "請使用 sudo 執行此腳本"
    exit 1
fi

# 1. 針對超大記憶體的網路參數優化
echo "1. 優化網路參數 (針對 64GB RAM + 雙2.5G網卡)..."
cat >> /etc/sysctl.conf << EOF

# Lineage 伺服器網路優化 - 超高效能版本 (64GB RAM)
# 針對雙2.5G網卡和1G外線，支援 1000+ 玩家
net.core.rmem_max = 268435456
net.core.wmem_max = 268435456
net.core.rmem_default = 2097152
net.core.wmem_default = 2097152
net.ipv4.tcp_rmem = 16384 2097152 268435456
net.ipv4.tcp_wmem = 16384 2097152 268435456
net.ipv4.tcp_congestion_control = bbr
net.core.netdev_max_backlog = 20000
net.ipv4.tcp_window_scaling = 1
net.ipv4.tcp_timestamps = 1
net.ipv4.tcp_sack = 1
net.ipv4.tcp_no_metrics_save = 1
net.ipv4.tcp_moderate_rcvbuf = 1
net.ipv4.tcp_fin_timeout = 10
net.ipv4.tcp_keepalive_time = 300
net.ipv4.tcp_max_syn_backlog = 32768
net.ipv4.tcp_max_tw_buckets = 20000
net.ipv4.tcp_fastopen = 3
net.ipv4.tcp_mem = 204800 409600 819200
net.ipv4.tcp_max_orphans = 13107200
net.ipv4.ip_local_port_range = 1024 65535

# 針對 64GB 記憶體系統的優化
vm.swappiness = 1
vm.dirty_ratio = 10
vm.dirty_background_ratio = 3
vm.vfs_cache_pressure = 25
vm.min_free_kbytes = 2097152

# 針對 PCIe SSD 的 I/O 優化 (64GB RAM 版本)
vm.dirty_expire_centisecs = 300
vm.dirty_writeback_centisecs = 50
vm.page-cluster = 3
EOF

# 2. 針對超大記憶體的文件描述符優化
echo "2. 優化文件描述符限制 (64GB RAM 版本)..."
cat >> /etc/security/limits.conf << EOF

# Lineage 伺服器文件描述符優化 - 超高效能版本 (64GB RAM)
* soft nofile 2097152
* hard nofile 2097152
* soft nproc 131072
* hard nproc 131072
* soft memlock unlimited
* hard memlock unlimited
* soft stack unlimited
* hard stack unlimited
EOF

# 3. 針對 i7-6700K + 64GB RAM 的 CPU 和記憶體優化
echo "3. CPU 和記憶體效能優化 (64GB RAM)..."
cat >> /etc/sysctl.conf << EOF

# CPU 效能優化 (針對高記憶體負載)
kernel.sched_migration_cost_ns = 5000000
kernel.sched_autogroup_enabled = 0
kernel.sched_wakeup_granularity_ns = 10000000
kernel.sched_min_granularity_ns = 5000000
kernel.sched_latency_ns = 60000000

# 記憶體管理優化 (64GB RAM)
vm.zone_reclaim_mode = 0
vm.numa_balancing = 1
kernel.numa_balancing = 1
EOF

# 4. 大頁面記憶體支援 (針對 64GB RAM)
echo "4. 啟用大頁面記憶體支援 (64GB RAM 版本)..."
# 為 64GB 系統分配更多大頁面
echo 'vm.nr_hugepages = 16384' >> /etc/sysctl.conf

# 設置大頁面
echo never > /sys/kernel/mm/transparent_hugepage/enabled
echo never > /sys/kernel/mm/transparent_hugepage/defrag

# 5. 針對 PCIe SSD 的進階 I/O 調度器優化
echo "5. 進階 I/O 調度器優化 (PCIe SSD + 64GB RAM)..."
# 檢測 NVMe 設備並設置調度器
for device in /sys/block/nvme*; do
    if [ -d "$device" ]; then
        device_name=$(basename "$device")
        echo "設置 $device_name 使用 none 調度器"
        echo none > /sys/block/$device_name/queue/scheduler
        echo 1 > /sys/block/$device_name/queue/nomerges
        echo 2048 > /sys/block/$device_name/queue/nr_requests
        echo 0 > /sys/block/$device_name/queue/add_random
        echo 1 > /sys/block/$device_name/queue/rq_affinity
    fi
done

# 6. 網卡進階優化 (雙2.5G網卡 + 64GB RAM)
echo "6. 網卡進階效能優化..."
# 檢測並優化網卡設置
for interface in $(ip link show | grep -E "^[0-9]+:" | grep -v lo | cut -d: -f2 | tr -d ' '); do
    echo "優化網卡: $interface"
    ethtool -G $interface rx 8192 tx 8192 2>/dev/null || true
    ethtool -K $interface gro on gso on tso on lro on 2>/dev/null || true
    ethtool -C $interface rx-usecs 25 tx-usecs 25 2>/dev/null || true
    ethtool -A $interface rx on tx on 2>/dev/null || true
done

# 7. Java 環境進階優化 (64GB RAM)
echo "7. Java 環境進階優化 (64GB RAM)..."
if ! command -v java &> /dev/null; then
    echo "安裝 OpenJDK 8..."
    apt update
    apt install -y openjdk-8-jdk
else
    echo "Java 已安裝: $(java -version 2>&1 | head -n 1)"
fi

# 設置 Java 環境變數 (64GB RAM 版本)
cat >> /etc/environment << EOF
JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
JVM_OPTS="-Xms16g -Xmx32g -XX:+UseG1GC -XX:+UseLargePages -XX:+AlwaysPreTouch"
EOF

# 8. 針對超高效能的 MariaDB 調優 (64GB RAM)
echo "8. MariaDB 超高效能配置 (64GB RAM)..."
if command -v mysql &> /dev/null; then
    echo "MariaDB 優化建議 (64GB RAM):"
    echo "  - innodb_buffer_pool_size = 24G"
    echo "  - max_connections = 2000"
    echo "  - query_cache_size = 1G"
    echo "  - innodb_log_file_size = 1G"
    echo "  - innodb_buffer_pool_instances = 16"
    echo "  - 支援 1000+ 同時在線玩家"
fi

# 9. 防火牆配置 (高負載版本)
echo "9. 配置防火牆 (高負載版本)..."
if command -v ufw &> /dev/null; then
    echo "配置 UFW 防火牆..."
    ufw allow 2000/tcp comment "Lineage Game Port"
    ufw allow 2001/tcp comment "Lineage Login Port"
    ufw allow 22/tcp comment "SSH"
    echo "防火牆規則已添加"
fi

# 10. 超高效能監控工具
echo "10. 安裝超高效能監控工具..."
apt install -y htop iotop nethogs sysstat numactl hwloc perf-tools-unstable

# 11. 針對 64GB RAM 的日誌輪轉
echo "11. 配置日誌輪轉 (64GB RAM 版本)..."
cat > /etc/logrotate.d/lineage << EOF
/path/to/your/lineage/server/logs/*.log {
    daily
    missingok
    rotate 90
    compress
    delaycompress
    notifempty
    create 644 lineage lineage
    size 200M
    postrotate
        /bin/kill -USR1 \$(cat /path/to/your/lineage/server/lineage.pid 2>/dev/null) 2>/dev/null || true
    endscript
}
EOF

# 12. 建立開機自動優化腳本 (64GB RAM 版本)
echo "12. 建立開機自動優化 (64GB RAM 版本)..."
cat > /etc/systemd/system/lineage-optimization.service << EOF
[Unit]
Description=Lineage Server Hardware Optimization (64GB RAM)
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'echo never > /sys/kernel/mm/transparent_hugepage/enabled'
ExecStart=/bin/bash -c 'echo never > /sys/kernel/mm/transparent_hugepage/defrag'
ExecStart=/bin/bash -c 'for dev in /sys/block/nvme*; do [ -d "\$dev" ] && echo none > \$dev/queue/scheduler; done'
ExecStart=/bin/bash -c 'echo 16384 > /proc/sys/vm/nr_hugepages'
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

systemctl enable lineage-optimization.service

# 13. CPU 親和性優化 (針對 i7-6700K)
echo "13. CPU 親和性優化..."
cat >> /etc/sysctl.conf << EOF

# CPU 親和性優化 (i7-6700K 4核8線程)
kernel.sched_rt_runtime_us = 950000
kernel.sched_rt_period_us = 1000000
EOF

# 14. 應用系統參數
echo "14. 應用系統參數..."
sysctl -p

echo "=========================================="
echo "超高效能系統優化完成！"
echo "=========================================="
echo "硬體配置優化摘要 (64GB RAM 版本):"
echo "- JVM 記憶體: 16GB-32GB (針對64GB RAM)"
echo "- MariaDB 緩衝: 24GB"
echo "- 連接池: 主資料庫200連接, 登入80連接"
echo "- 網路: 針對雙2.5G網卡優化"
echo "- 儲存: PCIe SSD 超高效能優化"
echo "- 大頁面記憶體: 32GB"
echo ""
echo "建議重啟系統以確保所有優化生效"
echo ""
echo "預期超高效能提升:"
echo "- 支援 1000-1500 同時在線玩家"
echo "- 查詢響應時間 < 30ms"
echo "- 記憶體使用率 < 70%"
echo "- 網路延遲 < 5ms"
echo "- 資料庫 TPS > 10000"
echo ""
echo "這是一個企業級的高效能配置！"
