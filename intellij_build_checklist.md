# ✅ IntelliJ JAR 構建檢查清單

## 🏗️ 構建前檢查

### 1. Project Structure 檢查
- [ ] **Project SDK**: 設置為 Java 8
- [ ] **Project language level**: 設置為 8 或更低
- [ ] **Project compiler output**: 設置正確的輸出路徑

### 2. Modules 檢查
- [ ] **Sources**: `src/main/java` 標記為 Sources
- [ ] **Resources**: `config` 標記為 Resources (如果需要)
- [ ] **Dependencies**: 所有 JAR 依賴都已添加

### 3. Libraries 檢查
確保以下 JAR 文件都在 Libraries 中：
- [ ] `log4j-1.2.16.jar`
- [ ] `commons-logging-1.2.jar`
- [ ] `c3p0-0.9.5.5.jar`
- [ ] `mchange-commons-java-0.2.19.jar`
- [ ] `mariadb-java-client-3.1.4.jar`
- [ ] `javolution-5.5.1.jar`

## 🎯 Artifact 配置檢查

### 1. 基本設置
- [ ] **Type**: JAR
- [ ] **Name**: `Lineage381a_Ubuntu`
- [ ] **Output directory**: `out/artifacts/Lineage381a_Ubuntu_jar/`

### 2. Main Class 設置
- [ ] **Main Class**: `com.lineage.Server`
- [ ] **JAR files from libraries**: 選擇 "extract to the target JAR"
- [ ] **Build on make**: 勾選

### 3. 包含內容檢查
確保 JAR 包含以下內容：
- [ ] **編譯後的類文件**: `com/lineage/Server.class`
- [ ] **所有依賴庫**: 解壓到 JAR 中
- [ ] **配置文件**: `config/` 目錄 (如果需要)
- [ ] **MANIFEST.MF**: 包含正確的 Main-Class

## 🔧 構建步驟

### 1. 清理項目
```
Build → Clean
```

### 2. 重新構建
```
Build → Rebuild Project
```

### 3. 構建 Artifact
```
Build → Build Artifacts... → Lineage381a_Ubuntu → Build
```

### 4. 驗證構建結果
檢查輸出目錄：
```
out/artifacts/Lineage381a_Ubuntu_jar/Lineage381a_Ubuntu.jar
```

## 🧪 構建後測試

### 1. 在 Windows 上測試
```bash
cd out/artifacts/Lineage381a_Ubuntu_jar/
java -jar Lineage381a_Ubuntu.jar
```

### 2. 檢查 JAR 內容
```bash
jar tf Lineage381a_Ubuntu.jar | head -20
```

### 3. 檢查 MANIFEST.MF
```bash
jar xf Lineage381a_Ubuntu.jar META-INF/MANIFEST.MF
cat META-INF/MANIFEST.MF
```

## 📦 部署到 Ubuntu

### 1. 傳輸 JAR 文件
```bash
scp out/artifacts/Lineage381a_Ubuntu_jar/Lineage381a_Ubuntu.jar schung@your-ubuntu-ip:/home/<USER>/381a/
```

### 2. 傳輸啟動腳本
```bash
scp run_jar_ubuntu.sh schung@your-ubuntu-ip:/home/<USER>/381a/
```

### 3. 在 Ubuntu 上運行
```bash
cd /home/<USER>/381a
chmod +x run_jar_ubuntu.sh
./run_jar_ubuntu.sh
```

## 🔍 常見問題解決

### 問題 1: "找不到主類"
**檢查項目**:
- [ ] Main-Class 在 MANIFEST.MF 中正確設置
- [ ] Server.class 存在於 JAR 中
- [ ] 包路徑正確 (`com.lineage.Server`)

**解決方案**:
```
重新配置 Artifact，確保 Main Class 設置正確
```

### 問題 2: "ClassNotFoundException"
**檢查項目**:
- [ ] 所有依賴 JAR 都包含在構建中
- [ ] 選擇了 "extract to the target JAR"

**解決方案**:
```
重新添加 Libraries，確保所有依賴都被包含
```

### 問題 3: "配置文件找不到"
**檢查項目**:
- [ ] config 目錄包含在 JAR 中
- [ ] 配置文件路徑正確

**解決方案**:
```
在 Artifact 配置中手動添加 config 目錄
```

### 問題 4: JAR 文件過大
**檢查項目**:
- [ ] 是否包含了不必要的依賴
- [ ] 是否重複包含了某些庫

**解決方案**:
```
檢查 Libraries 配置，移除重複或不必要的依賴
```

## 📊 預期結果

### JAR 文件大小
- **正常大小**: 50-100 MB
- **包含所有依賴**: 可能達到 200 MB+

### 啟動時間
- **首次啟動**: 2-5 分鐘
- **後續啟動**: 1-3 分鐘

### 記憶體使用
- **JVM 堆記憶體**: 16-32 GB
- **總記憶體使用**: 可能達到 35-40 GB

## 🎯 最佳實踐

### 1. 版本管理
為不同環境創建不同的 Artifact：
- `Lineage381a_Development`
- `Lineage381a_Ubuntu`
- `Lineage381a_Production`

### 2. 自動化構建
設置 Build Configuration：
```
Run → Edit Configurations → Add New → Build Artifacts
```

### 3. 構建腳本
創建批處理文件自動化構建過程：
```batch
@echo off
echo Building Lineage 381a JAR...
call gradlew clean build
echo Build completed!
```

---

**重要提醒**: 構建 JAR 是解決 Ubuntu 部署問題的最佳方案，避免了源碼傳輸和編譯問題。
