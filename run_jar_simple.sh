#!/bin/bash

# 簡化的 JAR 啟動腳本
# 使用較低的記憶體設置進行測試

echo "=========================================="
echo "Lineage 381a 簡化啟動 (測試模式)"
echo "路徑: /home/<USER>/381a"
echo "=========================================="

cd /home/<USER>/381a

# 設置 Java 環境
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 檢查 Java
if ! command -v java &> /dev/null; then
    echo "❌ Java 未安裝"
    echo "請執行: sudo apt install openjdk-8-jdk"
    exit 1
fi

echo "✅ Java 版本:"
java -version
echo

# 尋找 JAR 文件
JAR_FILES=(
    "Lineage381a.jar"
    "Lineage381a_Ubuntu.jar"
    "lineage381a.jar"
    "server.jar"
)

JAR_FILE=""
for jar in "${JAR_FILES[@]}"; do
    if [ -f "$jar" ]; then
        JAR_FILE="$jar"
        break
    fi
done

if [ -z "$JAR_FILE" ]; then
    echo "❌ 找不到 JAR 文件"
    echo "請確保 JAR 文件在當前目錄中"
    exit 1
fi

echo "✅ 找到 JAR 文件: $JAR_FILE"

# 檢查可用記憶體
TOTAL_MEM=$(free -g | grep Mem | awk '{print $2}')
AVAILABLE_MEM=$(free -g | grep Mem | awk '{print $7}')

echo "總記憶體: ${TOTAL_MEM}GB"
echo "可用記憶體: ${AVAILABLE_MEM}GB"

# 根據可用記憶體調整 JVM 參數
if [ $AVAILABLE_MEM -ge 35 ]; then
    # 64GB RAM 完整配置
    JVM_OPTS="-Xms16g -Xmx32g"
    echo "使用完整配置 (16-32GB)"
elif [ $AVAILABLE_MEM -ge 18 ]; then
    # 32GB RAM 配置
    JVM_OPTS="-Xms8g -Xmx16g"
    echo "使用中等配置 (8-16GB)"
elif [ $AVAILABLE_MEM -ge 8 ]; then
    # 16GB RAM 配置
    JVM_OPTS="-Xms4g -Xmx8g"
    echo "使用基本配置 (4-8GB)"
else
    # 最小配置
    JVM_OPTS="-Xms2g -Xmx4g"
    echo "使用最小配置 (2-4GB)"
fi

# 通用 JVM 優化參數
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Taipei"
JVM_OPTS="$JVM_OPTS -Djava.net.preferIPv4Stack=true"

# 創建日誌目錄
mkdir -p logs

echo "JVM 參數: $JVM_OPTS"
echo

# 檢查 MANIFEST.MF
echo "檢查 JAR 文件..."
jar xf "$JAR_FILE" META-INF/MANIFEST.MF 2>/dev/null
if [ -f "META-INF/MANIFEST.MF" ]; then
    if grep -q "Main-Class" META-INF/MANIFEST.MF; then
        MAIN_CLASS=$(grep "Main-Class" META-INF/MANIFEST.MF | cut -d: -f2 | tr -d ' \r\n')
        echo "✅ Main-Class: $MAIN_CLASS"
    else
        echo "❌ Main-Class 未設置"
        echo "請執行: ./fix_jar_manifest.sh"
        rm -rf META-INF
        exit 1
    fi
    rm -rf META-INF
else
    echo "❌ MANIFEST.MF 不存在"
    echo "請執行: ./fix_jar_manifest.sh"
    exit 1
fi

echo
echo "=========================================="
echo "啟動 Lineage 伺服器..."
echo "=========================================="
echo "JAR 文件: $JAR_FILE"
echo "記憶體配置: $JVM_OPTS"
echo "工作目錄: $(pwd)"
echo "=========================================="

# 啟動 JAR 文件
java $JVM_OPTS -jar "$JAR_FILE"

# 檢查退出狀態
exit_code=$?
echo
echo "=========================================="
if [ $exit_code -eq 0 ]; then
    echo "✅ 伺服器正常關閉"
else
    echo "❌ 伺服器異常退出，退出碼: $exit_code"
    echo
    echo "常見問題解決:"
    echo "1. 如果是 Main-Class 問題: ./fix_jar_manifest.sh"
    echo "2. 如果是記憶體問題: 調整 JVM 參數"
    echo "3. 如果是配置問題: 檢查資料庫連接"
    echo "4. 如果是依賴問題: 重新構建 JAR"
fi
echo "=========================================="
