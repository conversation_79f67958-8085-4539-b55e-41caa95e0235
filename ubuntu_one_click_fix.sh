#!/bin/bash

# Ubuntu 一鍵修復腳本
# 解決 Lineage 381a 在 Ubuntu 24.02 上的執行問題

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=========================================="
echo -e "Lineage 381a Ubuntu 一鍵修復工具"
echo -e "硬體: i7-6700K + 64GB RAM"
echo -e "==========================================${NC}"

# 檢查是否為 root 用戶
if [ "$EUID" -eq 0 ]; then
    echo -e "${RED}請不要使用 root 用戶執行此腳本${NC}"
    echo -e "${YELLOW}請使用普通用戶執行，需要時會自動使用 sudo${NC}"
    exit 1
fi

# 1. 更新系統和安裝基本軟體
echo -e "\n${BLUE}=== 1. 更新系統和安裝基本軟體 ===${NC}"
echo -e "${YELLOW}正在更新系統...${NC}"
sudo apt update

echo -e "${YELLOW}正在安裝必要軟體...${NC}"
sudo apt install -y openjdk-8-jdk mariadb-server mariadb-client build-essential wget curl git

# 2. 配置 Java 環境
echo -e "\n${BLUE}=== 2. 配置 Java 環境 ===${NC}"
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 添加到 bashrc
if ! grep -q "JAVA_HOME" ~/.bashrc; then
    echo 'export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64' >> ~/.bashrc
    echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
    echo -e "${GREEN}✓ Java 環境變數已添加到 ~/.bashrc${NC}"
fi

echo -e "${GREEN}✓ Java 版本:${NC}"
java -version

# 3. 檢查項目文件
echo -e "\n${BLUE}=== 3. 檢查項目文件 ===${NC}"
if [ ! -d "src/main/java" ]; then
    echo -e "${RED}✗ 錯誤: 找不到源碼目錄${NC}"
    echo -e "${YELLOW}請確保在正確的項目目錄中執行此腳本${NC}"
    exit 1
fi

if [ ! -f "src/main/java/com/lineage/Server.java" ]; then
    echo -e "${RED}✗ 錯誤: 找不到主類源文件${NC}"
    exit 1
fi

echo -e "${GREEN}✓ 項目文件檢查通過${NC}"

# 4. 檢查和修復依賴文件
echo -e "\n${BLUE}=== 4. 檢查依賴文件 ===${NC}"
required_jars=(
    "jar/log4j-1.2.16.jar"
    "jar/commons-logging-1.2.jar"
    "jar/c3p0-0.9.5.5.jar"
    "jar/mchange-commons-java-0.2.19.jar"
    "jar/mariadb-java-client-3.1.4.jar"
    "jar/javolution-5.5.1.jar"
)

missing_jars=()
for jar in "${required_jars[@]}"; do
    if [ -f "$jar" ]; then
        echo -e "${GREEN}✓ $(basename $jar)${NC}"
    else
        echo -e "${RED}✗ $(basename $jar)${NC}"
        missing_jars+=("$jar")
    fi
done

if [ ${#missing_jars[@]} -gt 0 ]; then
    echo -e "${RED}錯誤: 缺少以下依賴文件:${NC}"
    for jar in "${missing_jars[@]}"; do
        echo -e "${RED}  - $jar${NC}"
    done
    echo -e "${YELLOW}請確保所有 JAR 文件都在 jar/ 目錄中${NC}"
    exit 1
fi

# 5. 清理和重新編譯
echo -e "\n${BLUE}=== 5. 清理和重新編譯 ===${NC}"
echo -e "${YELLOW}清理舊的編譯文件...${NC}"
rm -rf out/production/Lineage381a
mkdir -p out/production/Lineage381a

echo -e "${YELLOW}檢查源碼編碼...${NC}"
# 檢查並轉換編碼
find src/main/java -name "*.java" -exec file -i {} \; | grep -v utf-8 | grep -v us-ascii | while read line; do
    file_path=$(echo $line | cut -d: -f1)
    echo "轉換文件編碼: $file_path"
    iconv -f GBK -t UTF-8 "$file_path" > "${file_path}.tmp" && mv "${file_path}.tmp" "$file_path"
done

echo -e "${YELLOW}開始編譯...${NC}"
# 構建 classpath
CLASSPATH=""
for jar in "${required_jars[@]}"; do
    if [ -z "$CLASSPATH" ]; then
        CLASSPATH="$jar"
    else
        CLASSPATH="$CLASSPATH:$jar"
    fi
done

# 編譯
java_files=$(find src/main/java -name "*.java")
javac -cp "$CLASSPATH" -d out/production/Lineage381a -encoding UTF-8 $java_files

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 編譯成功${NC}"
    class_count=$(find out/production/Lineage381a -name "*.class" | wc -l)
    echo -e "${GREEN}✓ 編譯了 $class_count 個類文件${NC}"
else
    echo -e "${RED}✗ 編譯失敗${NC}"
    exit 1
fi

# 6. 複製配置文件
echo -e "\n${BLUE}=== 6. 複製配置文件 ===${NC}"
if [ -d "config" ]; then
    cp -r config out/production/Lineage381a/
    echo -e "${GREEN}✓ 配置文件已複製${NC}"
fi

# 7. 設置腳本權限
echo -e "\n${BLUE}=== 7. 設置腳本權限 ===${NC}"
chmod +x *.sh
echo -e "${GREEN}✓ 腳本權限已設置${NC}"

# 8. 配置 MariaDB
echo -e "\n${BLUE}=== 8. 配置 MariaDB ===${NC}"
if systemctl is-active --quiet mariadb; then
    echo -e "${GREEN}✓ MariaDB 已運行${NC}"
else
    echo -e "${YELLOW}啟動 MariaDB...${NC}"
    sudo systemctl start mariadb
    sudo systemctl enable mariadb
fi

# 應用 MariaDB 優化配置
if [ -f "mariadb_optimization.cnf" ]; then
    echo -e "${YELLOW}應用 MariaDB 優化配置...${NC}"
    sudo cp mariadb_optimization.cnf /etc/mysql/mariadb.conf.d/99-lineage.cnf
    sudo systemctl restart mariadb
    echo -e "${GREEN}✓ MariaDB 優化配置已應用${NC}"
fi

# 9. 創建日誌目錄
echo -e "\n${BLUE}=== 9. 創建日誌目錄 ===${NC}"
mkdir -p logs
echo -e "${GREEN}✓ 日誌目錄已創建${NC}"

# 10. 測試啟動
echo -e "\n${BLUE}=== 10. 測試編譯結果 ===${NC}"
if [ -f "out/production/Lineage381a/com/lineage/Server.class" ]; then
    echo -e "${GREEN}✓ 主類編譯成功${NC}"
    
    # 測試 classpath
    echo -e "${YELLOW}測試 Java classpath...${NC}"
    java -cp "$CLASSPATH:out/production/Lineage381a" com.lineage.Server --version 2>/dev/null
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Java classpath 測試通過${NC}"
    else
        echo -e "${YELLOW}⚠ Java classpath 測試未通過，但這可能是正常的${NC}"
    fi
else
    echo -e "${RED}✗ 主類編譯失敗${NC}"
    exit 1
fi

# 11. 系統優化 (可選)
echo -e "\n${BLUE}=== 11. 系統優化 (可選) ===${NC}"
read -p "是否執行 64GB RAM 系統優化? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -f "ubuntu_optimization_6700k_64gb.sh" ]; then
        echo -e "${YELLOW}執行系統優化...${NC}"
        sudo ./ubuntu_optimization_6700k_64gb.sh
        echo -e "${GREEN}✓ 系統優化完成，建議重啟系統${NC}"
    else
        echo -e "${YELLOW}⚠ 系統優化腳本不存在${NC}"
    fi
fi

echo -e "\n${BLUE}=========================================="
echo -e "一鍵修復完成！"
echo -e "==========================================${NC}"
echo -e "${GREEN}修復摘要:${NC}"
echo -e "✓ Java 環境已配置"
echo -e "✓ 項目已重新編譯"
echo -e "✓ MariaDB 已配置"
echo -e "✓ 腳本權限已設置"
echo -e ""
echo -e "${GREEN}下一步:${NC}"
echo -e "1. 配置資料庫: ${YELLOW}mysql -u root -p${NC}"
echo -e "2. 啟動伺服器: ${YELLOW}./start_server.sh${NC}"
echo -e "3. 監控系統: ${YELLOW}./monitor_server.sh${NC}"
echo -e ""
echo -e "${YELLOW}如果仍有問題，請執行診斷: ./diagnose_ubuntu.sh${NC}"
