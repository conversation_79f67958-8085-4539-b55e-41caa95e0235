#!/bin/bash

# Lineage 381a Ubuntu 編譯腳本
# 針對 i7-6700K + 64GB RAM + Ubuntu 24.02

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=========================================="
echo -e "Lineage 381a Ubuntu 編譯腳本"
echo -e "硬體: i7-6700K + 64GB RAM"
echo -e "==========================================${NC}"

# 檢查 Java 環境
echo -e "\n${BLUE}=== 1. 檢查 Java 環境 ===${NC}"
if command -v javac &> /dev/null; then
    java_version=$(javac -version 2>&1)
    echo -e "${GREEN}✓ Java 編譯器已安裝: $java_version${NC}"
else
    echo -e "${RED}✗ Java 編譯器未安裝${NC}"
    echo -e "${YELLOW}正在安裝 OpenJDK 8...${NC}"
    sudo apt update
    sudo apt install -y openjdk-8-jdk
fi

# 設置 Java 環境
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 檢查源碼目錄
echo -e "\n${BLUE}=== 2. 檢查源碼目錄 ===${NC}"
if [ -d "src/main/java" ]; then
    echo -e "${GREEN}✓ 源碼目錄存在${NC}"
    source_count=$(find src/main/java -name "*.java" | wc -l)
    echo -e "${GREEN}✓ 找到 $source_count 個 Java 源文件${NC}"
else
    echo -e "${RED}✗ 源碼目錄不存在${NC}"
    exit 1
fi

# 檢查依賴 JAR 文件
echo -e "\n${BLUE}=== 3. 檢查依賴 JAR 文件 ===${NC}"
jar_files=(
    "jar/log4j-1.2.16.jar"
    "jar/commons-logging-1.2.jar"
    "jar/c3p0-0.9.5.5.jar"
    "jar/mchange-commons-java-0.2.19.jar"
    "jar/mariadb-java-client-3.1.4.jar"
    "jar/javolution-5.5.1.jar"
)

missing_jars=0
for jar_file in "${jar_files[@]}"; do
    if [ -f "$jar_file" ]; then
        echo -e "${GREEN}✓ $(basename $jar_file)${NC}"
    else
        echo -e "${RED}✗ $(basename $jar_file) 缺失${NC}"
        missing_jars=$((missing_jars + 1))
    fi
done

if [ $missing_jars -gt 0 ]; then
    echo -e "${RED}錯誤: 缺少 $missing_jars 個依賴文件${NC}"
    exit 1
fi

# 設置 Classpath
echo -e "\n${BLUE}=== 4. 設置編譯 Classpath ===${NC}"
CLASSPATH=""
for jar_file in "${jar_files[@]}"; do
    if [ -z "$CLASSPATH" ]; then
        CLASSPATH="$jar_file"
    else
        CLASSPATH="$CLASSPATH:$jar_file"
    fi
done

echo -e "${GREEN}✓ Classpath 設置完成${NC}"

# 創建輸出目錄
echo -e "\n${BLUE}=== 5. 創建輸出目錄 ===${NC}"
mkdir -p out/production/Lineage381a
echo -e "${GREEN}✓ 輸出目錄已創建${NC}"

# 編譯 Java 源碼
echo -e "\n${BLUE}=== 6. 編譯 Java 源碼 ===${NC}"
echo -e "${YELLOW}開始編譯...${NC}"

# 找到所有 Java 文件
java_files=$(find src/main/java -name "*.java")
java_count=$(echo "$java_files" | wc -l)

echo -e "${YELLOW}找到 $java_count 個 Java 文件需要編譯${NC}"

# 執行編譯
javac -cp "$CLASSPATH" -d out/production/Lineage381a -encoding UTF-8 $java_files

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 編譯成功完成${NC}"
else
    echo -e "${RED}✗ 編譯失敗${NC}"
    echo -e "${YELLOW}請檢查編譯錯誤信息${NC}"
    exit 1
fi

# 驗證主類
echo -e "\n${BLUE}=== 7. 驗證編譯結果 ===${NC}"
if [ -f "out/production/Lineage381a/com/lineage/Server.class" ]; then
    echo -e "${GREEN}✓ 主類 Server.class 編譯成功${NC}"
else
    echo -e "${RED}✗ 主類 Server.class 編譯失敗${NC}"
    exit 1
fi

# 檢查優化類
optimization_classes=(
    "out/production/Lineage381a/com/lineage/server/utils/ConnectionPoolMonitor.class"
    "out/production/Lineage381a/com/lineage/server/utils/ConnectionPoolHealthChecker.class"
    "out/production/Lineage381a/com/lineage/server/utils/C3P0ConnectionCustomizer.class"
    "out/production/Lineage381a/com/lineage/server/command/executor/L1ConnectionPoolCommand.class"
)

echo -e "\n${BLUE}檢查優化類編譯狀態:${NC}"
for class_file in "${optimization_classes[@]}"; do
    if [ -f "$class_file" ]; then
        class_name=$(basename "$class_file" .class)
        echo -e "${GREEN}✓ $class_name${NC}"
    else
        class_name=$(basename "$class_file" .class)
        echo -e "${YELLOW}⚠ $class_name (可選)${NC}"
    fi
done

# 複製配置文件
echo -e "\n${BLUE}=== 8. 複製配置文件 ===${NC}"
if [ -d "config" ]; then
    cp -r config out/production/Lineage381a/
    echo -e "${GREEN}✓ 配置文件已複製${NC}"
fi

# 統計編譯結果
echo -e "\n${BLUE}=== 9. 編譯統計 ===${NC}"
class_count=$(find out/production/Lineage381a -name "*.class" | wc -l)
echo -e "${GREEN}✓ 總共編譯了 $class_count 個類文件${NC}"

# 檢查文件大小
output_size=$(du -sh out/production/Lineage381a | cut -f1)
echo -e "${GREEN}✓ 編譯輸出大小: $output_size${NC}"

echo -e "\n${BLUE}=========================================="
echo -e "編譯完成！"
echo -e "==========================================${NC}"
echo -e "${GREEN}下一步:${NC}"
echo -e "1. 執行系統優化: ${YELLOW}sudo ./ubuntu_optimization_6700k_64gb.sh${NC}"
echo -e "2. 啟動伺服器: ${YELLOW}./start_server.sh${NC}"
echo -e "3. 監控系統: ${YELLOW}./monitor_server.sh${NC}"
