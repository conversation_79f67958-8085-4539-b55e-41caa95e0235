# 🏗️ IntelliJ IDEA JAR 構建完整指南

## 📋 構建 JAR 文件步驟

### 步驟 1: 配置 Project Structure

1. **打開 Project Structure**
   ```
   File → Project Structure (Ctrl+Alt+Shift+S)
   ```

2. **檢查 Project Settings**
   - **Project**: 確保 Project SDK 是 Java 8
   - **Modules**: 確保所有源碼目錄正確標記

3. **檢查 Libraries**
   - 確保所有 JAR 依賴都已添加
   - 路徑應該指向 `jar/` 目錄中的文件

### 步驟 2: 創建 JAR Artifact

1. **添加新的 Artifact**
   - 在 Project Structure 中點擊 **"Artifacts"**
   - 點擊 **"+"** → **"JAR"** → **"From modules with dependencies..."**

2. **配置 JAR 設置**
   ```
   Main Class: com.lineage.Server
   JAR files from libraries: ✅ extract to the target JAR
   Directory for META-INF/MANIFEST.MF: 使用默認路徑
   ```

3. **高級配置**
   - **Name**: `Lineage381a_Ubuntu`
   - **Output directory**: `out/artifacts/Lineage381a_Ubuntu_jar/`
   - **Build on make**: ✅ 勾選

### 步驟 3: 配置 MANIFEST.MF

確保 MANIFEST.MF 包含正確的 Main-Class：
```
Manifest-Version: 1.0
Main-Class: com.lineage.Server
Class-Path: .
```

### 步驟 4: 包含配置文件

1. **在 Artifact 配置中**
   - 右鍵點擊 JAR 根目錄
   - 選擇 **"Put into Output Root"**
   - 添加 `config/` 目錄

2. **或者手動添加**
   - 將 `config/` 目錄拖拽到 JAR 結構中

### 步驟 5: 構建 JAR

1. **構建 Artifact**
   ```
   Build → Build Artifacts... → Lineage381a_Ubuntu → Build
   ```

2. **或使用快捷鍵**
   ```
   Ctrl+Shift+F9 (Build Project)
   然後 Build → Build Artifacts...
   ```

## 🔧 針對 64GB RAM 的 JAR 優化配置

### 創建優化的 MANIFEST.MF

手動編輯 MANIFEST.MF 文件：
```
Manifest-Version: 1.0
Main-Class: com.lineage.Server
Class-Path: .
JVM-Args: -Xms16g -Xmx32g -XX:+UseG1GC -XX:MaxGCPauseMillis=100
```

### 創建 Ubuntu 專用啟動腳本

構建完成後，創建以下啟動腳本：

#### run_lineage_ubuntu.sh
```bash
#!/bin/bash

# Lineage 381a Ubuntu JAR 啟動腳本
# 針對 i7-6700K + 64GB RAM 優化

echo "=========================================="
echo "Lineage 381a Server - JAR 版本"
echo "硬體: i7-6700K + 64GB RAM"
echo "=========================================="

# 設置 Java 環境
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 檢查 Java
if ! command -v java &> /dev/null; then
    echo "❌ Java 未安裝"
    echo "請執行: sudo apt install openjdk-8-jdk"
    exit 1
fi

# 檢查 JAR 文件
JAR_FILE="Lineage381a_Ubuntu.jar"
if [ ! -f "$JAR_FILE" ]; then
    echo "❌ 找不到 JAR 文件: $JAR_FILE"
    echo "請確保 JAR 文件在當前目錄中"
    exit 1
fi

# 創建日誌目錄
mkdir -p logs

# 設置 JVM 參數 (64GB RAM 優化)
JVM_OPTS="-Xms16g -Xmx32g"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=100"
JVM_OPTS="$JVM_OPTS -XX:G1HeapRegionSize=32m"
JVM_OPTS="$JVM_OPTS -XX:G1NewSizePercent=20"
JVM_OPTS="$JVM_OPTS -XX:G1MaxNewSizePercent=30"
JVM_OPTS="$JVM_OPTS -XX:+UnlockExperimentalVMOptions"
JVM_OPTS="$JVM_OPTS -XX:+UseStringDeduplication"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Taipei"
JVM_OPTS="$JVM_OPTS -Djava.net.preferIPv4Stack=true"
JVM_OPTS="$JVM_OPTS -Xloggc:logs/gc.log"

echo "Java 版本:"
java -version
echo

echo "JVM 參數: $JVM_OPTS"
echo "JAR 文件: $JAR_FILE"
echo

echo "=========================================="
echo "啟動 Lineage 伺服器..."
echo "=========================================="

# 啟動 JAR
java $JVM_OPTS -jar "$JAR_FILE"

echo "伺服器已退出"
```

## 📦 構建後的部署步驟

### 1. 在 Windows 上構建
```
1. 在 IntelliJ 中構建 JAR
2. JAR 文件位於: out/artifacts/Lineage381a_Ubuntu_jar/Lineage381a_Ubuntu.jar
```

### 2. 傳輸到 Ubuntu
```bash
# 使用 SCP 傳輸 JAR 文件
scp "out/artifacts/Lineage381a_Ubuntu_jar/Lineage381a_Ubuntu.jar" schung@your-ubuntu-ip:/home/<USER>/381a/

# 傳輸啟動腳本
scp "run_lineage_ubuntu.sh" schung@your-ubuntu-ip:/home/<USER>/381a/
```

### 3. 在 Ubuntu 上運行
```bash
cd /home/<USER>/381a
chmod +x run_lineage_ubuntu.sh
./run_lineage_ubuntu.sh
```

## 🔍 故障排除

### 常見問題 1: "找不到主類"
**解決方案**: 檢查 MANIFEST.MF 中的 Main-Class 設置

### 常見問題 2: "ClassNotFoundException"
**解決方案**: 確保所有依賴 JAR 都包含在構建中

### 常見問題 3: "配置文件找不到"
**解決方案**: 確保 config/ 目錄包含在 JAR 中

### 常見問題 4: 記憶體不足
**解決方案**: 調整 JVM 參數中的 -Xmx 值

## 🎯 最佳實踐

### 1. 構建前檢查
- ✅ 所有源碼編譯無錯誤
- ✅ 所有依賴 JAR 文件存在
- ✅ 配置文件路徑正確
- ✅ Main-Class 設置正確

### 2. 測試 JAR
在 Windows 上先測試：
```bash
java -jar Lineage381a_Ubuntu.jar
```

### 3. 版本管理
為不同環境創建不同的 JAR：
- `Lineage381a_Windows.jar`
- `Lineage381a_Ubuntu.jar`
- `Lineage381a_Production.jar`

## 📊 64GB RAM 優化效果

使用 JAR 部署的優勢：
- ✅ 簡化部署流程
- ✅ 避免編譯問題
- ✅ 統一運行環境
- ✅ 便於版本管理

預期效能：
- **JVM 記憶體**: 16-32GB
- **同時在線**: 1000-1500 玩家
- **啟動時間**: < 2 分鐘
- **記憶體使用率**: < 70%

---

**重要提醒**: 構建 JAR 時確保包含所有必要的依賴和配置文件，這樣就可以避免在 Ubuntu 上的編譯問題。
