#!/bin/bash

# 檢查編譯狀態並自動編譯
# 解決 Ubuntu 中的編譯問題

echo "=========================================="
echo "Lineage 381a 編譯檢查和修復工具"
echo "=========================================="

# 設置 Java 環境
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 1. 檢查 Java 環境
echo "1. 檢查 Java 環境..."
if ! command -v java &> /dev/null || ! command -v javac &> /dev/null; then
    echo "錯誤: Java 開發環境未安裝"
    echo "正在安裝 Java..."
    sudo apt update
    sudo apt install -y openjdk-8-jdk
    
    # 重新設置環境
    export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
    export PATH=$JAVA_HOME/bin:$PATH
fi

echo "✓ Java 版本:"
java -version
echo

# 2. 檢查項目結構
echo "2. 檢查項目結構..."
if [ ! -d "src/main/java" ]; then
    echo "錯誤: 找不到源碼目錄 src/main/java"
    echo "請確保在正確的項目根目錄中執行此腳本"
    exit 1
fi

if [ ! -f "src/main/java/com/lineage/Server.java" ]; then
    echo "錯誤: 找不到主類源文件 Server.java"
    exit 1
fi

echo "✓ 項目結構正確"

# 3. 檢查依賴文件
echo "3. 檢查依賴文件..."
required_jars=(
    "jar/log4j-1.2.16.jar"
    "jar/commons-logging-1.2.jar"
    "jar/c3p0-0.9.5.5.jar"
    "jar/mchange-commons-java-0.2.19.jar"
    "jar/mariadb-java-client-3.1.4.jar"
    "jar/javolution-5.5.1.jar"
)

missing_count=0
for jar in "${required_jars[@]}"; do
    if [ -f "$jar" ]; then
        echo "✓ $(basename $jar)"
    else
        echo "✗ $(basename $jar) 缺失"
        missing_count=$((missing_count + 1))
    fi
done

if [ $missing_count -gt 0 ]; then
    echo "錯誤: 缺少 $missing_count 個依賴文件"
    echo "請確保所有 JAR 文件都在 jar/ 目錄中"
    exit 1
fi

# 4. 檢查編譯狀態
echo "4. 檢查編譯狀態..."
need_compile=false

if [ ! -d "out/production/Lineage381a" ]; then
    echo "編譯輸出目錄不存在，需要編譯"
    need_compile=true
elif [ ! -f "out/production/Lineage381a/com/lineage/Server.class" ]; then
    echo "主類未編譯，需要編譯"
    need_compile=true
else
    # 檢查源文件是否比編譯文件新
    if [ "src/main/java/com/lineage/Server.java" -nt "out/production/Lineage381a/com/lineage/Server.class" ]; then
        echo "源文件已更新，需要重新編譯"
        need_compile=true
    else
        echo "✓ 編譯文件是最新的"
    fi
fi

# 5. 執行編譯 (如果需要)
if [ "$need_compile" = true ]; then
    echo "5. 執行編譯..."
    
    # 清理舊的編譯文件
    echo "清理舊的編譯文件..."
    rm -rf out/production/Lineage381a
    mkdir -p out/production/Lineage381a
    
    # 構建 classpath
    CLASSPATH=""
    for jar in "${required_jars[@]}"; do
        if [ -z "$CLASSPATH" ]; then
            CLASSPATH="$jar"
        else
            CLASSPATH="$CLASSPATH:$jar"
        fi
    done
    
    echo "開始編譯 Java 源碼..."
    
    # 找到所有 Java 文件
    java_files=$(find src/main/java -name "*.java")
    java_count=$(echo "$java_files" | wc -l)
    echo "找到 $java_count 個 Java 文件"
    
    # 執行編譯
    javac -cp "$CLASSPATH" -d out/production/Lineage381a -encoding UTF-8 $java_files
    
    if [ $? -eq 0 ]; then
        echo "✓ 編譯成功"
        
        # 複製配置文件
        if [ -d "config" ]; then
            cp -r config out/production/Lineage381a/
            echo "✓ 配置文件已複製"
        fi
        
        # 統計編譯結果
        class_count=$(find out/production/Lineage381a -name "*.class" | wc -l)
        echo "✓ 總共編譯了 $class_count 個類文件"
        
    else
        echo "✗ 編譯失敗"
        echo "常見解決方案:"
        echo "1. 檢查 Java 版本 (建議使用 Java 8)"
        echo "2. 檢查源碼編碼 (應該是 UTF-8)"
        echo "3. 檢查依賴 JAR 文件是否完整"
        exit 1
    fi
else
    echo "5. 跳過編譯 (文件已是最新)"
fi

# 6. 驗證編譯結果
echo "6. 驗證編譯結果..."
if [ -f "out/production/Lineage381a/com/lineage/Server.class" ]; then
    echo "✓ 主類編譯成功"
    
    # 檢查其他重要類
    important_classes=(
        "out/production/Lineage381a/com/lineage/DatabaseFactory.class"
        "out/production/Lineage381a/com/lineage/DatabaseFactoryLogin.class"
    )
    
    for class_file in "${important_classes[@]}"; do
        if [ -f "$class_file" ]; then
            class_name=$(basename "$class_file" .class)
            echo "✓ $class_name"
        else
            class_name=$(basename "$class_file" .class)
            echo "⚠ $class_name (可能不存在)"
        fi
    done
    
else
    echo "✗ 主類編譯失敗"
    exit 1
fi

echo
echo "=========================================="
echo "編譯檢查完成！"
echo "=========================================="
echo "下一步:"
echo "1. 啟動伺服器: ./start_ubuntu.sh"
echo "2. 或使用簡化啟動: ./simple_start.sh"
echo "3. 監控系統: ./monitor_server.sh"
