#!/bin/bash

# Lineage 381a 編譯問題快速修復腳本
# 針對 Ubuntu 24.02 環境

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=========================================="
echo -e "Lineage 381a 編譯問題快速修復"
echo -e "==========================================${NC}"

# 1. 檢查並安裝 Java
echo -e "\n${BLUE}=== 1. 檢查 Java 環境 ===${NC}"
if ! command -v javac &> /dev/null; then
    echo -e "${YELLOW}正在安裝 Java...${NC}"
    sudo apt update
    sudo apt install -y openjdk-8-jdk
    
    # 設置環境變數
    echo 'export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64' >> ~/.bashrc
    echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
    source ~/.bashrc
fi

export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

echo -e "${GREEN}✓ Java 環境已配置${NC}"
java -version

# 2. 清理舊的編譯文件
echo -e "\n${BLUE}=== 2. 清理舊的編譯文件 ===${NC}"
if [ -d "out/production/Lineage381a" ]; then
    rm -rf out/production/Lineage381a
    echo -e "${GREEN}✓ 舊編譯文件已清理${NC}"
fi

mkdir -p out/production/Lineage381a
echo -e "${GREEN}✓ 編譯目錄已創建${NC}"

# 3. 檢查源碼編碼
echo -e "\n${BLUE}=== 3. 檢查源碼編碼 ===${NC}"
if [ -f "src/main/java/com/lineage/Server.java" ]; then
    encoding=$(file -i src/main/java/com/lineage/Server.java | grep -o 'charset=[^;]*' | cut -d= -f2)
    echo -e "${GREEN}✓ 主類編碼: $encoding${NC}"
    
    if [ "$encoding" != "utf-8" ] && [ "$encoding" != "us-ascii" ]; then
        echo -e "${YELLOW}⚠ 檢測到非 UTF-8 編碼，正在轉換...${NC}"
        find src/main/java -name "*.java" -exec file -i {} \; | grep -v utf-8 | grep -v us-ascii | while read line; do
            file_path=$(echo $line | cut -d: -f1)
            echo "轉換文件: $file_path"
            iconv -f GBK -t UTF-8 "$file_path" > "${file_path}.tmp"
            mv "${file_path}.tmp" "$file_path"
        done
        echo -e "${GREEN}✓ 編碼轉換完成${NC}"
    fi
else
    echo -e "${RED}✗ 找不到主類源文件${NC}"
    exit 1
fi

# 4. 檢查依賴文件
echo -e "\n${BLUE}=== 4. 檢查依賴文件 ===${NC}"
required_jars=(
    "jar/log4j-1.2.16.jar"
    "jar/commons-logging-1.2.jar"
    "jar/c3p0-0.9.5.5.jar"
    "jar/mchange-commons-java-0.2.19.jar"
    "jar/mariadb-java-client-3.1.4.jar"
    "jar/javolution-5.5.1.jar"
)

missing_jars=()
for jar in "${required_jars[@]}"; do
    if [ -f "$jar" ]; then
        echo -e "${GREEN}✓ $(basename $jar)${NC}"
    else
        echo -e "${RED}✗ $(basename $jar)${NC}"
        missing_jars+=("$jar")
    fi
done

if [ ${#missing_jars[@]} -gt 0 ]; then
    echo -e "${RED}錯誤: 缺少以下依賴文件:${NC}"
    for jar in "${missing_jars[@]}"; do
        echo -e "${RED}  - $jar${NC}"
    done
    echo -e "${YELLOW}請確保所有 JAR 文件都在 jar/ 目錄中${NC}"
    exit 1
fi

# 5. 執行編譯
echo -e "\n${BLUE}=== 5. 執行編譯 ===${NC}"

# 構建 classpath
CLASSPATH=""
for jar in "${required_jars[@]}"; do
    if [ -z "$CLASSPATH" ]; then
        CLASSPATH="$jar"
    else
        CLASSPATH="$CLASSPATH:$jar"
    fi
done

echo -e "${YELLOW}開始編譯 Java 源碼...${NC}"

# 找到所有 Java 文件
java_files=$(find src/main/java -name "*.java" 2>/dev/null)
if [ -z "$java_files" ]; then
    echo -e "${RED}✗ 找不到 Java 源文件${NC}"
    exit 1
fi

java_count=$(echo "$java_files" | wc -l)
echo -e "${YELLOW}找到 $java_count 個 Java 文件${NC}"

# 執行編譯
echo -e "${YELLOW}執行編譯命令...${NC}"
javac -cp "$CLASSPATH" -d out/production/Lineage381a -encoding UTF-8 -Xlint:unchecked $java_files

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 編譯成功！${NC}"
else
    echo -e "${RED}✗ 編譯失敗${NC}"
    echo -e "${YELLOW}常見解決方案:${NC}"
    echo -e "1. 檢查 Java 版本是否為 8"
    echo -e "2. 檢查源碼編碼是否正確"
    echo -e "3. 檢查依賴 JAR 文件是否完整"
    exit 1
fi

# 6. 驗證編譯結果
echo -e "\n${BLUE}=== 6. 驗證編譯結果 ===${NC}"

# 檢查主類
if [ -f "out/production/Lineage381a/com/lineage/Server.class" ]; then
    echo -e "${GREEN}✓ 主類 Server.class 編譯成功${NC}"
else
    echo -e "${RED}✗ 主類編譯失敗${NC}"
    exit 1
fi

# 統計編譯結果
class_count=$(find out/production/Lineage381a -name "*.class" | wc -l)
echo -e "${GREEN}✓ 總共編譯了 $class_count 個類文件${NC}"

# 檢查關鍵類
key_classes=(
    "out/production/Lineage381a/com/lineage/DatabaseFactory.class"
    "out/production/Lineage381a/com/lineage/DatabaseFactoryLogin.class"
)

echo -e "\n${BLUE}檢查關鍵類:${NC}"
for class_file in "${key_classes[@]}"; do
    if [ -f "$class_file" ]; then
        class_name=$(basename "$class_file" .class)
        echo -e "${GREEN}✓ $class_name${NC}"
    else
        class_name=$(basename "$class_file" .class)
        echo -e "${RED}✗ $class_name${NC}"
    fi
done

# 7. 複製配置文件
echo -e "\n${BLUE}=== 7. 複製配置文件 ===${NC}"
if [ -d "config" ]; then
    cp -r config out/production/Lineage381a/
    echo -e "${GREEN}✓ 配置文件已複製${NC}"
fi

# 8. 設置權限
echo -e "\n${BLUE}=== 8. 設置權限 ===${NC}"
chmod +x start_server.sh
chmod +x monitor_server.sh
chmod +x ubuntu_optimization_6700k_64gb.sh
echo -e "${GREEN}✓ 腳本權限已設置${NC}"

echo -e "\n${BLUE}=========================================="
echo -e "編譯修復完成！"
echo -e "==========================================${NC}"
echo -e "${GREEN}下一步:${NC}"
echo -e "1. 啟動伺服器: ${YELLOW}./start_server.sh${NC}"
echo -e "2. 如果仍有問題，檢查: ${YELLOW}./verify_configuration.sh${NC}"
echo -e "3. 監控系統: ${YELLOW}./monitor_server.sh${NC}"
