@echo off
echo ========================================
echo Lineage 381a Server - Optimized Startup
echo ========================================

REM 設定 Java 路徑 (如果需要的話)
REM set JAVA_HOME=C:\Program Files\Java\jdk-11

REM 設定 ClassPath
set CLASSPATH=out\production\Lineage381a
set CLASSPATH=%CLASSPATH%;jar\c3p0-0.9.5.5.jar
set CLASSPATH=%CLASSPATH%;jar\commons-logging-1.2.jar
set CLASSPATH=%CLASSPATH%;jar\javolution-5.5.1.jar
set CLASSPATH=%CLASSPATH%;jar\log4j-1.2.16.jar
set CLASSPATH=%CLASSPATH%;jar\mariadb-java-client-3.1.4.jar
set CLASSPATH=%CLASSPATH%;jar\mchange-commons-java-0.2.19.jar

REM 優化的 JVM 參數
set JVM_OPTS=-server
set JVM_OPTS=%JVM_OPTS% -Xms1024m -Xmx2048m
set JVM_OPTS=%JVM_OPTS% -XX:NewRatio=2
set JVM_OPTS=%JVM_OPTS% -XX:SurvivorRatio=8
set JVM_OPTS=%JVM_OPTS% -XX:+UseG1GC
set JVM_OPTS=%JVM_OPTS% -XX:MaxGCPauseMillis=200
set JVM_OPTS=%JVM_OPTS% -XX:G1HeapRegionSize=16m
set JVM_OPTS=%JVM_OPTS% -XX:+UseStringDeduplication
set JVM_OPTS=%JVM_OPTS% -XX:+OptimizeStringConcat
set JVM_OPTS=%JVM_OPTS% -XX:+UseCompressedOops
set JVM_OPTS=%JVM_OPTS% -XX:+UseCompressedClassPointers

REM 記憶體和 GC 調優
set JVM_OPTS=%JVM_OPTS% -XX:+DisableExplicitGC
set JVM_OPTS=%JVM_OPTS% -XX:+UseThreadPriorities
set JVM_OPTS=%JVM_OPTS% -XX:ThreadPriorityPolicy=42
set JVM_OPTS=%JVM_OPTS% -XX:+HeapDumpOnOutOfMemoryError
set JVM_OPTS=%JVM_OPTS% -XX:HeapDumpPath=./logs/

REM 網路和 I/O 優化
set JVM_OPTS=%JVM_OPTS% -Djava.net.preferIPv4Stack=true
set JVM_OPTS=%JVM_OPTS% -Djava.awt.headless=true
set JVM_OPTS=%JVM_OPTS% -Dfile.encoding=UTF-8
set JVM_OPTS=%JVM_OPTS% -Dsun.nio.ch.bugLevel=""

REM 效能監控 (可選)
REM set JVM_OPTS=%JVM_OPTS% -XX:+PrintGC
REM set JVM_OPTS=%JVM_OPTS% -XX:+PrintGCDetails
REM set JVM_OPTS=%JVM_OPTS% -XX:+PrintGCTimeStamps
REM set JVM_OPTS=%JVM_OPTS% -Xloggc:./logs/gc.log

echo JVM 參數: %JVM_OPTS%
echo.

REM 檢查主類是否存在
if not exist "out\production\Lineage381a\com\lineage\Server.class" (
    echo 錯誤: 找不到主類文件 Server.class
    echo 請確保專案已經編譯完成
    pause
    exit /b 1
)

REM 檢查配置文件是否存在
if not exist "config\sql.properties" (
    echo 錯誤: 找不到資料庫配置文件 config\sql.properties
    echo 請確保配置文件存在
    pause
    exit /b 1
)

REM 創建日誌目錄
if not exist "logs" mkdir logs

echo 啟動 Lineage 伺服器 (優化版)...
echo 記憶體配置: 1GB-2GB
echo 垃圾回收器: G1GC
echo.

REM 啟動伺服器
java %JVM_OPTS% -cp "%CLASSPATH%" com.lineage.Server

REM 如果程式結束，暫停以查看錯誤訊息
echo.
echo 伺服器已停止
pause
