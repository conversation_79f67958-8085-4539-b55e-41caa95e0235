2025-06-28 09:32:05,Config/王族_技能設定表讀取完成 (0ms)
2025-06-28 09:32:05,Config/騎士_技能設定表讀取完成 (1ms)
2025-06-28 09:32:05,Config/妖精_技能設定表讀取完成 (1ms)
2025-06-28 09:32:05,Config/法師_技能設定表讀取完成 (0ms)
2025-06-28 09:32:05,Config/黑暗妖精_技能設定表讀取完成 (0ms)
2025-06-28 09:32:05,Config/龍騎士_技能設定表讀取完成 (0ms)
2025-06-28 09:32:05,Config/幻術師_技能設定表讀取完成 (0ms)
2025-06-28 09:32:07,MLog clients using log4j logging.
2025-06-28 09:32:07,Reading VM config for path list /com/mchange/v2/log/default-mchange-log.properties, /mchange-commons.properties, /c3p0.properties, hocon:/reference,/application,/c3p0,/, /mchange-log.properties, /
2025-06-28 09:32:07,The configuration file for resource identifier '/mchange-commons.properties' could not be found. Skipping.
2025-06-28 09:32:07,The configuration file for resource identifier '/mchange-log.properties' could not be found. Skipping.
2025-06-28 09:32:07,The configuration file for resource identifier 'hocon:/reference,/application,/c3p0,/' could not be found. Skipping.
2025-06-28 09:32:07,The configuration file for resource identifier '/c3p0.properties' could not be found. Skipping.
2025-06-28 09:32:07,The configuration file for resource identifier '/mchange-commons.properties' could not be found. Skipping.
2025-06-28 09:32:07,The configuration file for resource identifier '/c3p0.properties' could not be found. Skipping.
2025-06-28 09:32:07,The configuration file for resource identifier 'hocon:/reference,/application,/c3p0,/' could not be found. Skipping.
2025-06-28 09:32:07,The configuration file for resource identifier '/mchange-log.properties' could not be found. Skipping.
2025-06-28 09:32:07,Initializing c3p0-******* [built 11-December-2019 22:18:33 -0800; debug? true; trace: 10]
2025-06-28 09:32:07,MBean: com.mchange.v2.c3p0:type=PooledDataSource,identityToken=1hge106bb1dzlm4q1qoxgvp|7d907bac,name=1hge106bb1dzlm4q1qoxgvp|7d907bac registered.
2025-06-28 09:32:07,MBean: com.mchange.v2.c3p0:type=PooledDataSource,identityToken=1hge106bb1dzlm4q1qoxgvp|7d907bac,name=1hge106bb1dzlm4q1qoxgvp|7d907bac unregistered, in order to be reregistered after update.
2025-06-28 09:32:07,MBean: com.mchange.v2.c3p0:type=PooledDataSource,identityToken=1hge106bb1dzlm4q1qoxgvp|7d907bac,name=1hge106bb1dzlm4q1qoxgvp|7d907bac registered.
2025-06-28 09:32:07,Initializing c3p0 pool... com.mchange.v2.c3p0.ComboPooledDataSource [ acquireIncrement -> 2, acquireRetryAttempts -> 3, acquireRetryDelay -> 1000, autoCommitOnClose -> false, automaticTestTable -> null, breakAfterAcquireFailure -> false, checkoutTimeout -> 30000, connectionCustomizerClassName -> null, connectionTesterClassName -> com.mchange.v2.c3p0.impl.DefaultConnectionTester, contextClassLoaderSource -> caller, dataSourceName -> 1hge106bb1dzlm4q1qoxgvp|7d907bac, debugUnreturnedConnectionStackTraces -> false, description -> null, driverClass -> org.mariadb.jdbc.Driver, extensions -> {}, factoryClassLocation -> null, forceIgnoreUnresolvedTransactions -> false, forceSynchronousCheckins -> false, forceUseNamedDriverClass -> false, identityToken -> 1hge106bb1dzlm4q1qoxgvp|7d907bac, idleConnectionTestPeriod -> 300, initialPoolSize -> 3, jdbcUrl -> **********************************************************************************************, maxAdministrativeTaskTime -> 0, maxConnectionAge -> 3600, maxIdleTime -> 1800, maxIdleTimeExcessConnections -> 0, maxPoolSize -> 20, maxStatements -> 100, maxStatementsPerConnection -> 25, minPoolSize -> 3, numHelperThreads -> 3, preferredTestQuery -> SELECT 1, privilegeSpawnedThreads -> false, properties -> {user=******, password=******}, propertyCycle -> 0, statementCacheNumDeferredCloseThreads -> 0, testConnectionOnCheckin -> true, testConnectionOnCheckout -> false, unreturnedConnectionTimeout -> 300, userOverrides -> {}, usesTraditionalReflectiveProxies -> false ]
2025-06-28 09:32:07,The configuration file for resource identifier '/mchange-commons.properties' could not be found. Skipping.
2025-06-28 09:32:07,The configuration file for resource identifier '/mchange-log.properties' could not be found. Skipping.
2025-06-28 09:32:07,The configuration file for resource identifier '/c3p0.properties' could not be found. Skipping.
2025-06-28 09:32:07,The configuration file for resource identifier 'hocon:/reference,/application,/c3p0,/' could not be found. Skipping.
2025-06-28 09:32:07,com.mchange.v2.resourcepool.BasicResourcePool@1990a65e config: [start -> 3; min -> 3; max -> 20; inc -> 2; num_acq_attempts -> 3; acq_attempt_delay -> 1000; check_idle_resources_delay -> 300000; max_resource_age -> 3600000; max_idle_time -> 1800000; excess_max_idle_time -> 0; destroy_unreturned_resc_time -> 300000; expiration_enforcement_delay -> 75000; break_on_acquisition_failure -> false; debug_store_checkout_exceptions -> false; force_synchronous_checkins -> false]
2025-06-28 09:32:07,Created new pool for auth, username (masked): 'ro******'.
2025-06-28 09:32:07,acquire test -- pool size: 0; target_pool_size: 3; desired target? 1
2025-06-28 09:32:07,awaitAvailable(): [unknown]
2025-06-28 09:32:07,登入資料庫連接池初始化成功
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN.
2025-06-28 09:32:07,MBean: com.mchange.v2.c3p0:type=PooledDataSource,identityToken=1hge106bb1dzlm4q1qoxgvp|150c158,name=1hge106bb1dzlm4q1qoxgvp|150c158 registered.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,MBean: com.mchange.v2.c3p0:type=PooledDataSource,identityToken=1hge106bb1dzlm4q1qoxgvp|150c158,name=1hge106bb1dzlm4q1qoxgvp|150c158 unregistered, in order to be reregistered after update.
2025-06-28 09:32:07,MBean: com.mchange.v2.c3p0:type=PooledDataSource,identityToken=1hge106bb1dzlm4q1qoxgvp|150c158,name=1hge106bb1dzlm4q1qoxgvp|150c158 registered.
2025-06-28 09:32:07,Initializing c3p0 pool... com.mchange.v2.c3p0.ComboPooledDataSource [ acquireIncrement -> 3, acquireRetryAttempts -> 3, acquireRetryDelay -> 1000, autoCommitOnClose -> false, automaticTestTable -> null, breakAfterAcquireFailure -> false, checkoutTimeout -> 30000, connectionCustomizerClassName -> null, connectionTesterClassName -> com.mchange.v2.c3p0.impl.DefaultConnectionTester, contextClassLoaderSource -> caller, dataSourceName -> 1hge106bb1dzlm4q1qoxgvp|150c158, debugUnreturnedConnectionStackTraces -> false, description -> null, driverClass -> org.mariadb.jdbc.Driver, extensions -> {}, factoryClassLocation -> null, forceIgnoreUnresolvedTransactions -> false, forceSynchronousCheckins -> false, forceUseNamedDriverClass -> false, identityToken -> 1hge106bb1dzlm4q1qoxgvp|150c158, idleConnectionTestPeriod -> 300, initialPoolSize -> 5, jdbcUrl -> **********************************************************************************************, maxAdministrativeTaskTime -> 0, maxConnectionAge -> 3600, maxIdleTime -> 1800, maxIdleTimeExcessConnections -> 0, maxPoolSize -> 50, maxStatements -> 200, maxStatementsPerConnection -> 50, minPoolSize -> 5, numHelperThreads -> 3, preferredTestQuery -> SELECT 1, privilegeSpawnedThreads -> false, properties -> {user=******, password=******}, propertyCycle -> 0, statementCacheNumDeferredCloseThreads -> 0, testConnectionOnCheckin -> true, testConnectionOnCheckout -> false, unreturnedConnectionTimeout -> 300, userOverrides -> {}, usesTraditionalReflectiveProxies -> false ]
2025-06-28 09:32:07,com.mchange.v2.resourcepool.BasicResourcePool@3578436e config: [start -> 5; min -> 5; max -> 50; inc -> 3; num_acq_attempts -> 3; acq_attempt_delay -> 1000; check_idle_resources_delay -> 300000; max_resource_age -> 3600000; max_idle_time -> 1800000; excess_max_idle_time -> 0; destroy_unreturned_resc_time -> 300000; expiration_enforcement_delay -> 75000; break_on_acquisition_failure -> false; debug_store_checkout_exceptions -> false; force_synchronous_checkins -> false]
2025-06-28 09:32:07,Created new pool for auth, username (masked): 'ro******'.
2025-06-28 09:32:07,acquire test -- pool size: 0; target_pool_size: 5; desired target? 1
2025-06-28 09:32:07,awaitAvailable(): [unknown]
2025-06-28 09:32:07,資料庫連接池初始化成功
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,廣播_寶箱_顯示開盒公告->1
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,廣播_殺人_顯示殺人公告->1
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,廣播_掉寶_顯示掉寶公告->1
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,廣播_定時_顯示定時公告->4
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,Config->Server設置: 0筆 / 處理: 1筆
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN.
2025-06-28 09:32:07,com.mchange.v2.c3p0.impl.NewProxyPreparedStatement@1e04fa0a [wrapping: ClientPreparedStatement{sql:'SELECT * FROM `server_info`', parameters:[]}] closed orphaned ResultSet: com.mchange.v2.c3p0.impl.NewProxyResultSet@7205765b [wrapping: null]
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN.
2025-06-28 09:32:07,載入服務器存檔資料完成  (2ms)
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,目前對像使用新ID: 128
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,載入已用最大id編號: 10000(1ms)
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,com.mchange.v2.c3p0.impl.NewProxyPreparedStatement@37d4349f [wrapping: ClientPreparedStatement{sql:'SELECT * FROM `accounts`', parameters:[]}] closed orphaned ResultSet: com.mchange.v2.c3p0.impl.NewProxyResultSet@5b03b9fe [wrapping: null]
2025-06-28 09:32:07,載入已有帳戶名稱資料數量: 0(0ms)
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,載入DB化系統設定檔資料數量: 9(1ms)
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,載入經驗質設置資料數量: 202(2ms)
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,載入圖形影格資料數量: 8716(19ms)
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,載入地圖設置資料數量: 700(6ms)
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,載入地圖等極限制資料數量: 6(1ms)
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,載入時間限制道具: 36(0ms)
2025-06-28 09:32:07,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:07,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:07,MAP進行數據加載...
2025-06-28 09:32:08,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN.
2025-06-28 09:32:08,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:08,初始連接次數: 1
2025-06-28 09:32:08,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:08,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:08,載入NPC設置資料數量: 2986(162ms)
2025-06-28 09:32:08,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:08,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:08,載入NPC積分設置資料數量: 0(0ms)
2025-06-28 09:32:08,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:08,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:08,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:08,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:08,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:08,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:08,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:08,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:08,遊戲世界儲存中心建立完成!!!
2025-06-28 09:32:08,載入陷阱資料數量: 54(2ms)
2025-06-28 09:32:08,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:08,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:08,載入陷阱召喚資料數量: 4832(9ms)
2025-06-28 09:32:08,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:08,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,載入道具,武器,防具資料: 3916+517+1593=6026(265ms)
2025-06-28 09:32:09,載入掉落物品資料數量: 854(7ms)
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,載入掉落物品資料數量(指定地圖): 145(1ms)
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:09,載入掉落物品機率資料數量: 0(0ms)
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,載入掉落物品強化值資料數量: 1(0ms)
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,載入技能設置資料數量: 642(2ms)
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,載入購買技能 材料 設置資料數量: 23(1ms)
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,載入MOB隊伍資料數量: 71(1ms)
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,載入NPC對話資料數量: 892(1ms)
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,載入NPC XML對話結果資料 (66ms)
2025-06-28 09:32:09,載入召喚時間資料數量: 0(0ms)
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,載入MOB技能資料數量: 1670(128ms)
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,載入召喚MOB資料數量: 57158(238ms)
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,載入人物變身資料數量: 313(1ms)
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1e08e0d1] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:09,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:09,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@9473e90] on CHECKIN.
