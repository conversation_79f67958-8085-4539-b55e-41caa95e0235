2025-06-28 08:53:55,Config/����_�ޯ�]�w��Ū������ (0ms)
2025-06-28 08:53:55,Config/�M�h_�ޯ�]�w��Ū������ (0ms)
2025-06-28 08:53:55,Config/����_�ޯ�]�w��Ū������ (1ms)
2025-06-28 08:53:55,Config/�k�v_�ޯ�]�w��Ū������ (0ms)
2025-06-28 08:53:55,Config/�·t����_�ޯ�]�w��Ū������ (0ms)
2025-06-28 08:53:55,Config/�s�M�h_�ޯ�]�w��Ū������ (1ms)
2025-06-28 08:53:55,Config/�۳N�v_�ޯ�]�w��Ū������ (0ms)
2025-06-28 08:53:57,MLog clients using log4j logging.
2025-06-28 08:53:57,Reading VM config for path list /com/mchange/v2/log/default-mchange-log.properties, /mchange-commons.properties, /c3p0.properties, hocon:/reference,/application,/c3p0,/, /mchange-log.properties, /
2025-06-28 08:53:57,The configuration file for resource identifier '/mchange-commons.properties' could not be found. Skipping.
2025-06-28 08:53:57,The configuration file for resource identifier '/mchange-log.properties' could not be found. Skipping.
2025-06-28 08:53:57,The configuration file for resource identifier 'hocon:/reference,/application,/c3p0,/' could not be found. Skipping.
2025-06-28 08:53:57,The configuration file for resource identifier '/c3p0.properties' could not be found. Skipping.
2025-06-28 08:53:57,The configuration file for resource identifier '/mchange-commons.properties' could not be found. Skipping.
2025-06-28 08:53:57,The configuration file for resource identifier '/c3p0.properties' could not be found. Skipping.
2025-06-28 08:53:57,The configuration file for resource identifier 'hocon:/reference,/application,/c3p0,/' could not be found. Skipping.
2025-06-28 08:53:57,The configuration file for resource identifier '/mchange-log.properties' could not be found. Skipping.
2025-06-28 08:53:57,Initializing c3p0-******* [built 11-December-2019 22:18:33 -0800; debug? true; trace: 10]
2025-06-28 08:53:57,MBean: com.mchange.v2.c3p0:type=PooledDataSource,identityToken=1hge106bb1dy8jn9ivabrf|307f6b8c,name=1hge106bb1dy8jn9ivabrf|307f6b8c registered.
2025-06-28 08:53:57,MBean: com.mchange.v2.c3p0:type=PooledDataSource,identityToken=1hge106bb1dy8jn9ivabrf|307f6b8c,name=1hge106bb1dy8jn9ivabrf|307f6b8c unregistered, in order to be reregistered after update.
2025-06-28 08:53:57,MBean: com.mchange.v2.c3p0:type=PooledDataSource,identityToken=1hge106bb1dy8jn9ivabrf|307f6b8c,name=1hge106bb1dy8jn9ivabrf|307f6b8c registered.
2025-06-28 08:53:57,Initializing c3p0 pool... com.mchange.v2.c3p0.ComboPooledDataSource [ acquireIncrement -> 3, acquireRetryAttempts -> 30, acquireRetryDelay -> 1000, autoCommitOnClose -> false, automaticTestTable -> null, breakAfterAcquireFailure -> false, checkoutTimeout -> 0, connectionCustomizerClassName -> null, connectionTesterClassName -> com.mchange.v2.c3p0.impl.DefaultConnectionTester, contextClassLoaderSource -> caller, dataSourceName -> 1hge106bb1dy8jn9ivabrf|307f6b8c, debugUnreturnedConnectionStackTraces -> false, description -> null, driverClass -> org.mariadb.jdbc.Driver, extensions -> {}, factoryClassLocation -> null, forceIgnoreUnresolvedTransactions -> false, forceSynchronousCheckins -> false, forceUseNamedDriverClass -> false, identityToken -> 1hge106bb1dy8jn9ivabrf|307f6b8c, idleConnectionTestPeriod -> 0, initialPoolSize -> 3, jdbcUrl -> **********************************************************************************************, maxAdministrativeTaskTime -> 0, maxConnectionAge -> 0, maxIdleTime -> 0, maxIdleTimeExcessConnections -> 0, maxPoolSize -> 15, maxStatements -> 0, maxStatementsPerConnection -> 0, minPoolSize -> 3, numHelperThreads -> 3, preferredTestQuery -> null, privilegeSpawnedThreads -> false, properties -> {user=******, password=******}, propertyCycle -> 0, statementCacheNumDeferredCloseThreads -> 0, testConnectionOnCheckin -> false, testConnectionOnCheckout -> false, unreturnedConnectionTimeout -> 0, userOverrides -> {}, usesTraditionalReflectiveProxies -> false ]
2025-06-28 08:53:57,The configuration file for resource identifier '/mchange-commons.properties' could not be found. Skipping.
2025-06-28 08:53:57,The configuration file for resource identifier '/mchange-log.properties' could not be found. Skipping.
2025-06-28 08:53:57,The configuration file for resource identifier '/c3p0.properties' could not be found. Skipping.
2025-06-28 08:53:57,The configuration file for resource identifier 'hocon:/reference,/application,/c3p0,/' could not be found. Skipping.
2025-06-28 08:53:57,com.mchange.v2.resourcepool.BasicResourcePool@7c417213 config: [start -> 3; min -> 3; max -> 15; inc -> 3; num_acq_attempts -> 30; acq_attempt_delay -> 1000; check_idle_resources_delay -> 0; max_resource_age -> 0; max_idle_time -> 0; excess_max_idle_time -> 0; destroy_unreturned_resc_time -> 0; expiration_enforcement_delay -> 0; break_on_acquisition_failure -> false; debug_store_checkout_exceptions -> false; force_synchronous_checkins -> false]
2025-06-28 08:53:57,Created new pool for auth, username (masked): 'ro******'.
2025-06-28 08:53:57,acquire test -- pool size: 0; target_pool_size: 3; desired target? 1
2025-06-28 08:53:57,awaitAvailable(): [unknown]
2025-06-28 08:53:57,MBean: com.mchange.v2.c3p0:type=PooledDataSource,identityToken=1hge106bb1dy8jn9ivabrf|5e4c8041,name=1hge106bb1dy8jn9ivabrf|5e4c8041 registered.
2025-06-28 08:53:57,MBean: com.mchange.v2.c3p0:type=PooledDataSource,identityToken=1hge106bb1dy8jn9ivabrf|5e4c8041,name=1hge106bb1dy8jn9ivabrf|5e4c8041 unregistered, in order to be reregistered after update.
2025-06-28 08:53:57,MBean: com.mchange.v2.c3p0:type=PooledDataSource,identityToken=1hge106bb1dy8jn9ivabrf|5e4c8041,name=1hge106bb1dy8jn9ivabrf|5e4c8041 registered.
2025-06-28 08:53:57,Initializing c3p0 pool... com.mchange.v2.c3p0.ComboPooledDataSource [ acquireIncrement -> 3, acquireRetryAttempts -> 30, acquireRetryDelay -> 1000, autoCommitOnClose -> false, automaticTestTable -> null, breakAfterAcquireFailure -> false, checkoutTimeout -> 0, connectionCustomizerClassName -> null, connectionTesterClassName -> com.mchange.v2.c3p0.impl.DefaultConnectionTester, contextClassLoaderSource -> caller, dataSourceName -> 1hge106bb1dy8jn9ivabrf|5e4c8041, debugUnreturnedConnectionStackTraces -> false, description -> null, driverClass -> org.mariadb.jdbc.Driver, extensions -> {}, factoryClassLocation -> null, forceIgnoreUnresolvedTransactions -> false, forceSynchronousCheckins -> false, forceUseNamedDriverClass -> false, identityToken -> 1hge106bb1dy8jn9ivabrf|5e4c8041, idleConnectionTestPeriod -> 0, initialPoolSize -> 3, jdbcUrl -> **********************************************************************************************, maxAdministrativeTaskTime -> 0, maxConnectionAge -> 0, maxIdleTime -> 0, maxIdleTimeExcessConnections -> 0, maxPoolSize -> 15, maxStatements -> 0, maxStatementsPerConnection -> 0, minPoolSize -> 3, numHelperThreads -> 3, preferredTestQuery -> null, privilegeSpawnedThreads -> false, properties -> {user=******, password=******}, propertyCycle -> 0, statementCacheNumDeferredCloseThreads -> 0, testConnectionOnCheckin -> false, testConnectionOnCheckout -> false, unreturnedConnectionTimeout -> 0, userOverrides -> {}, usesTraditionalReflectiveProxies -> false ]
2025-06-28 08:53:57,com.mchange.v2.resourcepool.BasicResourcePool@69930714 config: [start -> 3; min -> 3; max -> 15; inc -> 3; num_acq_attempts -> 30; acq_attempt_delay -> 1000; check_idle_resources_delay -> 0; max_resource_age -> 0; max_idle_time -> 0; excess_max_idle_time -> 0; destroy_unreturned_resc_time -> 0; expiration_enforcement_delay -> 0; break_on_acquisition_failure -> false; debug_store_checkout_exceptions -> false; force_synchronous_checkins -> false]
2025-06-28 08:53:57,Created new pool for auth, username (masked): 'ro******'.
2025-06-28 08:53:57,acquire test -- pool size: 0; target_pool_size: 3; desired target? 1
2025-06-28 08:53:57,awaitAvailable(): [unknown]
2025-06-28 08:53:58,�s��_�_�c_��ܶ}�����i->1
2025-06-28 08:53:58,�s��_���H_��ܱ��H���i->1
2025-06-28 08:53:58,�s��_���__��ܱ��_���i->1
2025-06-28 08:53:58,�s��_�w��_��ܩw�ɤ��i->4
2025-06-28 08:53:58,Config->Server�]�m: 0�� / �B�z: 1��
2025-06-28 08:53:58,com.mchange.v2.c3p0.impl.NewProxyPreparedStatement@782859e [wrapping: ClientPreparedStatement{sql:'SELECT * FROM `server_info`', parameters:[]}] closed orphaned ResultSet: com.mchange.v2.c3p0.impl.NewProxyResultSet@4d15107f [wrapping: null]
2025-06-28 08:53:58,���J�A�Ⱦ��s�ɸ�Ƨ���  (3ms)
2025-06-28 08:53:58,�ثe�ﹳ�ϥηsID: 128
2025-06-28 08:53:58,���J�w�γ̤jid�s��: 10000(0ms)
2025-06-28 08:53:58,com.mchange.v2.c3p0.impl.NewProxyPreparedStatement@6a4f1a55 [wrapping: ClientPreparedStatement{sql:'SELECT * FROM `accounts`', parameters:[]}] closed orphaned ResultSet: com.mchange.v2.c3p0.impl.NewProxyResultSet@4dc27487 [wrapping: null]
2025-06-28 08:53:58,���J�w���b��W�ٸ�Ƽƶq: 0(0ms)
2025-06-28 08:53:58,���JDB�ƨt�γ]�w�ɸ�Ƽƶq: 9(1ms)
2025-06-28 08:53:58,���J�g���]�m��Ƽƶq: 202(3ms)
2025-06-28 08:53:58,���J�ϧμv���Ƽƶq: 8716(21ms)
2025-06-28 08:53:58,���J�a�ϳ]�m��Ƽƶq: 700(7ms)
2025-06-28 08:53:58,���J�a�ϵ��������Ƽƶq: 6(1ms)
2025-06-28 08:53:58,���J�ɶ�����D��: 36(2ms)
2025-06-28 08:53:58,MAP�i��ƾڥ[��...
2025-06-28 08:53:59,���JNPC�]�m��Ƽƶq: 2986(155ms)
2025-06-28 08:53:59,���JNPC�n���]�m��Ƽƶq: 0(2ms)
2025-06-28 08:53:59,�C���@���x�s���߫إߧ���!!!
2025-06-28 08:53:59,���J������Ƽƶq: 54(2ms)
2025-06-28 08:53:59,���J�����l���Ƽƶq: 4832(8ms)
2025-06-28 08:53:59,���J�D��,�Z��,������: 3916+517+1593=6026(263ms)
2025-06-28 08:53:59,���J�������~��Ƽƶq: 854(7ms)
2025-06-28 08:53:59,���J�������~��Ƽƶq(���w�a��): 145(2ms)
2025-06-28 08:53:59,���J�������~���v��Ƽƶq: 0(0ms)
2025-06-28 08:53:59,���J�������~�j�ƭȸ�Ƽƶq: 1(1ms)
2025-06-28 08:53:59,���J�ޯ�]�m��Ƽƶq: 642(3ms)
2025-06-28 08:53:59,���J�ʶR�ޯ� ���� �]�m��Ƽƶq: 23(1ms)
2025-06-28 08:53:59,���JMOB�����Ƽƶq: 71(1ms)
2025-06-28 08:53:59,���JNPC��ܸ�Ƽƶq: 892(2ms)
2025-06-28 08:53:59,���JNPC XML��ܵ��G��� (69ms)
2025-06-28 08:53:59,���J�l��ɶ���Ƽƶq: 0(2ms)
2025-06-28 08:54:00,���JMOB�ޯ��Ƽƶq: 1670(141ms)
2025-06-28 08:54:00,���J�l��MOB��Ƽƶq: 57158(259ms)
2025-06-28 08:54:00,���J�H���ܨ���Ƽƶq: 313(2ms)
2025-06-28 08:54:00,���J�ө��c���Ƽƶq: 107(413ms)
2025-06-28 08:54:00,���J�ӫ��c�檫�~��Ƽƶq: 2(0ms)
2025-06-28 08:54:00,���J�a�Ϥ����I�]�m�ƶq: 1505(2ms)
2025-06-28 08:54:00,���J�a�Ϥ����I�]�m(�h�I)�ƶq: 10(0ms)
2025-06-28 08:54:00,���J�l��NPC��Ƽƶq: 779/779(18ms)
2025-06-28 08:54:00,���J����ܮw����M���Ƽƶq: 0/0(0ms)
2025-06-28 08:54:00,���J�����Ƹ�Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J�������ɬ�����Ƽƶq: 0(2ms)
2025-06-28 08:54:00,���J������Ƽƶq: 8(1ms)
2025-06-28 08:54:00,���J�^���y�и�Ƽƶq: 281(1ms)
2025-06-28 08:54:00,���J����Ƽƶq: 594(3ms)
2025-06-28 08:54:00,���J�ޯ�Z����Ƽƶq: 3(1ms)
2025-06-28 08:54:00,���J�ޯ�Z����O��Ƽƶq: 44(7ms)
2025-06-28 08:54:00,���J�ޯ�Z���]�m��Ƽƶq: 119(7ms)
2025-06-28 08:54:00,���J�^���y�г]�m�ƶq: 343(1ms)
2025-06-28 08:54:00,���J�d���رڸ�Ƽƶq: 39(0ms)
2025-06-28 08:54:00,���J�d���D���Ƽƶq: 18(0ms)
2025-06-28 08:54:00,���J�c�l�}�X���]�m: 89/886(1ms)
2025-06-28 08:54:00,���J�c�l�}�X���]�m(�h��): 77/247(0ms)
2025-06-28 08:54:00,���J�c�l�}�X���]�m(���w�ϥΪ��~�}��): 0/0(0ms)
2025-06-28 08:54:00,���J���Ѫ��~�]�m��Ƽƶq: 281(0ms)
2025-06-28 08:54:00,���JNPC�ǰe�I�]�m�ƶq: 8(0ms)
2025-06-28 08:54:00,���J�ɶ��a�ϳ]�m�ƶq: 0(0ms)
2025-06-28 08:54:00,���J�ζ��a�ϳ]�m�ƶq: 0(0ms)
2025-06-28 08:54:00,���J�x��ǰe�I�]�m�ƶq: 0(0ms)
2025-06-28 08:54:00,���JNPC�|�ܸ�Ƽƶq: 26(0ms)
2025-06-28 08:54:00,���J�M�˳]�m�ƶq: 70(1ms)
2025-06-28 08:54:00,���J�M�ˮĪG�Ʀr�}�C: 21ms)
2025-06-28 08:54:00,Ū��=>�ǰe���b�ǰe�I�w�q�ƶq: 284(2ms)
2025-06-28 08:54:00,���J���~�ɯŸ�Ƽƶq: 393(3ms)
2025-06-28 08:54:00,���J�޲z�̩R�O�ƶq: 102(1ms)
2025-06-28 08:54:00,���J�s�⪫�~��Ƽƶq: 1/32(1ms)
2025-06-28 08:54:00,���J�������D��: 3(1ms)
2025-06-28 08:54:00,���J����ˤͰө�������~��Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J����������D��: 0(1ms)
2025-06-28 08:54:00,���J����������D��: 1(0ms)
2025-06-28 08:54:00,���JBOSS�l���Ƽƶq: 42(6ms)
2025-06-28 08:54:00,���J���θ�Ƽƶq: 129(3ms)
2025-06-28 08:54:00,���J�T��n�JIP��Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J�T��n�JNAME��Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J������Ƽƶq: 10(1ms)
2025-06-28 08:54:00,���J�H���Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J���Ω�椽�i���Ƽƶq: 62(2ms)
2025-06-28 08:54:00,���J�G�i���Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J�O�d�ޯ������Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J�H���ޯ������Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J�H���ֳt�������Ƽƶq: 0(2ms)
2025-06-28 08:54:00,���J�H���n�ͬ�����Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J�O�Юy�Ь�����Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J�H���O�Юy�вM���Ƽƶq: 0(1ms)
2025-06-28 08:54:00,���J�B�~������Ƽƶq: 0(1ms)
2025-06-28 08:54:00,���J�B�~������Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J�B�~������Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J�B�~������Ƽƶq: 0(2ms)
2025-06-28 08:54:00,>>>>>>>>(�C������)������Ȧ���^�m�M�z�����C
2025-06-28 08:54:00,���J�H�����Ȭ�����Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J�T��W�ټƶq: 1320
2025-06-28 08:54:00,���J���[�]�m��Ƽƶq: 595(1ms)
2025-06-28 08:54:00,���J�H���I�]����M���Ƽƶq: 0/0(0ms)
2025-06-28 08:54:00,���J�b���ܮw����M���Ƽƶq: 0/0(0ms)
2025-06-28 08:54:00,���J���F�ܮw����M���Ƽƶq: 0/0(0ms)
2025-06-28 08:54:00,���J����M�ݭܮw����M���Ƽƶq: 0/0(0ms)
2025-06-28 08:54:00,���J������O��Ƽƶq: 303(22ms)
2025-06-28 08:54:00,���J������O��Ƽƶq: 0(2ms)
2025-06-28 08:54:00,���J�d����Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J���~�ϥδ�����Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J�_�����������Ƽƶq: 0(0ms)
2025-06-28 08:54:00,���J�U��D���Ƽƶq: 0/0(0ms)
2025-06-28 08:54:00,���J�T���檫�~��Ƽƶq: 2(1ms)
2025-06-28 08:54:00,���J�������y���~: 1(1ms)
2025-06-28 08:54:02,���J�p�����]�m��� (0ms)
2025-06-28 08:54:02,���J���~�ɯŸ�Ƽƶq: 1(1ms)
2025-06-28 08:54:02,���J����W�ѲM���Ƽƶq: 0(2ms)
2025-06-28 08:54:02,���J�}��W�ٰO���ƶq: 3(0ms)
2025-06-28 08:54:02,���J�}�綥�ů�O�O���ƶq: 30(2ms)
2025-06-28 08:54:02,���J�H���}�������Ƽƶq: 0(2ms)
2025-06-28 08:54:02,���JQuest(����)�]�m��Ƽƶq: 44(18ms)
2025-06-28 08:54:03,���J�l��QUEST NPC��Ƽƶq: 594(7ms)
2025-06-28 08:54:03,���J[��g]���y���ȳ]�m��Ƽƶq: 4(0ms)
2025-06-28 08:54:03,�Z�����Ŷˮ`->80
2025-06-28 08:54:03,�±浥�ż��y->16
2025-06-28 08:54:03,���J���ʳ]�m��Ƽƶq: 33(2494ms)
2025-06-28 08:54:03,���J�l��EVENT NPC��Ƽƶq: 0(0ms)
2025-06-28 08:54:03,���JQuest(�ƥ�)�a�ϳ]�m��Ƽƶq: 34(0ms)
2025-06-28 08:54:03,���J�a���m��Ƽƶq: 0(1ms)
2025-06-28 08:54:03,���J���_���i���s���ƶq: 154(12ms)
2025-06-28 08:54:03,���J�Z���B�~�ˮ`��Ƽƶq: 0(0ms)
2025-06-28 08:54:03,���J�����Ƽƶq: 11(1ms)
2025-06-28 08:54:03,���J��ͪ��[��O��Ƽƶq: 160(1ms)
2025-06-28 08:54:03,���J�ݩʪZ����Ƽƶq: 130(2ms)
2025-06-28 08:54:03,���J�D��s�y��Ƽƶq: 93(13ms)
2025-06-28 08:54:03,���J���N������Ƽƶq: 11(3ms)
2025-06-28 08:54:03,���Jw_�Z�����żƾڸ�Ƽƶq: 87(1ms)
2025-06-28 08:54:03,���Jw_���~���żƾڸ�Ƽƶq: 0(0ms)
2025-06-28 08:54:03,���J�����ķҹD���Ƽƶq: 112(1ms)
2025-06-28 08:54:03,com.mchange.v2.c3p0.impl.NewProxyConnection@30eb55c9 [wrapping: null]: close() called after already close()ed or abort()ed.
2025-06-28 08:54:03,com.mchange.v2.c3p0.impl.NewProxyConnection@30eb55c9 [wrapping: null]: close() called after already close()ed or abort()ed.
2025-06-28 08:54:03,���JVIP�D��[�ȼƶq: 100(2ms)
2025-06-28 08:54:03,���J�����NPC���~���~��Ƽƶq: 51(1ms)
2025-06-28 08:54:03,���J�۩w��ܹD��:17
2025-06-28 08:54:03,Ū��->w_Boss�D�԰ƥ��ɸ�Ƽƶq: 38(1ms)
2025-06-28 08:54:03,Ū��->w_�ܨ��d����O�զX�M�d�t��: 10(1ms)
2025-06-28 08:54:03,Ū��->w_�ܨ��d����O�n�J: 64(0ms)
2025-06-28 08:54:03,���J���q��O��Ƽƶq: 4(1ms)
2025-06-28 08:54:05,com.mchange.v2.c3p0.impl.NewPooledConnection@19003b5f handling a throwable.
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:451)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:342)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 08:54:05,Attempted to convert SQLException to SQLException. Leaving it alone. [SQLState: null; errorCode: 0]
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:451)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:342)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 08:54:05,Testing a Connection in response to an Exception:
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:451)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:342)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 08:54:05,Data type BLOB cannot be decoded as Integer
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:451)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:342)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 08:54:05,���J�a�Ϧ^��^�]:148
2025-06-28 08:54:05,���J�a�Ͻd��^��^�]:1
2025-06-28 08:54:05,���J�a�ϭ���]�w:0
2025-06-28 08:54:05,���J�a�ϭ���D��]�w:0
2025-06-28 08:54:05,���J���a�g�@�W��:0
2025-06-28 08:54:05,com.mchange.v2.c3p0.impl.NewProxyConnection@5c8e600b [wrapping: org.mariadb.jdbc.Connection@25b3fcfb]: close() called more than once.
2025-06-28 08:54:05,���J��͸g��:49
2025-06-28 08:54:05,���J�a�ϸs�ճ]�m��� (�J���ɶ�����) �ƶq: 6(1ms)
2025-06-28 08:54:05,���J�a�ϤJ���ɶ�������Ƽƶq: 0(0ms)
2025-06-28 08:54:05,���J�����Ǫ�����:0
2025-06-28 08:54:05,Ū��->w_�Ǫ������t�θ��: 16(1ms)
2025-06-28 08:54:05,���J�����ƶq����D��: 0(0ms)
2025-06-28 08:54:05,�ɯŦ۾ǧޯ�->7
2025-06-28 08:54:05,com.mchange.v2.c3p0.impl.NewProxyStatement@3700994c [wrapping: org.mariadb.jdbc.Statement@3c4c7e51] closed orphaned ResultSet: com.mchange.v2.c3p0.impl.NewProxyResultSet@349aeec4 [wrapping: null]
2025-06-28 08:54:05,Ū��->w_����_����]�w�ƶq: 161(3ms)
2025-06-28 08:54:05,Ū��->�H�����~�S���ݩʼƶq: 0(1ms)
2025-06-28 08:54:05,Ū��->�P���t�θ�Ƽƶq: 0(0ms)
2025-06-28 08:54:05,���J���q�C���]�m��Ƽƶq: 2(1ms)
2025-06-28 08:54:05,���J�����󪫫~���~��Ƽƶq: 30(0ms)
2025-06-28 08:54:05,���J���Q�C���ƾڳ]�m��Ƽƶq: 1(0ms)
2025-06-28 08:54:05,���J�a�Ϭ��y�ƾڳ]�m��Ƽƶq: 2(1ms)
2025-06-28 08:54:05,Ū��->�Z���]�kDIY��Ƽƶq: 106(1ms)
2025-06-28 08:54:05,Ū��->�Z���]�k�ϥδ�����Ƽƶq: 0(0ms)
2025-06-28 08:54:05,���J�i�����ĪG�D��ƶq: 9
2025-06-28 08:54:05,Ū��->w_���~�ĦXdb�Ƹ�Ƽƶq: 0(0ms)
2025-06-28 08:54:05,Ū��->�쫽�����ݩʼƶq: 33(1ms)
2025-06-28 08:54:05,Ū��->�F�������ݩʼƶq: 33(1ms)
2025-06-28 08:54:05,Ū��->�����ǯ����ݩʼƶq: 33(1ms)
2025-06-28 08:54:05,Ū��->���樽�������ݩʼƶq: 33(1ms)
2025-06-28 08:54:05,Ū��->����į����ݩʼƶq: 0(0ms)
2025-06-28 08:54:05,[D] ServerExecutor �}�l��ť�A�Ⱥݤf:(2000)
2025-06-28 08:54:05,��ť�ݤf���m�@�~�Ұ� ���j�ɶ�:360�����C
2025-06-28 08:54:05,

--------------------------------------------------

       �i�ثe����:3.81�j

       �i�֤ߨ����:�L����j

       �i�֤߿�X:Kevin�j

       �i�������@: DKWR - Core �j

       �i��l�}�o�ζ�:�饻�j

       �i���L�I�v�δc�N��ŧ�B�y���L�C�����e���D�A�Y���p�P���ݥ��X�I�j

--------------------------------------------------
2025-06-28 08:54:05,

--------------------------------------------------
       �Ұʫ��O������ť��!!�C�����A���Ұʧ���!!
       �A�Ⱦ��Ұʯӥήɶ�: 7096ms

--------------------------------------------------
2025-06-28 08:54:05,�w���t���s�ϥζq: 595/27305mb
2025-06-28 08:54:05,�D���t���s�ϥζq: 53/0mb
2025-06-28 08:54:13,���J�]�m�T�w�ɶ����(NowTimeSpawn): 23(0ms)
2025-06-28 09:00:11,�H��/���~ ��ƪ��s�� - �����֤߫e�s�u�b���ƶq: 0
2025-06-28 09:00:11,���b�������a�s�u��...
2025-06-28 09:00:14,�Z���֤ߧ������� : 5��!
2025-06-28 09:00:15,�Z���֤ߧ������� : 4��!
2025-06-28 09:00:16,�Z���֤ߧ������� : 3��!
2025-06-28 09:00:17,�Z���֤ߧ������� : 2��!
2025-06-28 09:00:18,�Z���֤ߧ������� : 1��!
2025-06-28 09:00:19,�֤������ݾl�s�u�b���ƶq: 0
