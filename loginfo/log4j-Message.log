2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,���J�ө��c���Ƽƶq: 107(682ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���J�ӫ��c�檫�~��Ƽƶq: 2(0ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���J�a�Ϥ����I�]�m�ƶq: 1505(1ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�a�Ϥ����I�]�m(�h�I)�ƶq: 10(0ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�l��NPC��Ƽƶq: 779/779(18ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J����ܮw����M���Ƽƶq: 2/4(0ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�����Ƹ�Ƽƶq: 6(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�������ɬ�����Ƽƶq: 3(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J������Ƽƶq: 8(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�^���y�и�Ƽƶq: 281(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���J����Ƽƶq: 594(2ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���J�ޯ�Z����Ƽƶq: 3(2ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�ޯ�Z����O��Ƽƶq: 44(5ms)
2025-06-28 10:13:17,���J�ޯ�Z���]�m��Ƽƶq: 119(5ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�^���y�г]�m�ƶq: 343(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�d���رڸ�Ƽƶq: 39(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�d���D���Ƽƶq: 18(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,���J�c�l�}�X���]�m: 89/886(1ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�c�l�}�X���]�m(�h��): 77/247(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,���J�c�l�}�X���]�m(���w�ϥΪ��~�}��): 0/0(0ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J���Ѫ��~�]�m��Ƽƶq: 281(0ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���JNPC�ǰe�I�]�m�ƶq: 8(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,���J�ɶ��a�ϳ]�m�ƶq: 0(1ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�ζ��a�ϳ]�m�ƶq: 0(1ms)
2025-06-28 10:13:17,���J�x��ǰe�I�]�m�ƶq: 0(1ms)
2025-06-28 10:13:17,���JNPC�|�ܸ�Ƽƶq: 26(0ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�M�˳]�m�ƶq: 70(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�M�ˮĪG�Ʀr�}�C: 22ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Ū��=>�ǰe���b�ǰe�I�w�q�ƶq: 284(1ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,���J���~�ɯŸ�Ƽƶq: 393(1ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�޲z�̩R�O�ƶq: 102(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3ff7c4a7] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3ff7c4a7] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�s�⪫�~��Ƽƶq: 1/32(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�������D��: 3(2ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J����ˤͰө�������~��Ƽƶq: 0(0ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���J����������D��: 0(0ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���J����������D��: 1(1ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���JBOSS�l���Ƽƶq: 42(5ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J���θ�Ƽƶq: 129(2ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3ff7c4a7] on CHECKIN.
2025-06-28 10:13:17,���J�T��n�JIP��Ƽƶq: 0(0ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3ff7c4a7] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�T��n�JNAME��Ƽƶq: 0(0ms)
2025-06-28 10:13:17,���J������Ƽƶq: 10(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���J�H���Ƽƶq: 0(0ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J���Ω�椽�i���Ƽƶq: 62(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���J�G�i���Ƽƶq: 0(0ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�O�d�ޯ������Ƽƶq: 85(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���J�H���ޯ������Ƽƶq: 85(2ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�H���ֳt�������Ƽƶq: 114(3ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�H���n�ͬ�����Ƽƶq: 1(0ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���J�O�Юy�Ь�����Ƽƶq: 15(1ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���J�H���O�Юy�вM���Ƽƶq: 15(1ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�B�~������Ƽƶq: 144(2ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�B�~������Ƽƶq: 144(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�B�~������Ƽƶq: 144(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,���J�B�~������Ƽƶq: 0(1ms)
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,>>>>>>>>(�C������)������Ȧ���^�m�M�z�����C
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3ff7c4a7] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3ff7c4a7] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�H�����Ȭ�����Ƽƶq: 95(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,���J�T��W�ټƶq: 1320
2025-06-28 10:13:17,���J���[�]�m��Ƽƶq: 595(1ms)
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,���J�H���I�]����M���Ƽƶq: 144/7944(40ms)
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:18,���J�b���ܮw����M���Ƽƶq: 31/406(4ms)
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:18,���J���F�ܮw����M���Ƽƶq: 0/0(1ms)
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:18,���J����M�ݭܮw����M���Ƽƶq: 1/1(0ms)
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,���J������O��Ƽƶq: 303(12ms)
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,���J������O��Ƽƶq: 0(1ms)
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,���J�d����Ƽƶq: 3(1ms)
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,���J���~�ϥδ�����Ƽƶq: 54(0ms)
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:18,���J�_�����������Ƽƶq: 1409(3ms)
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,���J�U��D���Ƽƶq: 0/0(0ms)
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,���J�T���檫�~��Ƽƶq: 2(1ms)
2025-06-28 10:13:18,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:18,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:18,���J�������y���~: 1(0ms)
2025-06-28 10:13:19,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:19,���J�p�����]�m��� (1ms)
2025-06-28 10:13:19,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:19,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:19,���J���~�ɯŸ�Ƽƶq: 1(1ms)
2025-06-28 10:13:19,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:19,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:19,���J����W�ѲM���Ƽƶq: 579(2ms)
2025-06-28 10:13:19,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:19,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:19,���J�}��W�ٰO���ƶq: 3(0ms)
2025-06-28 10:13:19,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:19,���J�}�綥�ů�O�O���ƶq: 30(2ms)
2025-06-28 10:13:19,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:19,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:19,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:19,���J�H���}�������Ƽƶq: 0(0ms)
2025-06-28 10:13:19,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:19,���JQuest(����)�]�m��Ƽƶq: 44(14ms)
2025-06-28 10:13:19,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:19,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,���J�l��QUEST NPC��Ƽƶq: 594(11ms)
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,���J[��g]���y���ȳ]�m��Ƽƶq: 4(1ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,�Z�����Ŷˮ`->80
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,�±浥�ż��y->16
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,���J���ʳ]�m��Ƽƶq: 33(2478ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,���J�l��EVENT NPC��Ƽƶq: 0(0ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,���JQuest(�ƥ�)�a�ϳ]�m��Ƽƶq: 34(0ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,���J�a���m��Ƽƶq: 0(1ms)
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,���J���_���i���s���ƶq: 154(90ms)
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,���J�Z���B�~�ˮ`��Ƽƶq: 0(0ms)
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,���J�����Ƽƶq: 11(0ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,���J��ͪ��[��O��Ƽƶq: 160(1ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,���J�ݩʪZ����Ƽƶq: 130(1ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,���J�D��s�y��Ƽƶq: 93(18ms)
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@61eff81d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:20,���J���N������Ƽƶq: 11(3ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,���Jw_�Z�����żƾڸ�Ƽƶq: 87(0ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,���Jw_���~���żƾڸ�Ƽƶq: 0(0ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,���J�����ķҹD���Ƽƶq: 112(1ms)
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,com.mchange.v2.c3p0.impl.NewProxyConnection@87fc0fc [wrapping: null]: close() called after already close()ed or abort()ed.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,com.mchange.v2.c3p0.impl.NewProxyConnection@87fc0fc [wrapping: null]: close() called after already close()ed or abort()ed.
2025-06-28 10:13:20,���JVIP�D��[�ȼƶq: 100(2ms)
2025-06-28 10:13:20,���J�����NPC���~���~��Ƽƶq: 51(1ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,���J�۩w��ܹD��:17
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Ū��->w_Boss�D�԰ƥ��ɸ�Ƽƶq: 38(1ms)
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Ū��->w_�ܨ��d����O�զX�M�d�t��: 10(1ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,Ū��->w_�ܨ��d����O�n�J: 64(1ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:20,���J���q��O��Ƽƶq: 4(1ms)
2025-06-28 10:13:20,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:20,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,�Ұʳs�����ʱ��A��
2025-06-28 10:13:22,�D��Ʈw�s�������A - �`�s��: 8, ���L: 0, �Ŷ�: 8, �̤j: 50
2025-06-28 10:13:22,�n�J��Ʈw�s�������A - �`�s��: 3, ���L: 0, �Ŷ�: 3, �̤j: 20
2025-06-28 10:13:22,�Ұʳs�������d�ˬd�A��
2025-06-28 10:13:22,�s�����ʱ��M���d�ˬd�A�Ȥw�Ұ�
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3ff7c4a7] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3ff7c4a7] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3ff7c4a7] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3ff7c4a7] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8 handling a throwable.
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:451)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:352)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 10:13:22,Attempted to convert SQLException to SQLException. Leaving it alone. [SQLState: null; errorCode: 0]
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:451)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:352)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 10:13:22,Testing a Connection in response to an Exception:
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:451)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:352)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 10:13:22,Data type BLOB cannot be decoded as Integer
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:451)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:352)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,���J�a�Ϧ^��^�]:148
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,���J�a�Ͻd��^��^�]:1
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,���J�a�ϭ���]�w:0
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,���J�a�ϭ���D��]�w:0
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,���J���a�g�@�W��:0
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,���J��͸g��:49
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,���J�a�ϸs�ճ]�m��� (�J���ɶ�����) �ƶq: 6(0ms)
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,���J�a�ϤJ���ɶ�������Ƽƶq: 0(0ms)
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,���J�����Ǫ�����:0
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Ū��->w_�Ǫ������t�θ��: 16(1ms)
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,���J�����ƶq����D��: 0(0ms)
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,�ɯŦ۾ǧޯ�->7
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,com.mchange.v2.c3p0.impl.NewProxyStatement@2e7517aa [wrapping: org.mariadb.jdbc.Statement@18b58c77] closed orphaned ResultSet: com.mchange.v2.c3p0.impl.NewProxyResultSet@42805abe [wrapping: null]
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Ū��->w_����_����]�w�ƶq: 161(1ms)
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:22,Ū��->�H�����~�S���ݩʼƶq: 553(16ms)
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:22,Ū��->�P���t�θ�Ƽƶq: 0(1ms)
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,���J���q�C���]�m��Ƽƶq: 2(1ms)
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,���J�����󪫫~���~��Ƽƶq: 30(0ms)
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,���J���Q�C���ƾڳ]�m��Ƽƶq: 1(0ms)
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,���J�a�Ϭ��y�ƾڳ]�m��Ƽƶq: 2(0ms)
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Ū��->�Z���]�kDIY��Ƽƶq: 106(2ms)
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Ū��->�Z���]�k�ϥδ�����Ƽƶq: 0(0ms)
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,���J�i�����ĪG�D��ƶq: 9
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Ū��->w_���~�ĦXdb�Ƹ�Ƽƶq: 0(0ms)
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Ū��->�쫽�����ݩʼƶq: 33(1ms)
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Ū��->�F�������ݩʼƶq: 33(1ms)
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Ū��->�����ǯ����ݩʼƶq: 33(1ms)
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Ū��->���樽�������ݩʼƶq: 33(0ms)
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:22,Ū��->����į����ݩʼƶq: 0(1ms)
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:22,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:22,[D] ServerExecutor �}�l��ť�A�Ⱥݤf:(2000)
2025-06-28 10:13:23,��ť�ݤf���m�@�~�Ұ� ���j�ɶ�:360�����C
2025-06-28 10:13:23,

--------------------------------------------------

       �i�ثe����:3.81�j

       �i�֤ߨ����:�L����j

       �i�֤߿�X:Kevin�j

       �i�������@: DKWR - Core �j

       �i��l�}�o�ζ�:�饻�j

       �i���L�I�v�δc�N��ŧ�B�y���L�C�����e���D�A�Y���p�P���ݥ��X�I�j

--------------------------------------------------
2025-06-28 10:13:23,

--------------------------------------------------
       �Ұʫ��O������ť��!!�C�����A���Ұʧ���!!
       �A�Ⱦ��Ұʯӥήɶ�: 7621ms

--------------------------------------------------
2025-06-28 10:13:23,�w���t���s�ϥζq: 331/27305mb
2025-06-28 10:13:23,�D���t���s�ϥζq: 52/0mb
2025-06-28 10:13:26,�H��/���~ ��ƪ��s�� - �����֤߫e�s�u�b���ƶq: 0
2025-06-28 10:13:26,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN.
2025-06-28 10:13:26,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:26,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:26,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:26,���b�������a�s�u��...
2025-06-28 10:13:26,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN.
2025-06-28 10:13:26,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@513ebe8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:29,�Z���֤ߧ������� : 5��!
2025-06-28 10:13:30,�Z���֤ߧ������� : 4��!
2025-06-28 10:13:31,���J�]�m�T�w�ɶ����(NowTimeSpawn): 23(0ms)
2025-06-28 10:13:31,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:31,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:31,�Z���֤ߧ������� : 3��!
2025-06-28 10:13:32,�Z���֤ߧ������� : 2��!
2025-06-28 10:13:33,�Z���֤ߧ������� : 1��!
2025-06-28 10:13:34,�֤������ݾl�s�u�b���ƶq: 0
