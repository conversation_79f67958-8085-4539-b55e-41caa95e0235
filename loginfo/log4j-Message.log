2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@267318bd] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入商店販賣資料數量: 107(642ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入商城販賣物品資料數量: 2(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入地圖切換點設置數量: 1505(3ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入地圖切換點設置(多點)數量: 10(1ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入召喚NPC資料數量: 779/779(20ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入血盟倉庫物件清單資料數量: 0/0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入血盟資料資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入盟輝圖檔紀錄資料數量: 0(0ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入城堡資料數量: 8(1ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入回城座標資料數量: 281(0ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入門資料數量: 594(2ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入技能武器資料數量: 3(2ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入技能武器能力資料數量: 44(7ms)
2025-06-28 09:32:10,載入技能武器設置資料數量: 119(7ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入回村座標設置數量: 343(1ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入寵物種族資料數量: 39(0ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入寵物道具資料數量: 18(0ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,載入箱子開出物設置: 89/886(2ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入箱子開出物設置(多種): 77/247(0ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入箱子開出物設置(指定使用物品開啟): 0/0(1ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,載入溶解物品設置資料數量: 281(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入NPC傳送點設置數量: 8(1ms)
2025-06-28 09:32:10,載入時間地圖設置數量: 0(1ms)
2025-06-28 09:32:10,載入團隊地圖設置數量: 0(1ms)
2025-06-28 09:32:10,載入官方傳送點設置數量: 0(1ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入NPC會話資料數量: 26(0ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入套裝設置數量: 70(1ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入套裝效果數字陣列: 25ms)
2025-06-28 09:32:10,讀取=>傳送捲軸傳送點定義數量: 284(1ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入物品升級資料數量: 393(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入管理者命令數量: 102(1ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入新手物品資料數量: 1/32(0ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,載入交易限制道具: 3(1ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入限制親友商店交易物品資料數量: 0(0ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入掛機限制掉落道具: 0(0ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入掛機限制掉落道具: 1(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入BOSS召喚資料數量: 42(5ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入盟屋資料數量: 129(3ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN.
2025-06-28 09:32:10,載入禁止登入IP資料數量: 0(0ms)
2025-06-28 09:32:10,載入禁止登入NAME資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入村莊資料數量: 10(1ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入信件資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入盟屋拍賣公告欄資料數量: 62(2ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入佈告欄資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入保留技能紀錄資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入人物技能紀錄資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入人物快速鍵紀錄資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入人物好友紀錄資料數量: 0(1ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入記憶座標紀錄資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入人物記憶座標清單資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入額外紀錄資料數量: 0(1ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入額外紀錄資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入額外紀錄資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入額外紀錄資料數量: 0(1ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,>>>>>>>>(每次重啟)角色任務血盟貢獻清理完畢。
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入人物任務紀錄資料數量: 0(1ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入禁止名稱數量: 1320
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入景觀設置資料數量: 595(1ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入人物背包物件清單資料數量: 0/0(1ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入帳號倉庫物件清單資料數量: 0/0(1ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入精靈倉庫物件清單資料數量: 0/0(1ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入角色專屬倉庫物件清單資料數量: 0/0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入娃娃能力資料數量: 303(17ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入娃娃能力資料數量: 0(2ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,載入寵物資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:10,載入物品使用期限資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,載入奇岩賭場紀錄資料數量: 0(0ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,載入託售道具資料數量: 0/0(1ms)
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入禁止拍賣物品資料數量: 2(0ms)
2025-06-28 09:32:10,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:10,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:10,載入給予獎勵物品: 1(2ms)
2025-06-28 09:32:11,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:11,載入小瑪莉設置資料 (1ms)
2025-06-28 09:32:11,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:11,載入物品升級資料數量: 1(1ms)
2025-06-28 09:32:11,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:11,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:11,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:11,載入物件凹槽清單資料數量: 0(1ms)
2025-06-28 09:32:11,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:11,載入陣營名稱記錄數量: 3(1ms)
2025-06-28 09:32:11,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:11,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:11,載入陣營階級能力記錄數量: 30(2ms)
2025-06-28 09:32:11,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:11,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:11,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:11,載入人物陣營紀錄資料數量: 0(0ms)
2025-06-28 09:32:11,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:11,載入Quest(任務)設置資料數量: 44(19ms)
2025-06-28 09:32:11,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:11,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,載入召喚QUEST NPC資料數量: 594(5ms)
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,載入[改寫]狩獵任務設置資料數量: 4(1ms)
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,武器等級傷害->80
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,威望等級獎勵->16
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,載入活動設置資料數量: 33(2485ms)
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:12,載入召喚EVENT NPC資料數量: 0(1ms)
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,載入Quest(副本)地圖設置資料數量: 34(1ms)
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:12,載入家具位置資料數量: 0(0ms)
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:12,載入打寶公告物編號數量: 154(19ms)
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:12,載入武器額外傷害資料數量: 0(0ms)
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,載入漁獲資料數量: 11(0ms)
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:12,載入轉生附加能力資料數量: 160(1ms)
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:12,載入屬性武器資料數量: 130(2ms)
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:12,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:12,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4dfe36ca] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:13,載入道具製造資料數量: 93(149ms)
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:13,載入成就收集資料數量: 11(1ms)
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,載入w_武防等級數據資料數量: 87(1ms)
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,載入w_飾品等級數據資料數量: 0(0ms)
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:13,載入火神融煉道具資料數量: 112(1ms)
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:13,com.mchange.v2.c3p0.impl.NewProxyConnection@4d5ea776 [wrapping: null]: close() called after already close()ed or abort()ed.
2025-06-28 09:32:13,com.mchange.v2.c3p0.impl.NewProxyConnection@4d5ea776 [wrapping: null]: close() called after already close()ed or abort()ed.
2025-06-28 09:32:13,載入VIP道具加值數量: 100(2ms)
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,載入限制丟NPC物品物品資料數量: 51(1ms)
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,載入自定對話道具:17
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,讀取->w_Boss挑戰副本檔資料數量: 38(1ms)
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,讀取->w_變身卡片能力組合套卡系統: 10(2ms)
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,讀取->w_變身卡片能力登入: 64(1ms)
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:13,載入祭司能力資料數量: 4(0ms)
2025-06-28 09:32:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,啟動連接池監控服務
2025-06-28 09:32:14,主資料庫連接池狀態 - 總連接: 8, 忙碌: 0, 空閒: 8, 最大: 50
2025-06-28 09:32:14,登入資料庫連接池狀態 - 總連接: 3, 忙碌: 0, 空閒: 3, 最大: 20
2025-06-28 09:32:14,啟動連接池健康檢查服務
2025-06-28 09:32:14,連接池監控和健康檢查服務已啟動
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@23411c8d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814 handling a throwable.
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:451)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:352)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 09:32:14,Attempted to convert SQLException to SQLException. Leaving it alone. [SQLState: null; errorCode: 0]
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:451)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:352)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 09:32:14,Testing a Connection in response to an Exception:
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:451)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:352)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 09:32:14,Data type BLOB cannot be decoded as Integer
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:451)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:352)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,載入地圖回血回魔:148
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,載入地圖範圍回血回魔:1
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,載入地圖限制設定:0
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,載入地圖限制道具設定:0
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,載入玩家懲罰名單:0
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,載入轉生經驗:49
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,載入地圖群組設置資料 (入場時間限制) 數量: 6(0ms)
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,載入地圖入場時間紀錄資料數量: 0(0ms)
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,載入全部怪物掉落:0
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,讀取->w_怪物擊殺系統資料: 16(0ms)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,載入掉落數量限制道具: 0(1ms)
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,升級自學技能->7
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,com.mchange.v2.c3p0.impl.NewProxyStatement@361f1647 [wrapping: org.mariadb.jdbc.Statement@36eb8e07] closed orphaned ResultSet: com.mchange.v2.c3p0.impl.NewProxyResultSet@39ac8c0c [wrapping: null]
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,讀取->w_炫色_素質設定數量: 161(1ms)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,讀取->人物物品特殊屬性數量: 0(0ms)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:14,讀取->同盟系統資料數量: 0(1ms)
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,載入挖礦遊戲設置資料數量: 2(1ms)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@3a0cd8eb] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,載入限制丟棄物品物品資料數量: 30(0ms)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,載入拉霸遊戲數據設置資料數量: 1(0ms)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,載入地圖狩獵數據設置資料數量: 2(0ms)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,讀取->武器魔法DIY資料數量: 106(1ms)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,讀取->武器魔法使用期限資料數量: 0(0ms)
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,載入進階版效果道具數量: 9
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,讀取->w_物品融合db化資料數量: 0(1ms)
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,讀取->伊娃紋樣屬性數量: 33(1ms)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,讀取->沙哈紋樣屬性數量: 33(1ms)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,讀取->馬普勒紋樣屬性數量: 33(2ms)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,讀取->帕格里奧紋樣屬性數量: 33(1ms)
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN.
2025-06-28 09:32:14,讀取->殷海薩紋樣屬性數量: 0(0ms)
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4fec2f7a] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:14,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:14,[D] ServerExecutor 開始監聽服務端口:(2000)
2025-06-28 09:32:15,監聽端口重置作業啟動 間隔時間:360分鐘。
2025-06-28 09:32:15,

--------------------------------------------------

       【目前版本:3.81】

       【核心到期日:無限制】

       【核心輸出:Kevin】

       【版本維護: DKWR - Core 】

       【原始開發團隊:日本】

       【絕無侵權或惡意抄襲、篡改其他遊戲內容問題，若有雷同純屬巧合！】

--------------------------------------------------
2025-06-28 09:32:15,

--------------------------------------------------
       啟動指令視窗監聽器!!遊戲伺服器啟動完成!!
       服務器啟動耗用時間: 7448ms

--------------------------------------------------
2025-06-28 09:32:15,已分配內存使用量: 243/910mb
2025-06-28 09:32:15,非分配內存使用量: 55/0mb
2025-06-28 09:32:23,載入設置固定時間放怪(NowTimeSpawn): 23(1ms)
2025-06-28 09:32:23,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:23,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:28,人物/物品 資料的存檔 - 關閉核心前連線帳號數量: 0
2025-06-28 09:32:28,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN.
2025-06-28 09:32:28,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:28,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN.
2025-06-28 09:32:28,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5310e57d] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:28,正在關閉玩家連線中...
2025-06-28 09:32:28,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN.
2025-06-28 09:32:28,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c543814] on CHECKIN has SUCCEEDED.
2025-06-28 09:32:31,距離核心完全關閉 : 5秒!
2025-06-28 09:32:32,距離核心完全關閉 : 4秒!
2025-06-28 09:32:33,距離核心完全關閉 : 3秒!
2025-06-28 09:32:34,距離核心完全關閉 : 2秒!
2025-06-28 09:32:35,距離核心完全關閉 : 1秒!
2025-06-28 09:32:36,核心關閉殘餘連線帳號數量: 0
