2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,���J�ө��c���Ƽƶq: 107(648ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�ӫ��c�檫�~��Ƽƶq: 2(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,���J�a�Ϥ����I�]�m�ƶq: 1505(2ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�a�Ϥ����I�]�m(�h�I)�ƶq: 10(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,���J�l��NPC��Ƽƶq: 779/779(18ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J����ܮw����M���Ƽƶq: 2/4(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�����Ƹ�Ƽƶq: 6(2ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,���J�������ɬ�����Ƽƶq: 3(1ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J������Ƽƶq: 8(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�^���y�и�Ƽƶq: 281(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,���J����Ƽƶq: 594(2ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�ޯ�Z����Ƽƶq: 3(2ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�ޯ�Z����O��Ƽƶq: 44(6ms)
2025-06-28 10:06:36,���J�ޯ�Z���]�m��Ƽƶq: 119(6ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,���J�^���y�г]�m�ƶq: 343(2ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�d���رڸ�Ƽƶq: 39(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�d���D���Ƽƶq: 18(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�c�l�}�X���]�m: 89/886(2ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�c�l�}�X���]�m(�h��): 77/247(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,���J�c�l�}�X���]�m(���w�ϥΪ��~�}��): 0/0(0ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J���Ѫ��~�]�m��Ƽƶq: 281(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���JNPC�ǰe�I�]�m�ƶq: 8(0ms)
2025-06-28 10:06:36,���J�ɶ��a�ϳ]�m�ƶq: 0(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,���J�ζ��a�ϳ]�m�ƶq: 0(1ms)
2025-06-28 10:06:36,���J�x��ǰe�I�]�m�ƶq: 0(1ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���JNPC�|�ܸ�Ƽƶq: 26(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�M�˳]�m�ƶq: 70(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�M�ˮĪG�Ʀr�}�C: 20ms)
2025-06-28 10:06:36,Ū��=>�ǰe���b�ǰe�I�w�q�ƶq: 284(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J���~�ɯŸ�Ƽƶq: 393(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�޲z�̩R�O�ƶq: 102(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@798cad44] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@798cad44] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�s�⪫�~��Ƽƶq: 1/32(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�������D��: 3(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J����ˤͰө�������~��Ƽƶq: 0(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J����������D��: 0(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J����������D��: 1(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,���JBOSS�l���Ƽƶq: 42(5ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,���J���θ�Ƽƶq: 129(3ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@798cad44] on CHECKIN.
2025-06-28 10:06:36,���J�T��n�JIP��Ƽƶq: 0(0ms)
2025-06-28 10:06:36,���J�T��n�JNAME��Ƽƶq: 0(0ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@798cad44] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J������Ƽƶq: 10(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,���J�H���Ƽƶq: 0(0ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J���Ω�椽�i���Ƽƶq: 62(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,���J�G�i���Ƽƶq: 0(1ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�O�d�ޯ������Ƽƶq: 85(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,���J�H���ޯ������Ƽƶq: 85(2ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�H���ֳt�������Ƽƶq: 114(3ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,���J�H���n�ͬ�����Ƽƶq: 1(1ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�O�Юy�Ь�����Ƽƶq: 15(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�H���O�Юy�вM���Ƽƶq: 15(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�B�~������Ƽƶq: 144(2ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�B�~������Ƽƶq: 144(2ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�B�~������Ƽƶq: 144(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�B�~������Ƽƶq: 1(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,>>>>>>>>(�C������)������Ȧ���^�m�M�z�����C
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@798cad44] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@798cad44] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,���J�H�����Ȭ�����Ƽƶq: 95(1ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�T��W�ټƶq: 1320
2025-06-28 10:06:36,���J���[�]�m��Ƽƶq: 595(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�H���I�]����M���Ƽƶq: 144/7944(41ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�b���ܮw����M���Ƽƶq: 31/406(3ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,���J���F�ܮw����M���Ƽƶq: 0/0(0ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J����M�ݭܮw����M���Ƽƶq: 1/1(0ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J������O��Ƽƶq: 303(13ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J������O��Ƽƶq: 0(2ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�d����Ƽƶq: 3(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J���~�ϥδ�����Ƽƶq: 54(1ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,���J�_�����������Ƽƶq: 1409(3ms)
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�U��D���Ƽƶq: 0/0(0ms)
2025-06-28 10:06:36,���J�T���檫�~��Ƽƶq: 2(0ms)
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,���J�������y���~: 1(1ms)
2025-06-28 10:06:38,���J�p�����]�m��� (0ms)
2025-06-28 10:06:38,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:38,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:38,���J���~�ɯŸ�Ƽƶq: 1(0ms)
2025-06-28 10:06:38,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:38,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:38,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:38,���J����W�ѲM���Ƽƶq: 579(2ms)
2025-06-28 10:06:38,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:38,���J�}��W�ٰO���ƶq: 3(1ms)
2025-06-28 10:06:38,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:38,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:38,���J�}�綥�ů�O�O���ƶq: 30(2ms)
2025-06-28 10:06:38,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:38,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:38,���J�H���}�������Ƽƶq: 0(0ms)
2025-06-28 10:06:38,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:38,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:38,���JQuest(����)�]�m��Ƽƶq: 44(14ms)
2025-06-28 10:06:38,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:38,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���J�l��QUEST NPC��Ƽƶq: 594(5ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���J[��g]���y���ȳ]�m��Ƽƶq: 4(1ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,�Z�����Ŷˮ`->80
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,�±浥�ż��y->16
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���J���ʳ]�m��Ƽƶq: 33(2480ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���J�l��EVENT NPC��Ƽƶq: 0(0ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���JQuest(�ƥ�)�a�ϳ]�m��Ƽƶq: 34(0ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:39,���J�a���m��Ƽƶq: 0(0ms)
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@558f0b3b] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���J���_���i���s���ƶq: 154(62ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,���J�Z���B�~�ˮ`��Ƽƶq: 0(0ms)
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,���J�����Ƽƶq: 11(0ms)
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,���J��ͪ��[��O��Ƽƶq: 160(2ms)
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,���J�ݩʪZ����Ƽƶq: 130(1ms)
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���J�D��s�y��Ƽƶq: 93(18ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@b9a3509] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���J���N������Ƽƶq: 11(2ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���Jw_�Z�����żƾڸ�Ƽƶq: 87(1ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���Jw_���~���żƾڸ�Ƽƶq: 0(0ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���J�����ķҹD���Ƽƶq: 112(1ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,���JVIP�D��[�ȼƶq: 100(1ms)
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���J�����NPC���~���~��Ƽƶq: 51(0ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,���J�۩w��ܹD��:17
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Ū��->w_Boss�D�԰ƥ��ɸ�Ƽƶq: 38(0ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Ū��->w_�ܨ��d����O�զX�M�d�t��: 10(0ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,Ū��->w_�ܨ��d����O�n�J: 64(2ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:39,���J���q��O��Ƽƶq: 4(0ms)
2025-06-28 10:06:39,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:39,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,�Ұʳs�����ʱ��A��
2025-06-28 10:06:41,�Ұʳs�������d�ˬd�A��
2025-06-28 10:06:41,�D��Ʈw�s�������A - �`�s��: 8, ���L: 0, �Ŷ�: 8, �̤j: 50
2025-06-28 10:06:41,�s�����ʱ��M���d�ˬd�A�Ȥw�Ұ�
2025-06-28 10:06:41,�n�J��Ʈw�s�������A - �`�s��: 3, ���L: 0, �Ŷ�: 3, �̤j: 20
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@798cad44] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@798cad44] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@798cad44] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@798cad44] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Data type BLOB cannot be decoded as Integer
java.sql.SQLDataException: Data type BLOB cannot be decoded as Integer
	at org.mariadb.jdbc.client.column.BlobColumn.decodeIntText(BlobColumn.java:206)
	at org.mariadb.jdbc.client.result.rowdecoder.TextRowDecoder.decodeInt(TextRowDecoder.java:115)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:465)
	at org.mariadb.jdbc.client.result.Result.getInt(Result.java:664)
	at com.mchange.v2.c3p0.impl.NewProxyResultSet.getInt(NewProxyResultSet.java:2417)
	at com.lineage.server.datatables.DeClanTable.load(DeClanTable.java:60)
	at com.lineage.server.GameServer.initialize(GameServer.java:352)
	at com.lineage.Server.main(Server.java:129)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,���J�a�Ϧ^��^�]:148
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,���J�a�Ͻd��^��^�]:1
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,���J�a�ϭ���]�w:0
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,���J�a�ϭ���D��]�w:0
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,���J���a�g�@�W��:0
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,���J��͸g��:49
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,���J�a�ϸs�ճ]�m��� (�J���ɶ�����) �ƶq: 6(0ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,���J�a�ϤJ���ɶ�������Ƽƶq: 0(1ms)
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,���J�����Ǫ�����:0
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Ū��->w_�Ǫ������t�θ��: 16(1ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,���J�����ƶq����D��: 0(0ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,�ɯŦ۾ǧޯ�->7
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,ResultSet org.mariadb.jdbc.client.result.CompleteResult@2ec0ca83 was apparently closed after the Statement that created it had already been closed.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Ū��->w_����_����]�w�ƶq: 161(1ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:41,Ū��->�H�����~�S���ݩʼƶq: 553(17ms)
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Ū��->�P���t�θ�Ƽƶq: 0(1ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,���J���q�C���]�m��Ƽƶq: 2(1ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,���J�����󪫫~���~��Ƽƶq: 30(0ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,���J���Q�C���ƾڳ]�m��Ƽƶq: 1(0ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,���J�a�Ϭ��y�ƾڳ]�m��Ƽƶq: 2(0ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Ū��->�Z���]�kDIY��Ƽƶq: 106(1ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Ū��->�Z���]�k�ϥδ�����Ƽƶq: 0(0ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,���J�i�����ĪG�D��ƶq: 9
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Ū��->w_���~�ĦXdb�Ƹ�Ƽƶq: 0(0ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Ū��->�쫽�����ݩʼƶq: 33(1ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Ū��->�F�������ݩʼƶq: 33(1ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Ū��->�����ǯ����ݩʼƶq: 33(1ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Ū��->���樽�������ݩʼƶq: 33(1ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Ū��->����į����ݩʼƶq: 0(0ms)
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:41,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:41,[D] ServerExecutor �}�l��ť�A�Ⱥݤf:(2000)
2025-06-28 10:06:41,��ť�ݤf���m�@�~�Ұ� ���j�ɶ�:360�����C
2025-06-28 10:06:41,

--------------------------------------------------

       �i�ثe����:3.81�j

       �i�֤ߨ����:�L����j

       �i�֤߿�X:Kevin�j

       �i�������@: DKWR - Core �j

       �i��l�}�o�ζ�:�饻�j

       �i���L�I�v�δc�N��ŧ�B�y���L�C�����e���D�A�Y���p�P���ݥ��X�I�j

--------------------------------------------------
2025-06-28 10:06:41,

--------------------------------------------------
       �Ұʫ��O������ť��!!�C�����A���Ұʧ���!!
       �A�Ⱦ��Ұʯӥήɶ�: 7454ms

--------------------------------------------------
2025-06-28 10:06:41,�w���t���s�ϥζq: 633/27305mb
2025-06-28 10:06:41,�D���t���s�ϥζq: 52/0mb
2025-06-28 10:06:50,���J�]�m�T�w�ɶ����(NowTimeSpawn): 23(0ms)
2025-06-28 10:06:50,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:50,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:07:13,�H��/���~ ��ƪ��s�� - �����֤߫e�s�u�b���ƶq: 0
2025-06-28 10:07:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN.
2025-06-28 10:07:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN has SUCCEEDED.
2025-06-28 10:07:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:07:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:07:13,���b�������a�s�u��...
2025-06-28 10:07:13,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN.
2025-06-28 10:07:13,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@53f6abf3] on CHECKIN has SUCCEEDED.
2025-06-28 10:07:16,�Z���֤ߧ������� : 5��!
2025-06-28 10:07:17,�Z���֤ߧ������� : 4��!
2025-06-28 10:07:18,�Z���֤ߧ������� : 3��!
2025-06-28 10:07:19,�Z���֤ߧ������� : 2��!
2025-06-28 10:07:20,�Z���֤ߧ������� : 1��!
2025-06-28 10:07:21,�֤������ݾl�s�u�b���ƶq: 0
