2025-06-28 10:13:17,acquire test -- pool size: 5; target_pool_size: 5; desired target? 6
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN.
2025-06-28 10:13:17,awaitAvailable(): com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4b931f30] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@fdf27ff] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@333e4da3] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@7fcb244d] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@4aec46e2] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@1f20b1f8] on CHECKIN has SUCCEEDED.
2025-06-28 10:13:17,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN.
2025-06-28 10:13:17,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5f838cbc] on CHECKIN has SUCCEEDED.
