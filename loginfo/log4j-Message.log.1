2025-06-28 10:06:36,acquire test -- pool size: 5; target_pool_size: 5; desired target? 6
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,acquire test -- pool size: 5; target_pool_size: 8; desired target? 6
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@574e12ba] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@2f705e07] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@765507c2] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN.
2025-06-28 10:06:36,Test of PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@5b88855f] on CHECKIN has SUCCEEDED.
2025-06-28 10:06:36,Testing PooledConnection [com.mchange.v2.c3p0.impl.NewPooledConnection@6c43df72] on CHECKIN.
