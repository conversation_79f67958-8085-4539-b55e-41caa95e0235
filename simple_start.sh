#!/bin/bash

# 簡化的 Lineage 381a 啟動腳本
# 專門解決 Ubuntu 執行問題

echo "=========================================="
echo "Lineage 381a 簡化啟動腳本"
echo "Ubuntu 24.02 + 64GB RAM"
echo "=========================================="

# 設置 Java 環境
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

# 檢查 Java
if ! command -v java &> /dev/null; then
    echo "錯誤: Java 未安裝"
    echo "請執行: sudo apt install openjdk-8-jdk"
    exit 1
fi

echo "Java 版本:"
java -version

# 檢查主類
if [ ! -f "out/production/Lineage381a/com/lineage/Server.class" ]; then
    echo "錯誤: 找不到編譯後的主類"
    echo "請先執行編譯:"
    echo "  ./ubuntu_one_click_fix.sh"
    echo "  或 ./compile_ubuntu.sh"
    exit 1
fi

# 檢查依賴
echo "檢查依賴文件..."
missing=0
for jar in jar/*.jar; do
    if [ ! -f "$jar" ]; then
        echo "缺少: $jar"
        missing=1
    fi
done

if [ $missing -eq 1 ]; then
    echo "錯誤: 缺少依賴文件"
    exit 1
fi

# 設置 Classpath
CLASSPATH="out/production/Lineage381a"
for jar in jar/*.jar; do
    CLASSPATH="$CLASSPATH:$jar"
done

echo "Classpath 設置完成"

# 創建日誌目錄
mkdir -p logs

# 設置 JVM 參數 (64GB RAM 優化)
JVM_OPTS="-Xms16g -Xmx32g"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=100"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Taipei"

echo "JVM 參數: $JVM_OPTS"
echo "=========================================="
echo "啟動 Lineage 伺服器..."
echo "=========================================="

# 啟動伺服器
java $JVM_OPTS -cp "$CLASSPATH" com.lineage.Server

echo "伺服器已退出"
